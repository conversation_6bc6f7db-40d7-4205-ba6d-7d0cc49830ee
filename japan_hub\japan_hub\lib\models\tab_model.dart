import 'package:flutter_inappwebview/flutter_inappwebview.dart';

/// 标签页数据模型
class TabModel {
  final String id;
  String title;
  String url;
  String? favicon;
  bool isLoading;
  InAppWebViewController? controller;
  
  TabModel({
    required this.id,
    this.title = '新建页签',
    this.url = '',
    this.favicon,
    this.isLoading = false,
    this.controller,
  });

  /// 更新标签页信息
  void updateInfo({
    String? title,
    String? url,
    String? favicon,
    bool? isLoading,
    InAppWebViewController? controller,
  }) {
    if (title != null) this.title = title;
    if (url != null) this.url = url;
    if (favicon != null) this.favicon = favicon;
    if (isLoading != null) this.isLoading = isLoading;
    if (controller != null) this.controller = controller;
  }

  /// 获取显示标题
  String get displayTitle {
    if (title.isNotEmpty && title != '新建页签') {
      return title;
    }
    if (url.isNotEmpty) {
      try {
        final uri = Uri.parse(url);
        return uri.host;
      } catch (e) {
        return url;
      }
    }
    return '新建页签';
  }

  /// 获取简短URL
  String get shortUrl {
    if (url.isEmpty) return '';
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return url;
    }
  }

  @override
  String toString() {
    return 'TabModel(id: $id, title: $title, url: $url, isLoading: $isLoading)';
  }
}
