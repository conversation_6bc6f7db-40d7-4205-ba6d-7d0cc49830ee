# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 50ms
  [gap of 30ms]
generate_cxx_metadata completed in 101ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 45ms
  [gap of 29ms]
generate_cxx_metadata completed in 92ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 67ms
  [gap of 23ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 134ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 49ms
  [gap of 28ms]
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 45ms
  [gap of 27ms]
generate_cxx_metadata completed in 90ms

