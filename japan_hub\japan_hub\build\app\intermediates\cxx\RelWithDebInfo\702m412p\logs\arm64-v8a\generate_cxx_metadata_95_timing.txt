# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 66ms
  [gap of 32ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 131ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 58ms
  [gap of 14ms]
  write-metadata-json-to-file 21ms
generate_cxx_metadata completed in 122ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 32ms]
  create-invalidation-state 89ms
  [gap of 33ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 169ms

