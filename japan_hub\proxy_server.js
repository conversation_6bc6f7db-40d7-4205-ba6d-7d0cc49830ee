// 简单的CORS代理服务器
const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = 3001;

// 启用CORS
app.use(cors());

// 代理中间件
app.use('/proxy', createProxyMiddleware({
  target: 'https://example.com', // 会被动态替换
  changeOrigin: true,
  pathRewrite: {
    '^/proxy': '', // 移除/proxy前缀
  },
  router: (req) => {
    // 从查询参数中获取目标URL
    const targetUrl = req.query.url;
    if (targetUrl) {
      return targetUrl;
    }
    return 'https://example.com';
  },
  onProxyReq: (proxyReq, req, res) => {
    // 移除可能导致问题的头部
    proxyReq.removeHeader('X-Frame-Options');
    proxyReq.removeHeader('Content-Security-Policy');
  },
  onProxyRes: (proxyRes, req, res) => {
    // 移除阻止iframe的响应头
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    
    // 添加CORS头
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,PUT,POST,DELETE,OPTIONS';
    proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization';
  }
}));

app.listen(PORT, () => {
  console.log(`代理服务器运行在 http://localhost:${PORT}`);
  console.log(`使用方式: http://localhost:${PORT}/proxy?url=https://目标网站.com`);
});
