{"buildFiles": ["D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Scoop\\apps\\android-clt\\current\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\lk\\japan_hub\\japan_hub\\build\\.cxx\\RelWithDebInfo\\702m412p\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Scoop\\apps\\android-clt\\current\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\lk\\japan_hub\\japan_hub\\build\\.cxx\\RelWithDebInfo\\702m412p\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Scoop\\apps\\android-clt\\current\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Scoop\\apps\\android-clt\\current\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}