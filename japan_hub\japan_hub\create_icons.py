#!/usr/bin/env python3
"""
创建NACG翻译君的Android图标
N字稍微靠上10%
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """创建指定尺寸的N字图标"""
    # 1. 创建方形带圆角的蓝色背景
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # 1. 绘制方形带圆角的蓝色背景（和开屏一样的蓝色）
    margin = size // 20  # 留一点边距
    corner_radius = size // 8  # 圆角半径
    draw.rounded_rectangle([margin, margin, size-margin, size-margin],
                          radius=corner_radius, fill='#1E88E5')  # 1. 使用开屏一样的蓝色
    
    # 计算N字的位置和大小
    font_size = int(size * 0.5)  # N字占图标的50%
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
        except:
            # 如果找不到字体，使用默认字体
            font = ImageFont.load_default()
    
    # 获取N字的尺寸
    bbox = draw.textbbox((0, 0), "N", font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # 2. 计算N字位置（居中，但向上10%）
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - int(size * 0.1)  # 2. 向上10%
    
    # 绘制白色N字
    draw.text((x, y), "N", fill='white', font=font)
    
    # 保存图标
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    img.save(output_path, 'PNG')
    print(f"Created: {output_path}")

def main():
    """创建所有尺寸的图标"""
    base_path = "android/app/src/main/res"
    
    # Android图标尺寸
    sizes = {
        'mipmap-mdpi': 48,
        'mipmap-hdpi': 72,
        'mipmap-xhdpi': 96,
        'mipmap-xxhdpi': 144,
        'mipmap-xxxhdpi': 192,
    }
    
    for folder, size in sizes.items():
        output_path = f"{base_path}/{folder}/ic_launcher.png"
        create_icon(size, output_path)
    
    print("所有图标创建完成！")

if __name__ == "__main__":
    main()
