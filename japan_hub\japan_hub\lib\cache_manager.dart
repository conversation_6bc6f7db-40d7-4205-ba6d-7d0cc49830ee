import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:crypto/crypto.dart';

class CacheManager {
  static final CacheManager _instance = CacheManager._internal();
  factory CacheManager() => _instance;
  CacheManager._internal();

  static const String _cachePrefix = 'japan_hub_cache_';
  static const String _timestampPrefix = 'japan_hub_timestamp_';
  static const String _hashPrefix = 'japan_hub_hash_';
  static const String _contentPrefix = 'japan_hub_content_';
  static const Duration _cacheExpiry = Duration(days: 30); // 30天长期缓存

  // 预加载网站列表（深度预加载前12个）
  Future<void> preloadWebsites(List<String> urls) async {
    // 限制为前12个网站进行深度预加载
    final urlsToPreload = urls.take(12).toList();

    for (int i = 0; i < urlsToPreload.length; i++) {
      final url = urlsToPreload[i];
      try {
        await _deepPreloadWebsite(url, depth: 2);
        // 添加延迟避免过于频繁的请求
        await Future.delayed(const Duration(milliseconds: 1000));
      } catch (e) {
        print('预加载网站失败: $url, 错误: $e');
      }
    }
  }

  // 深度预加载单个网站
  Future<void> _deepPreloadWebsite(String url, {int depth = 2}) async {
    if (depth <= 0) return;

    try {
      // 1. 预加载主页面
      final mainPageContent = await _preloadSingleWebsiteWithContent(url);

      // 2. 如果成功获取主页面，提取链接进行深度预加载
      if (mainPageContent != null && depth > 1) {
        final links = _extractLinksFromHtml(mainPageContent, url);

        // 限制每个页面最多预加载5个子链接
        final linksToPreload = links.take(5).toList();

        for (final link in linksToPreload) {
          try {
            await _deepPreloadWebsite(link, depth: depth - 1);
            // 添加延迟避免过于频繁的请求
            await Future.delayed(const Duration(milliseconds: 500));
          } catch (e) {
            // 忽略子链接预加载失败
          }
        }
      }
    } catch (e) {
      print('深度预加载网站失败: $url, 错误: $e');
    }
  }

  // 预加载单个网站并返回内容
  Future<String?> _preloadSingleWebsiteWithContent(String url) async {
    try {
      // 检查缓存是否存在且未过期
      if (await _isCacheValid(url)) {
        return await getCachedPageContent(url);
      }

      // 获取网站内容
      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
              'Accept':
                  'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
              'Accept-Language': 'ja,en-US;q=0.5',
              'Accept-Encoding': 'gzip, deflate',
              'Connection': 'keep-alive',
            },
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final content = response.body;

        // 缓存内容
        await _cachePageContent(url, content);

        return content;
      }
    } catch (e) {
      print('预加载网站失败: $url, 错误: $e');
    }

    return null;
  }

  // 从HTML中提取链接
  List<String> _extractLinksFromHtml(String html, String baseUrl) {
    final links = <String>[];
    final uri = Uri.parse(baseUrl);

    // 简单的正则表达式提取href链接
    final linkRegex = RegExp(
      r'href=["' + "'" + r'](.*?)["' + "'" + r']',
      caseSensitive: false,
    );
    final matches = linkRegex.allMatches(html);

    for (final match in matches) {
      final href = match.group(1);
      if (href != null && href.isNotEmpty) {
        try {
          // 处理相对链接和绝对链接
          Uri linkUri;
          if (href.startsWith('http://') || href.startsWith('https://')) {
            linkUri = Uri.parse(href);
          } else if (href.startsWith('/')) {
            linkUri = Uri(
              scheme: uri.scheme,
              host: uri.host,
              port: uri.port,
              path: href,
            );
          } else {
            continue; // 跳过其他类型的链接
          }

          // 只预加载同域名的链接，且不是锚点链接
          if (linkUri.host == uri.host && !href.contains('#')) {
            links.add(linkUri.toString());
          }
        } catch (e) {
          // 忽略无效的链接
        }
      }
    }

    return links;
  }

  // 预加载单个网站
  Future<void> _preloadSingleWebsite(String url) async {
    try {
      // 检查缓存是否存在且未过期
      if (await _isCacheValid(url)) {
        print('缓存有效，跳过预加载: $url');
        return;
      }

      print('开始预加载: $url');

      // 获取网站基本信息
      final response = await http
          .head(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            },
          )
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        // 缓存网站状态
        await _cacheWebsiteStatus(url, 'online');

        // 预加载favicon
        await _preloadFavicon(url);

        print('预加载完成: $url');
      } else {
        await _cacheWebsiteStatus(url, 'offline');
      }
    } catch (e) {
      print('预加载失败: $url - $e');
      await _cacheWebsiteStatus(url, 'error');
    }
  }

  // 预加载网站favicon
  Future<void> _preloadFavicon(String url) async {
    try {
      final uri = Uri.parse(url);
      final faviconUrl = '${uri.scheme}://${uri.host}/favicon.ico';

      // 实际下载favicon内容并缓存
      final response = await http
          .get(Uri.parse(faviconUrl))
          .timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        await _cacheFaviconData(url, faviconUrl, response.bodyBytes);
      }
    } catch (e) {
      print('Favicon预加载失败: $url - $e');
    }
  }

  // 智能预加载相关内容
  Future<void> smartPreload(String websiteUrl, String category) async {
    switch (category) {
      case '小说':
        await _preloadNovelContent(websiteUrl);
        break;
      case '创作':
        await _preloadArtContent(websiteUrl);
        break;
      case '视频':
        await _preloadVideoContent(websiteUrl);
        break;
      case '门户':
        await _preloadPortalContent(websiteUrl);
        break;
      default:
        await _preloadGenericContent(websiteUrl);
    }
  }

  // 预加载小说网站内容
  Future<void> _preloadNovelContent(String url) async {
    try {
      if (url.contains('syosetu.com')) {
        // 预加载小説家になろう的分类页面
        final categories = [
          'https://syosetu.com/rank/list/type/total/',
          'https://syosetu.com/rank/list/type/monthly/',
          'https://syosetu.com/rank/list/type/weekly/',
        ];
        await Future.wait(categories.map((cat) => _preloadPageHeaders(cat)));
      } else if (url.contains('kakuyomu.jp')) {
        // 预加载カクヨム的热门内容
        final pages = [
          'https://kakuyomu.jp/rankings',
          'https://kakuyomu.jp/genres',
        ];
        await Future.wait(pages.map((page) => _preloadPageHeaders(page)));
      }
    } catch (e) {
      print('小说内容预加载失败: $e');
    }
  }

  // 预加载创作网站内容
  Future<void> _preloadArtContent(String url) async {
    try {
      if (url.contains('pixiv.net')) {
        // 预加载pixiv的热门内容
        final pages = [
          'https://www.pixiv.net/ranking.php',
          'https://www.pixiv.net/tags/',
        ];
        await Future.wait(pages.map((page) => _preloadPageHeaders(page)));
      }
    } catch (e) {
      print('创作内容预加载失败: $e');
    }
  }

  // 预加载视频网站内容
  Future<void> _preloadVideoContent(String url) async {
    try {
      if (url.contains('nicovideo.jp')) {
        // 预加载ニコニコ動画的热门内容
        final pages = [
          'https://www.nicovideo.jp/ranking',
          'https://www.nicovideo.jp/genre',
        ];
        await Future.wait(pages.map((page) => _preloadPageHeaders(page)));
      }
    } catch (e) {
      print('视频内容预加载失败: $e');
    }
  }

  // 预加载门户网站内容
  Future<void> _preloadPortalContent(String url) async {
    try {
      if (url.contains('yahoo.co.jp')) {
        // 预加载Yahoo的主要栏目
        final pages = [
          'https://news.yahoo.co.jp/',
          'https://shopping.yahoo.co.jp/',
        ];
        await Future.wait(pages.map((page) => _preloadPageHeaders(page)));
      }
    } catch (e) {
      print('门户内容预加载失败: $e');
    }
  }

  // 预加载通用内容
  Future<void> _preloadGenericContent(String url) async {
    try {
      await _preloadPageHeaders(url);
    } catch (e) {
      print('通用内容预加载失败: $e');
    }
  }

  // 预加载页面头部信息
  Future<void> _preloadPageHeaders(String url) async {
    try {
      await http
          .head(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            },
          )
          .timeout(const Duration(seconds: 3));
    } catch (e) {
      print('页面头部预加载失败: $url - $e');
    }
  }

  // 检查缓存是否有效
  Future<bool> _isCacheValid(String url) async {
    final prefs = await SharedPreferences.getInstance();
    final timestamp = prefs.getInt('$_timestampPrefix$url');

    if (timestamp == null) return false;

    final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    final now = DateTime.now();

    return now.difference(cacheTime) < _cacheExpiry;
  }

  // 缓存网站状态
  Future<void> _cacheWebsiteStatus(String url, String status) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_cachePrefix${url}_status', status);
    await prefs.setInt(
      '$_timestampPrefix$url',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  // 缓存favicon数据
  Future<void> _cacheFaviconData(
    String url,
    String faviconUrl,
    List<int> data,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_cachePrefix${url}_favicon', faviconUrl);
    // 将图标数据转换为base64存储
    final base64Data = base64Encode(data);
    await prefs.setString('$_cachePrefix${url}_favicon_data', base64Data);
  }

  // 缓存favicon（保留兼容性）
  Future<void> _cacheFavicon(String url, String faviconUrl) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_cachePrefix${url}_favicon', faviconUrl);
  }

  // 获取缓存的网站状态
  Future<String?> getCachedWebsiteStatus(String url) async {
    if (!await _isCacheValid(url)) return null;

    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('$_cachePrefix${url}_status');
  }

  // 获取缓存的favicon base64数据
  Future<String?> getCachedFavicon(String url) async {
    if (!await _isCacheValid(url)) return null;

    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('$_cachePrefix${url}_favicon_data');
  }

  // 缓存favicon base64数据
  Future<void> cacheFavicon(String url, String base64Data) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('$_cachePrefix${url}_favicon_data', base64Data);
    await prefs.setInt(
      '$_timestampPrefix$url',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  // 检测内容是否有变化
  Future<bool> hasContentChanged(String url) async {
    try {
      // 获取页面内容的ETag或Last-Modified
      final response = await http
          .head(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            },
          )
          .timeout(const Duration(seconds: 5));

      final etag = response.headers['etag'];
      final lastModified = response.headers['last-modified'];

      // 生成内容标识
      final contentId =
          etag ??
          lastModified ??
          DateTime.now().millisecondsSinceEpoch.toString();

      // 检查是否与缓存的内容标识相同
      final prefs = await SharedPreferences.getInstance();
      final cachedContentId = prefs.getString('$_hashPrefix$url');

      if (cachedContentId == contentId) {
        return false; // 内容未变化
      }

      // 更新内容标识
      await prefs.setString('$_hashPrefix$url', contentId);
      return true; // 内容有变化
    } catch (e) {
      return true; // 检测失败，假设有变化
    }
  }

  // 逐级预加载 - 网站首页→列表页→详情页→内容页
  Future<void> progressivePreload(String baseUrl, String category) async {
    switch (category) {
      case '小说':
        await _progressivePreloadNovel(baseUrl);
        break;
      case '创作':
        await _progressivePreloadArt(baseUrl);
        break;
      case '视频':
        await _progressivePreloadVideo(baseUrl);
        break;
      default:
        await _progressivePreloadGeneric(baseUrl);
    }
  }

  // 小说网站逐级预加载
  Future<void> _progressivePreloadNovel(String baseUrl) async {
    if (baseUrl.contains('syosetu.com')) {
      // 第1级：首页
      await _preloadWithChangeDetection('https://syosetu.com/');

      // 第2级：排行榜页面和分类页面
      final rankingPages = [
        'https://syosetu.com/rank/list/type/total/',
        'https://syosetu.com/rank/list/type/monthly/',
        'https://syosetu.com/rank/list/type/weekly/',
        'https://syosetu.com/rank/list/type/daily/',
        'https://syosetu.com/rank/list/type/quarter/',
        'https://syosetu.com/rank/list/type/yearly/',
      ];

      // 预加载热门分类
      final categoryPages = [
        'https://syosetu.com/search/?&order=hyoka&notnizi=1',
        'https://syosetu.com/search/?&order=favnovels&notnizi=1',
        'https://syosetu.com/search/?&order=impressions&notnizi=1',
        'https://syosetu.com/search/?&order=weekly&notnizi=1',
      ];

      // 预加载所有排行榜页面
      for (final page in rankingPages) {
        await _preloadWithChangeDetection(page);

        // 第3级：从排行榜页面提取小说链接并预加载前20个
        await _preloadTopNovelsFromRanking(page, 20);
      }

      // 预加载所有分类页面
      for (final page in categoryPages) {
        await _preloadWithChangeDetection(page);

        // 从分类页面也预加载热门小说
        await _preloadTopNovelsFromRanking(page, 15);
      }
    } else if (baseUrl.contains('kakuyomu.jp')) {
      // カクヨム的逐级预加载
      await _preloadWithChangeDetection('https://kakuyomu.jp/');

      // 预加载多个排行榜
      final kakuyomuRankings = [
        'https://kakuyomu.jp/rankings',
        'https://kakuyomu.jp/rankings/weekly',
        'https://kakuyomu.jp/rankings/monthly',
        'https://kakuyomu.jp/rankings/quarterly',
      ];

      for (final ranking in kakuyomuRankings) {
        await _preloadWithChangeDetection(ranking);
      }

      // 预加载分类页面
      final kakuyomuGenres = [
        'https://kakuyomu.jp/genres/romance',
        'https://kakuyomu.jp/genres/fantasy',
        'https://kakuyomu.jp/genres/mystery',
        'https://kakuyomu.jp/genres/sf',
      ];

      for (final genre in kakuyomuGenres) {
        await _preloadWithChangeDetection(genre);
      }

      await _preloadTopNovelsFromKakuyomu(15);
    }
  }

  // 创作网站逐级预加载
  Future<void> _progressivePreloadArt(String baseUrl) async {
    if (baseUrl.contains('pixiv.net')) {
      // pixiv的逐级预加载
      await _preloadWithChangeDetection('https://www.pixiv.net/');

      // 预加载多个排行榜
      final pixivRankings = [
        'https://www.pixiv.net/ranking.php',
        'https://www.pixiv.net/ranking.php?mode=daily',
        'https://www.pixiv.net/ranking.php?mode=weekly',
        'https://www.pixiv.net/ranking.php?mode=monthly',
        'https://www.pixiv.net/ranking.php?mode=rookie',
        'https://www.pixiv.net/ranking.php?mode=original',
      ];

      for (final ranking in pixivRankings) {
        await _preloadWithChangeDetection(ranking);
      }

      // 预加载热门标签
      final pixivTags = [
        'https://www.pixiv.net/tags/オリジナル/artworks',
        'https://www.pixiv.net/tags/イラスト/artworks',
        'https://www.pixiv.net/tags/漫画/artworks',
        'https://www.pixiv.net/tags/アニメ/artworks',
      ];

      for (final tag in pixivTags) {
        await _preloadWithChangeDetection(tag);
      }

      await _preloadTopArtworksFromPixiv(25);
    }
  }

  // 视频网站逐级预加载
  Future<void> _progressivePreloadVideo(String baseUrl) async {
    if (baseUrl.contains('nicovideo.jp')) {
      // ニコニコ動画的逐级预加载
      await _preloadWithChangeDetection('https://www.nicovideo.jp/');
      await _preloadWithChangeDetection('https://www.nicovideo.jp/ranking');
      await _preloadTopVideosFromNico(20);
    }
  }

  // 通用逐级预加载
  Future<void> _progressivePreloadGeneric(String baseUrl) async {
    await _preloadWithChangeDetection(baseUrl);
  }

  // 带变化检测的预加载
  Future<void> _preloadWithChangeDetection(String url) async {
    try {
      // 检查内容是否有变化
      final hasChanged = await hasContentChanged(url);

      if (!hasChanged) {
        // 内容未变化，使用缓存
        return;
      }

      // 内容有变化，重新加载
      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
            },
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // 缓存页面内容
        await _cachePageContent(url, response.body);
      }
    } catch (e) {
      // 预加载失败不影响正常使用
    }
  }

  // 从排行榜预加载热门小说
  Future<void> _preloadTopNovelsFromRanking(
    String rankingUrl,
    int count,
  ) async {
    try {
      final response = await http
          .get(Uri.parse(rankingUrl))
          .timeout(const Duration(seconds: 10));
      if (response.statusCode == 200) {
        // 这里应该解析HTML提取小说链接，简化处理
        // 实际实现中需要HTML解析库
        final novelUrls = _extractNovelUrls(response.body, count);

        for (final novelUrl in novelUrls) {
          await _preloadWithChangeDetection(novelUrl);

          // 预加载小说的前10章，确保无缝阅读
          final chapterUrls = _getChapterUrls(novelUrl, 10);
          for (final chapterUrl in chapterUrls) {
            await _preloadWithChangeDetection(chapterUrl);
          }

          // 预加载小说的目录页
          final tocUrl = _getTableOfContentsUrl(novelUrl);
          if (tocUrl != null) {
            await _preloadWithChangeDetection(tocUrl);
          }

          // 预加载作者的其他作品
          final authorWorksUrl = _getAuthorWorksUrl(novelUrl);
          if (authorWorksUrl != null) {
            await _preloadWithChangeDetection(authorWorksUrl);
          }
        }
      }
    } catch (e) {
      // 预加载失败不影响正常使用
    }
  }

  // 从カクヨム预加载热门小说
  Future<void> _preloadTopNovelsFromKakuyomu(int count) async {
    // 简化实现，实际需要HTML解析
  }

  // 从pixiv预加载热门作品
  Future<void> _preloadTopArtworksFromPixiv(int count) async {
    // 简化实现，实际需要HTML解析
  }

  // 从ニコニコ動画预加载热门视频
  Future<void> _preloadTopVideosFromNico(int count) async {
    // 简化实现，实际需要HTML解析
  }

  // 提取小说URL（简化实现）
  List<String> _extractNovelUrls(String html, int count) {
    // 这里应该使用HTML解析库提取真实的小说链接
    // 简化返回示例链接
    return [];
  }

  // 获取小说章节URL列表（简化实现）
  List<String> _getChapterUrls(String novelUrl, int count) {
    // 这里应该解析小说页面获取章节链接
    // 简化实现：根据小说URL模式生成章节URL
    List<String> chapterUrls = [];

    if (novelUrl.contains('syosetu.com')) {
      // 小説家になろう的章节URL模式
      final baseUrl = novelUrl.replaceAll('/', '');
      for (int i = 1; i <= count; i++) {
        chapterUrls.add('$baseUrl/$i/');
      }
    } else if (novelUrl.contains('kakuyomu.jp')) {
      // カクヨム的章节URL模式
      for (int i = 1; i <= count; i++) {
        chapterUrls.add('$novelUrl/episodes/$i');
      }
    }

    return chapterUrls;
  }

  // 获取小说目录页URL（简化实现）
  String? _getTableOfContentsUrl(String novelUrl) {
    if (novelUrl.contains('syosetu.com')) {
      return '$novelUrl/';
    } else if (novelUrl.contains('kakuyomu.jp')) {
      return '$novelUrl/episodes';
    }
    return null;
  }

  // 获取作者其他作品URL（简化实现）
  String? _getAuthorWorksUrl(String novelUrl) {
    if (novelUrl.contains('syosetu.com')) {
      // 从小说URL提取作者ID
      final authorId = _extractAuthorId(novelUrl);
      if (authorId != null) {
        return 'https://mypage.syosetu.com/$authorId/';
      }
    } else if (novelUrl.contains('kakuyomu.jp')) {
      // カクヨム的作者页面
      final authorId = _extractKakuyomuAuthorId(novelUrl);
      if (authorId != null) {
        return 'https://kakuyomu.jp/users/$authorId';
      }
    }
    return null;
  }

  // 提取作者ID（简化实现）
  String? _extractAuthorId(String novelUrl) {
    // 这里应该解析小说页面获取作者ID
    // 简化返回null
    return null;
  }

  // 提取カクヨム作者ID（简化实现）
  String? _extractKakuyomuAuthorId(String novelUrl) {
    // 这里应该解析小说页面获取作者ID
    // 简化返回null
    return null;
  }

  // 缓存页面内容
  Future<void> _cachePageContent(String url, String content) async {
    final prefs = await SharedPreferences.getInstance();

    // 生成内容哈希
    final contentHash = sha256.convert(utf8.encode(content)).toString();

    // 缓存内容和哈希
    await prefs.setString('$_contentPrefix$url', content);
    await prefs.setString('$_hashPrefix${url}_content', contentHash);
    await prefs.setInt(
      '$_timestampPrefix$url',
      DateTime.now().millisecondsSinceEpoch,
    );
  }

  // 获取缓存的页面内容
  Future<String?> getCachedPageContent(String url) async {
    if (!await _isCacheValid(url)) return null;

    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('$_contentPrefix$url');
  }

  // 清理过期缓存（现在是30天）
  Future<void> cleanExpiredCache() async {
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys();
    final now = DateTime.now();

    for (final key in keys) {
      if (key.startsWith(_timestampPrefix)) {
        final timestamp = prefs.getInt(key);
        if (timestamp != null) {
          final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
          if (now.difference(cacheTime) > _cacheExpiry) {
            // 删除过期的缓存
            final url = key.substring(_timestampPrefix.length);
            await prefs.remove('$_cachePrefix${url}_status');
            await prefs.remove('$_cachePrefix${url}_favicon');
            await prefs.remove('$_contentPrefix$url');
            await prefs.remove('$_hashPrefix$url');
            await prefs.remove('$_hashPrefix${url}_content');
            await prefs.remove(key);
          }
        }
      }
    }
  }
}
