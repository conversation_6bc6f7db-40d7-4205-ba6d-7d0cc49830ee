import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class ProxyWebViewPage extends StatefulWidget {
  final String originalUrl;
  final String title;

  const ProxyWebViewPage({
    super.key,
    required this.originalUrl,
    required this.title,
  });

  @override
  State<ProxyWebViewPage> createState() => _ProxyWebViewPageState();
}

class _ProxyWebViewPageState extends State<ProxyWebViewPage> {
  InAppWebViewController? webViewController;
  String currentUrl = "";
  double progress = 0;
  bool isLoading = true;

  // 多个代理服务器列表
  final List<String> proxyServers = [
    'https://cors-anywhere.herokuapp.com/',
    'https://api.allorigins.win/raw?url=',
    'https://corsproxy.io/?',
    'https://proxy.cors.sh/',
  ];

  int currentProxyIndex = 0;

  @override
  void initState() {
    super.initState();
    currentUrl = _buildProxyUrl(widget.originalUrl);
  }

  String _buildProxyUrl(String originalUrl) {
    return '${proxyServers[currentProxyIndex]}$originalUrl';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.title,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const Text(
              '代理模式',
              style: TextStyle(fontSize: 12, color: Colors.orange),
            ),
          ],
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        actions: [
          // 切换代理按钮
          IconButton(
            icon: const Icon(Icons.swap_horiz, color: Colors.orange),
            onPressed: _switchProxy,
            tooltip: '切换代理服务器',
          ),
          // 刷新按钮
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.blue),
            onPressed: () async {
              await webViewController?.reload();
            },
          ),
          // 原始链接按钮
          PopupMenuButton<String>(
            onSelected: (value) async {
              switch (value) {
                case 'original':
                  _showOriginalUrl();
                  break;
                case 'proxy_info':
                  _showProxyInfo();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'original',
                child: Row(
                  children: [
                    Icon(Icons.link, size: 20),
                    SizedBox(width: 8),
                    Text('查看原始链接'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'proxy_info',
                child: Row(
                  children: [
                    Icon(Icons.info, size: 20),
                    SizedBox(width: 8),
                    Text('代理信息'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 进度条
            if (isLoading)
              LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey[200],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            // 代理状态提示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.orange[50],
              child: Row(
                children: [
                  Icon(Icons.security, color: Colors.orange[700], size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '正在通过代理服务器访问，可能会有延迟',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: _switchProxy,
                    child: const Text('切换', style: TextStyle(fontSize: 12)),
                  ),
                ],
              ),
            ),
            // WebView
            Expanded(
              child: InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(currentUrl)),
                initialSettings: InAppWebViewSettings(
                  isInspectable: kDebugMode,
                  javaScriptEnabled: true,
                  domStorageEnabled: true,
                  useWideViewPort: true,
                  loadWithOverviewMode: true,
                  // 代理模式下的特殊设置
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  allowUniversalAccessFromFileURLs: true,
                  allowFileAccessFromFileURLs: true,
                ),
                onWebViewCreated: (controller) {
                  webViewController = controller;
                },
                onLoadStart: (controller, url) {
                  setState(() {
                    isLoading = true;
                  });
                },
                onProgressChanged: (controller, progress) {
                  setState(() {
                    this.progress = progress / 100;
                  });
                },
                onLoadStop: (controller, url) async {
                  setState(() {
                    isLoading = false;
                  });
                },
                onReceivedError: (controller, request, error) {
                  setState(() {
                    isLoading = false;
                  });
                  // 自动尝试下一个代理
                  _tryNextProxy();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 切换代理服务器
  void _switchProxy() {
    setState(() {
      currentProxyIndex = (currentProxyIndex + 1) % proxyServers.length;
      currentUrl = _buildProxyUrl(widget.originalUrl);
    });
    
    webViewController?.loadUrl(
      urlRequest: URLRequest(url: WebUri(currentUrl)),
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换到代理服务器 ${currentProxyIndex + 1}'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // 尝试下一个代理（错误时自动切换）
  void _tryNextProxy() {
    if (currentProxyIndex < proxyServers.length - 1) {
      Future.delayed(const Duration(seconds: 2), () {
        _switchProxy();
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('所有代理服务器都无法访问该网站'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }

  // 显示原始链接
  void _showOriginalUrl() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('原始链接'),
        content: SelectableText(widget.originalUrl),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  // 显示代理信息
  void _showProxyInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('代理信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('当前代理: ${proxyServers[currentProxyIndex]}'),
            const SizedBox(height: 8),
            const Text('可用代理服务器:'),
            ...proxyServers.asMap().entries.map((entry) {
              final index = entry.key;
              final proxy = entry.value;
              return Padding(
                padding: const EdgeInsets.only(left: 16, top: 4),
                child: Row(
                  children: [
                    Icon(
                      index == currentProxyIndex ? Icons.radio_button_checked : Icons.radio_button_unchecked,
                      size: 16,
                      color: index == currentProxyIndex ? Colors.blue : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Expanded(child: Text(proxy, style: const TextStyle(fontSize: 12))),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
