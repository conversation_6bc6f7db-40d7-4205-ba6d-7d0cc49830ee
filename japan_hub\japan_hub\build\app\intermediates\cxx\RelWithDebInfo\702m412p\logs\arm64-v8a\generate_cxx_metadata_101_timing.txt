# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 49ms
  [gap of 23ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 104ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 82ms
  [gap of 32ms]
generate_cxx_metadata completed in 142ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 66ms
  [gap of 22ms]
  write-metadata-json-to-file 13ms
generate_cxx_metadata completed in 123ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 46ms
  [gap of 23ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 95ms

