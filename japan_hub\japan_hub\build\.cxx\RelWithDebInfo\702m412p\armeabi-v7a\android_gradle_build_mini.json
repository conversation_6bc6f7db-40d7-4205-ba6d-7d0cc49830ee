{"buildFiles": ["D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Scoop\\apps\\android-clt\\current\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\lk\\japan_hub\\japan_hub\\build\\.cxx\\RelWithDebInfo\\702m412p\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Scoop\\apps\\android-clt\\current\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\lk\\japan_hub\\japan_hub\\build\\.cxx\\RelWithDebInfo\\702m412p\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}