import 'package:flutter/material.dart';
import '../models/tab_model.dart';

/// 标签页栏组件
class TabBarWidget extends StatelessWidget {
  final List<TabModel> tabs;
  final int currentTabIndex;
  final Function(int) onTabSelected;
  final Function(int) onTabClosed;
  final VoidCallback onNewTab;

  const TabBarWidget({
    super.key,
    required this.tabs,
    required this.currentTabIndex,
    required this.onTabSelected,
    required this.onTabClosed,
    required this.onNewTab,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          // 标签页列表
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: tabs.length,
              itemBuilder: (context, index) {
                return _buildTabItem(context, index);
              },
            ),
          ),
          
          // 新建标签页按钮
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(color: Colors.grey[300]!, width: 1),
              ),
            ),
            child: IconButton(
              onPressed: onNewTab,
              icon: Icon(Icons.add, color: Colors.grey[600]),
              tooltip: '新建标签页',
            ),
          ),
        ],
      ),
    );
  }

  /// 构建标签页项
  Widget _buildTabItem(BuildContext context, int index) {
    final tab = tabs[index];
    final isActive = index == currentTabIndex;
    
    return GestureDetector(
      onTap: () => onTabSelected(index),
      child: Container(
        constraints: const BoxConstraints(
          minWidth: 120,
          maxWidth: 200,
        ),
        height: 48,
        decoration: BoxDecoration(
          color: isActive ? Colors.white : Colors.transparent,
          border: Border(
            right: BorderSide(color: Colors.grey[300]!, width: 1),
            top: isActive 
              ? BorderSide(color: Colors.blue, width: 2)
              : BorderSide.none,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              // 网站图标或加载指示器
              SizedBox(
                width: 16,
                height: 16,
                child: tab.isLoading
                  ? SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.grey[600],
                      ),
                    )
                  : Icon(
                      Icons.public,
                      size: 16,
                      color: Colors.grey[600],
                    ),
              ),
              
              const SizedBox(width: 8),
              
              // 标签页标题
              Expanded(
                child: Text(
                  tab.displayTitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: isActive ? Colors.black87 : Colors.grey[600],
                    fontWeight: isActive ? FontWeight.w500 : FontWeight.normal,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              const SizedBox(width: 4),
              
              // 关闭按钮
              if (tabs.length > 1) // 至少保留一个标签页
                GestureDetector(
                  onTap: () => onTabClosed(index),
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.close,
                      size: 12,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
