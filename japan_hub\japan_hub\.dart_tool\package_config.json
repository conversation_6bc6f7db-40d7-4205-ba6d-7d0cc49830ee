{"configVersion": 2, "packages": [{"name": "async", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/async-2.13.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "characters", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fake_async", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/fake_async-1.3.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "flutter", "rootUri": "file:///D:/Scoop/persist/puro/data/envs/stable/flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_inappwebview", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview-6.1.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_android", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_internal_annotations", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_internal_annotations-1.2.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_inappwebview_ios", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_macos", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_platform_interface", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_platform_interface-1.3.0+1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_web", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_inappwebview_windows", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_lints", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_test", "rootUri": "file:///D:/Scoop/persist/puro/data/envs/stable/flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///D:/Scoop/persist/puro/data/envs/stable/flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "http", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "leak_tracker", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/leak_tracker-10.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "matcher", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_linux", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "shared_preferences", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///D:/Scoop/persist/puro/data/envs/stable/flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_span", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "stream_channel", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "url_launcher", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher-6.3.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_android", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_android-6.3.16", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_ios", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_ios-6.3.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "url_launcher_linux", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_linux-3.2.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_macos", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_macos-3.2.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "url_launcher_platform_interface", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "url_launcher_web", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_web-2.4.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "url_launcher_windows", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/url_launcher_windows-3.1.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/vm_service-15.0.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///D:/Scoop/persist/puro/data/shared/pub_cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "japan_hub", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.8"}], "generator": "pub", "generatorVersion": "3.8.1", "flutterRoot": "file:///D:/Scoop/persist/puro/data/envs/stable/flutter", "flutterVersion": "3.32.7", "pubCache": "file:///D:/Scoop/persist/puro/data/shared/pub_cache"}