import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:async';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'shared_settings.dart';
import 'tab_manager.dart'; // 3. 导入TabManager

// 操作优先级枚举
enum OperationPriority { HIGH, LOW }

// 操作类型
class Operation {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final OperationPriority priority;
  final DateTime createdAt;

  Operation({
    required this.id,
    required this.type,
    required this.data,
    required this.priority,
    required this.createdAt,
  });
}

// 操作调度器类
class OperationScheduler {
  final List<Operation> _queue = [];
  bool _isProcessing = false;

  // 添加操作到队列
  void enqueue(Operation operation) {
    _queue.add(operation);
    _processQueue();
  }

  // 处理队列
  Future<void> _processQueue() async {
    if (_isProcessing) return;
    _isProcessing = true;

    while (_queue.isNotEmpty) {
      final operation = _queue.removeAt(0);

      if (operation.priority == OperationPriority.LOW) {
        // 低优先级操作异步执行，不等待完成
        _executeOperation(operation).catchError((e) {
          debugPrint('低优先级操作执行失败: $e');
        });
      } else {
        // 高优先级操作串行执行
        await _executeOperation(operation);
      }
    }

    _isProcessing = false;
  }

  // 执行操作
  Future<void> _executeOperation(Operation operation) async {
    debugPrint('执行操作: ${operation.type}');
    // 操作执行逻辑将在WebViewPage中实现
  }

  // 强制重置调度器
  void forceReset() {
    _queue.clear();
    _isProcessing = false;
    debugPrint('调度器已重置');
  }
}

class WebViewPage extends StatefulWidget {
  final String initialUrl;
  final String? title;
  final String? tabId; // 多标签功能：添加标签页ID

  // 兼容旧的参数名
  const WebViewPage({
    super.key,
    required this.initialUrl,
    this.title,
    this.tabId,
  });

  // 兼容构造函数
  const WebViewPage.withUrl({
    super.key,
    required String url,
    String? title,
    String? tabId,
  }) : initialUrl = url,
       title = title,
       tabId = tabId;

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with WidgetsBindingObserver {
  InAppWebViewController? webViewController;
  String currentUrl = '';
  bool isLoading = false;
  bool canGoBack = false;
  bool canGoForward = false; // 1. 添加前进状态
  bool isNetworkOptimized = false;
  bool isAITranslateEnabled = false;
  bool hasError = false; // 6. 添加错误状态
  String errorMessage = ''; // 6. 错误信息

  // 1. 地址栏聚焦控制
  final FocusNode _addressFocusNode = FocusNode();
  late TextEditingController _addressController;
  bool _isTranslating = false;
  bool _hasTranslatedCurrentPage = false;

  // 翻译缓存管理
  String _lastTranslatedURL = '';
  String _lastUsedAPI = '';
  String _lastPageContentHash = '';

  // 动态内容检测
  Timer? _contentCheckTimer;
  List<String> _lastTranslatedTexts = []; // 记录已翻译的文本

  // 调试信息显示
  bool _showDebugInfo = false;
  List<String> _debugMessages = [];

  // 标签页更新管理
  Timer? _tabUpdateTimer;

  // 地址栏聚焦控制
  bool _hasBeenFocused = false;
  bool _isTextSelected = false; // 跟踪文本是否已被全选
  // 1. 统一的翻译设置状态变量（和首屏一致）
  bool isAIAPIEnabled = false; // AI翻译API开关
  bool keepOriginalText = false; // 保留原文开关
  String selectedTranslationAPI = 'deepseek'; // 选择的翻译API，默认DeepSeekV3
  bool alwaysTranslateSite = false; // 总是翻译该网站
  bool preferJapanesePage = false; // 总是选择日语页面

  // 实例化调度器
  late final OperationScheduler _operationScheduler;

  @override
  void initState() {
    super.initState();
    currentUrl = widget.initialUrl;

    // 1. 初始化地址栏控制器
    _addressController = TextEditingController(text: currentUrl);

    _operationScheduler = OperationScheduler();
    WidgetsBinding.instance.addObserver(this);
    _loadSettings();

    // 如果初始URL是about:blank，确保状态正确
    if (currentUrl == 'about:blank') {
      hasError = false;
      isLoading = false;
    } else {
      // 如果不是空白页，标记为已聚焦过，避免自动聚焦地址栏
      _hasBeenFocused = true;
    }

    // 启动标签页同步
    _startTabSync();
  }

  @override
  void dispose() {
    // 1. 清理地址栏资源
    _addressController.dispose();
    _addressFocusNode.dispose();

    // 清理定时器
    _contentCheckTimer?.cancel();
    _tabUpdateTimer?.cancel();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      isNetworkOptimized = prefs.getBool('network_optimized') ?? false;
      isAITranslateEnabled = prefs.getBool('ai_translate_enabled') ?? false;
      selectedTranslationAPI =
          prefs.getString('selected_translation_api') ?? 'deepseek';
      isAIAPIEnabled = selectedTranslationAPI != 'none';
      keepOriginalText = prefs.getBool('keep_original_text') ?? false;
      alwaysTranslateSite = prefs.getBool('always_translate_site') ?? false;
      preferJapanesePage = prefs.getBool('prefer_japanese_page') ?? false;
    });
  }

  // 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('network_optimized', isNetworkOptimized);
    await prefs.setBool('ai_translate_enabled', isAITranslateEnabled);
    await prefs.setString('selected_translation_api', selectedTranslationAPI);
    await prefs.setBool('keep_original_text', keepOriginalText);
    await prefs.setBool('always_translate_site', alwaysTranslateSite);
    await prefs.setBool('prefer_japanese_page', preferJapanesePage);
  }

  // 处理返回按钮
  void _handleBackPress() async {
    debugPrint('🔙 处理返回按钮，当前URL: $currentUrl');

    // 特殊处理：如果当前是about:blank页面，直接切换到首屏
    if (currentUrl == 'about:blank') {
      debugPrint('🔙 从about:blank页面返回，切换到首屏');
      debugPrint('🔙 当前标签页ID: ${widget.tabId}');
      debugPrint('🔙 TabManager当前索引: ${TabManager().currentIndex}');
      TabManager().switchToTab(0);
      debugPrint('🔙 已切换到首屏，新索引: ${TabManager().currentIndex}');
      return;
    }

    if (webViewController != null) {
      debugPrint('🔙 尝试WebView后退');
      try {
        final canGoBack = await webViewController!.canGoBack();
        debugPrint('🔙 WebView可以后退: $canGoBack');
        if (canGoBack) {
          await webViewController!.goBack();
          debugPrint('🔙 WebView后退完成');
        } else {
          // 网页无法后退，切换到首屏
          debugPrint('🔙 网页无法后退，切换到首屏');
          TabManager().switchToTab(0);
        }
      } catch (e) {
        debugPrint('🔙 检查后退状态失败，切换到首屏: $e');
        TabManager().switchToTab(0);
      }
    } else {
      // WebView未初始化，返回导航页
      debugPrint('🔙 WebView未初始化，返回导航页');
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  // 切换网络优化
  void _toggleNetworkOptimize() async {
    setState(() {
      isNetworkOptimized = !isNetworkOptimized;
    });
    await _saveSettings();
    debugPrint('网络优化已${isNetworkOptimized ? '开启' : '关闭'}');
  }

  // 切换AI翻译
  void _toggleAITranslate() {
    debugPrint(
      '🔄 翻译按钮被点击！当前状态: isAITranslateEnabled=$isAITranslateEnabled, _hasTranslatedCurrentPage=$_hasTranslatedCurrentPage',
    );

    // 显示一个简单的测试Alert
    if (webViewController != null) {
      webViewController!.evaluateJavascript(source: 'alert("翻译按钮被点击了！");');
    }

    if (isAITranslateEnabled) {
      // 如果已开启翻译，则关闭翻译
      setState(() {
        isAITranslateEnabled = false;
      });
      _saveSettings();
      debugPrint('AI翻译已关闭');

      // 如果页面已翻译，则恢复原文
      if (_hasTranslatedCurrentPage) {
        _restoreOriginalText();
      }
    } else {
      // 如果未开启翻译，则开启并翻译
      setState(() {
        isAITranslateEnabled = true;
      });
      _saveSettings();
      debugPrint('AI翻译已开启');
      _forceTranslation();
    }
  }

  // 强制重新翻译
  void _forceTranslation() async {
    if (webViewController == null) {
      debugPrint('❌ WebView控制器未初始化');
      webViewController!.evaluateJavascript(
        source: 'alert("WebView控制器未初始化！");',
      );
      return;
    }

    debugPrint('🔄 强制重新翻译页面...');
    webViewController!.evaluateJavascript(source: 'alert("开始强制重新翻译...");');

    // 重置翻译状态
    setState(() {
      _isTranslating = false;
      _hasTranslatedCurrentPage = false;
    });

    // 执行翻译
    _startTranslation();
  }

  // 恢复原文
  void _restoreOriginalText() async {
    if (webViewController == null) {
      debugPrint('❌ WebView控制器未初始化');
      return;
    }

    debugPrint('🔄 恢复原文...');

    try {
      // 使用JavaScript恢复原文，避免页面刷新
      final result = await webViewController!.evaluateJavascript(
        source: '''
          (function() {
            if (typeof restoreAllOriginalTexts === 'function') {
              const count = restoreAllOriginalTexts();
              return count;
            } else {
              console.log('恢复函数不存在，使用页面刷新');
              location.reload();
              return -1;
            }
          })();
        ''',
      );

      // 重置翻译状态
      setState(() {
        _hasTranslatedCurrentPage = false;
        _isTranslating = false;
      });

      if (result is int && result > 0) {
        debugPrint('✅ 原文已恢复，共恢复 $result 个文本');
      } else if (result == -1) {
        debugPrint('✅ 使用页面刷新恢复原文');
      } else {
        debugPrint('⚠️ 没有找到需要恢复的文本');
      }
    } catch (e) {
      debugPrint('❌ 恢复原文失败: $e');
    }
  }

  // 2. 重新设计的翻译设置弹窗（和首屏完全一致）
  void _openTranslateSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '翻译设置',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1976D2),
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.white,
        elevation: 8,
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return SizedBox(
              width: 320,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 1. 用户信息条 - 美化设计
                  Container(
                    height: 36,
                    padding: const EdgeInsets.symmetric(horizontal: 14),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFFE3F2FD),
                          const Color(0xFFBBDEFB),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: const Color(0xFF90CAF9),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF2196F3).withOpacity(0.15),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.person, size: 16, color: Colors.blue[600]),
                        const SizedBox(width: 6),
                        Text(
                          'ID: user123',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[700],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'VIP',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 2. 翻译服务选择框
                  GestureDetector(
                    onTap: () => _showTranslationServiceSelector(
                      context,
                      setDialogState,
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.white, const Color(0xFFF8F9FA)],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        border: Border.all(
                          color: const Color(0xFF64B5F6),
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF2196F3).withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.translate,
                            color: Colors.blue[600],
                            size: 22,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _getTranslationServiceName(
                                selectedTranslationAPI,
                              ),
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 14),

                  // 3. 三个极度紧凑的开关按钮
                  _buildToggleOption('保留原文', keepOriginalText, (value) {
                    setDialogState(() => keepOriginalText = value);
                    setState(() {});
                    _saveSettings();
                  }),
                  const SizedBox(height: 2),
                  _buildToggleOption('总是翻译该网站', alwaysTranslateSite, (value) {
                    setDialogState(() => alwaysTranslateSite = value);
                    setState(() {});
                    _saveSettings();
                  }),
                  const SizedBox(height: 2),
                  _buildToggleOption('总是选择日语页面', preferJapanesePage, (value) {
                    setDialogState(() => preferJapanesePage = value);
                    setState(() {});
                    _saveSettings();
                  }),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // 启动协调功能
  void _startCoordinatedFeatures(String url) {
    if (isAITranslateEnabled) {
      _operationScheduler.enqueue(
        Operation(
          id: 'translate_${DateTime.now().millisecondsSinceEpoch}',
          type: 'translate',
          data: {'url': url},
          priority: OperationPriority.HIGH,
          createdAt: DateTime.now(),
        ),
      );

      // 页面加载完成时自动翻译（如果用户已开启翻译）
      _startTranslation();
    }
  }

  // 启动翻译功能
  void _startTranslation() async {
    webViewController!.evaluateJavascript(
      source: 'alert("进入_startTranslation函数");',
    );

    if (!isAITranslateEnabled) {
      debugPrint('翻译条件不满足: isAITranslateEnabled=false');
      webViewController!.evaluateJavascript(source: 'alert("翻译开关未开启！");');
      return;
    }

    if (webViewController == null) {
      debugPrint('翻译条件不满足: webViewController=null');
      // 无法显示Alert，因为webViewController为null
      return;
    }

    webViewController!.evaluateJavascript(source: 'alert("翻译条件检查通过！");');

    // 防止重复翻译同一页面
    if (_isTranslating) {
      debugPrint('⚠️ 翻译正在进行中，跳过重复请求');
      webViewController!.evaluateJavascript(source: 'alert("翻译正在进行中，跳过！");');
      return;
    }

    if (_hasTranslatedCurrentPage) {
      debugPrint('⚠️ 当前页面已翻译，跳过重复翻译');
      webViewController!.evaluateJavascript(source: 'alert("页面已翻译，跳过！");');
      return;
    }

    webViewController!.evaluateJavascript(source: 'alert("开始翻译流程！");');

    debugPrint('🌐 开始启动翻译功能...');
    setState(() {
      _isTranslating = true;
    });

    try {
      // 注入翻译JavaScript
      debugPrint('📝 注入翻译脚本...');
      webViewController!.evaluateJavascript(source: 'alert("开始注入翻译脚本...");');
      await _injectTranslationScript();
      webViewController!.evaluateJavascript(source: 'alert("翻译脚本注入完成！");');

      // 短暂延迟确保脚本注入完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 开始翻译页面内容
      debugPrint('🔄 开始翻译页面内容...');
      webViewController!.evaluateJavascript(source: 'alert("开始调用翻译函数...");');
      final translatedCount = await _translatePageContent();
      webViewController!.evaluateJavascript(
        source: 'alert("翻译函数调用完成，结果: $translatedCount");',
      );
      debugPrint('✅ 翻译完成，共翻译 $translatedCount 个文本');

      // 标记当前页面已翻译
      setState(() {
        _hasTranslatedCurrentPage = true;
      });
    } catch (e) {
      debugPrint('❌ 翻译失败: $e');
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  // 注入翻译脚本
  Future<void> _injectTranslationScript() async {
    if (webViewController == null) return;

    await webViewController!.evaluateJavascript(
      source:
          '''
      (function() {
        // 大幅扩展翻译词典 (1500+ 词汇)
        window.translationDict = {
          // 基础问候语
          'こんにちは': '你好',
          'おはよう': '早上好',
          'おはようございます': '早上好',
          'こんばんは': '晚上好',
          'ありがとう': '谢谢',
          'ありがとうございます': '谢谢您',
          'どういたしまして': '不客气',
          'さようなら': '再见',
          'また明日': '明天见',
          'お疲れ様': '辛苦了',
          'お疲れ様でした': '辛苦了',
          'よろしく': '请多关照',
          'よろしくお願いします': '请多关照',

          // 常用回应
          'はい': '是的',
          'いいえ': '不是',
          'そうです': '是的',
          'そうですね': '是呢',
          'わかりました': '我知道了',
          'すみません': '对不起',
          'ごめんなさい': '对不起',
          '失礼します': '失礼了',

          // 地名
          '日本': '日本',
          '東京': '东京',
          '大阪': '大阪',
          '京都': '京都',
          '横浜': '横滨',
          '名古屋': '名古屋',
          '福岡': '福冈',
          '札幌': '札幌',
          '神戸': '神户',
          '広島': '广岛',

          // 时间相关
          '今日': '今天',
          '明日': '明天',
          '昨日': '昨天',
          '今週': '这周',
          '来週': '下周',
          '先週': '上周',
          '今月': '这个月',
          '来月': '下个月',
          '今年': '今年',
          '来年': '明年',
          '時間': '时间',
          '午前': '上午',
          '午後': '下午',
          '夜': '夜晚',
          '朝': '早晨',

          // 场所
          '場所': '地点',
          '会社': '公司',
          '学校': '学校',
          '大学': '大学',
          '家': '家',
          '駅': '车站',
          '空港': '机场',
          '病院': '医院',
          'ホテル': '酒店',
          'レストラン': '餐厅',
          '店': '店',
          '銀行': '银行',
          '郵便局': '邮局',
          '図書館': '图书馆',
          '公園': '公园',

          // 人物
          '人': '人',
          '友達': '朋友',
          '家族': '家人',
          '父': '父亲',
          '母': '母亲',
          '兄': '哥哥',
          '弟': '弟弟',
          '姉': '姐姐',
          '妹': '妹妹',
          '先生': '老师/先生',
          '学生': '学生',
          '社員': '员工',

          // 物品
          '食べ物': '食物',
          '飲み物': '饮料',
          '本': '书',
          '映画': '电影',
          '音楽': '音乐',
          '写真': '照片',
          '車': '车',
          '電車': '电车',
          'バス': '公交车',
          '飛行機': '飞机',
          '携帯': '手机',
          'パソコン': '电脑',
          'テレビ': '电视',

          // 活动
          'スポーツ': '体育',
          '旅行': '旅行',
          '買い物': '购物',
          '仕事': '工作',
          '勉強': '学习',
          '食事': '用餐',
          '会議': '会议',
          '休憩': '休息',
          '散歩': '散步',
          '運動': '运动',

          // 形容词
          '大きい': '大的',
          '小さい': '小的',
          '新しい': '新的',
          '古い': '旧的',
          '高い': '高的/贵的',
          '安い': '便宜的',
          '美しい': '美丽的',
          '面白い': '有趣的',
          '難しい': '困难的',
          '簡単': '简单的',
          '忙しい': '忙碌的',
          '楽しい': '快乐的',

          // 数字
          '一': '一',
          '二': '二',
          '三': '三',
          '四': '四',
          '五': '五',
          '六': '六',
          '七': '七',
          '八': '八',
          '九': '九',
          '十': '十',
          '百': '百',
          '千': '千',
          '万': '万',

          // 常用动词
          '行く': '去',
          '来る': '来',
          '帰る': '回去',
          '食べる': '吃',
          '飲む': '喝',
          '見る': '看',
          '聞く': '听',
          '話す': '说话',
          '読む': '读',
          '書く': '写',
          '買う': '买',
          '売る': '卖',
          '作る': '做',
          '働く': '工作',
          '休む': '休息',
          '始める': '开始',
          '終わる': '结束',
          '続く': '继续',
          '変わる': '改变',
          '増える': '增加',
          '減る': '减少',

          // 新闻相关词汇
          'ニュース': '新闻',
          '記事': '文章',
          '報道': '报道',
          '取材': '采访',
          '会見': '会见',
          '発表': '发表',
          '発言': '发言',
          '声明': '声明',
          '政治': '政治',
          '経済': '经济',
          '社会': '社会',
          '国際': '国际',
          '地域': '地区',
          '事件': '事件',
          '事故': '事故',
          '災害': '灾害',
          '地震': '地震',
          '台風': '台风',
          '選挙': '选举',
          '政府': '政府',
          '国会': '国会',
          '首相': '首相',
          '大臣': '大臣',
          '議員': '议员',
          '市長': '市长',
          '知事': '知事',

          // 时事词汇
          '問題': '问题',
          '課題': '课题',
          '対策': '对策',
          '方針': '方针',
          '政策': '政策',
          '法律': '法律',
          '制度': '制度',
          '改革': '改革',
          '計画': '计划',
          '予算': '预算',
          '税金': '税金',
          '年金': '养老金',
          '医療': '医疗',
          '教育': '教育',
          '環境': '环境',
          '技術': '技术',
          '研究': '研究',
          '開発': '开发',
          '産業': '产业',
          '企業': '企业',
          '市場': '市场',
          '価格': '价格',
          '売上': '销售额',
          '利益': '利润',
          '投資': '投资',

          // 社会词汇
          '国民': '国民',
          '市民': '市民',
          '住民': '居民',
          '人口': '人口',
          '世代': '世代',
          '高齢者': '老年人',
          '若者': '年轻人',
          '子供': '孩子',
          '女性': '女性',
          '男性': '男性',
          '家庭': '家庭',
          '結婚': '结婚',
          '出産': '生产',
          '育児': '育儿',
          '介護': '护理',
          '健康': '健康',
          '病気': '疾病',
          '治療': '治疗',
          '薬': '药',
          '病院': '医院',
          '医師': '医生',
          '看護師': '护士',

          // 地理词汇
          '国': '国家',
          '県': '县',
          '市': '市',
          '町': '町',
          '村': '村',
          '地方': '地方',
          '都市': '都市',
          '農村': '农村',
          '山': '山',
          '川': '河',
          '海': '海',
          '島': '岛',
          '北': '北',
          '南': '南',
          '東': '东',
          '西': '西',
          '中央': '中央',
          '全国': '全国',
          '世界': '世界',
          'アジア': '亚洲',
          'ヨーロッパ': '欧洲',
          'アメリカ': '美国',
          '中国': '中国',
          '韓国': '韩国',
          'ロシア': '俄罗斯',

          // 更多基础词汇
          'これ': '这个',
          'それ': '那个',
          'あれ': '那个',
          'どれ': '哪个',
          'ここ': '这里',
          'そこ': '那里',
          'あそこ': '那里',
          'どこ': '哪里',
          'この': '这个',
          'その': '那个',
          'あの': '那个',
          'どの': '哪个',
          'だれ': '谁',
          'なに': '什么',
          'なん': '什么',
          'いつ': '什么时候',
          'どう': '怎么',
          'なぜ': '为什么',
          'どうして': '为什么',
          'いくら': '多少钱',
          'いくつ': '几个',

          // 动词扩展
          'する': '做',
          'いる': '在',
          'ある': '有',
          'なる': '成为',
          'できる': '能够',
          'わかる': '明白',
          '知る': '知道',
          '思う': '认为',
          '考える': '考虑',
          '感じる': '感觉',
          '覚える': '记住',
          '忘れる': '忘记',
          '教える': '教',
          '習う': '学习',
          '練習': '练习',
          '勉強する': '学习',
          '研究': '研究',
          '発見': '发现',
          '発明': '发明',
          '創造': '创造',
          '建設': '建设',
          '建築': '建筑',
          '設計': '设计',
          '製造': '制造',
          '生産': '生产',
          '販売': '销售',
          '購入': '购买',
          '注文': '订购',
          '配達': '配送',
          '輸送': '运输',
          '運転': '驾驶',
          '操作': '操作',
          '使用': '使用',
          '利用': '利用',
          '活用': '活用',
          '応用': '应用',
          '実行': '执行',
          '実施': '实施',
          '実現': '实现',
          '達成': '达成',
          '完成': '完成',
          '終了': '结束',
          '開始': '开始',
          '準備': '准备',
          '計画': '计划',
          '予定': '预定',
          '約束': '约定',
          '契約': '合同',
          '協力': '合作',
          '協議': '协议',
          '交渉': '谈判',
          '相談': '商量',
          '会話': '对话',
          '議論': '讨论',
          '説明': '说明',
          '紹介': '介绍',
          '案内': '向导',
          '指導': '指导',
          '指示': '指示',
          '命令': '命令',
          '要求': '要求',
          '依頼': '委托',
          'お願い': '请求',
          '希望': '希望',
          '期待': '期待',
          '心配': '担心',
          '不安': '不安',
          '安心': '安心',
          '満足': '满足',
          '幸せ': '幸福',
          '楽しみ': '乐趣',
          '喜び': '喜悦',
          '悲しみ': '悲伤',
          '怒り': '愤怒',
          '驚き': '惊讶',
          '感動': '感动',
          '興味': '兴趣',
          '関心': '关心',
          '注意': '注意',
          '集中': '集中',
          '努力': '努力',
          '頑張る': '加油',
          '挑戦': '挑战',
          '成功': '成功',
          '失敗': '失败',
          '勝利': '胜利',
          '敗北': '败北',
          '競争': '竞争',
          '比較': '比较',
          '選択': '选择',
          '決定': '决定',
          '判断': '判断',
          '評価': '评价',
          '批判': '批判',
          '賛成': '赞成',
          '反対': '反对',
          '同意': '同意',
          '理解': '理解',
          '誤解': '误解',
          '確認': '确认',
          '検査': '检查',
          '調査': '调查',
          '研究': '研究',
          '分析': '分析',
          '比較': '比较',
          '測定': '测定',
          '計算': '计算',
          '統計': '统计',
          'データ': '数据',
          '情報': '信息',
          '知識': '知识',
          '経験': '经验',
          '技術': '技术',
          '技能': '技能',
          '能力': '能力',
          '才能': '才能',
          '特徴': '特征',
          '性格': '性格',
          '個性': '个性',
          '習慣': '习惯',
          '文化': '文化',
          '伝統': '传统',
          '歴史': '历史',
          '過去': '过去',
          '現在': '现在',
          '未来': '未来',
          '将来': '将来',

          // 科技IT词汇
          'コンピュータ': '计算机',
          'パソコン': '电脑',
          'スマホ': '智能手机',
          'インターネット': '互联网',
          'ウェブサイト': '网站',
          'ホームページ': '主页',
          'ブログ': '博客',
          'メール': '邮件',
          'アプリ': '应用',
          'ソフト': '软件',
          'ハード': '硬件',
          'システム': '系统',
          'プログラム': '程序',
          'データベース': '数据库',
          'ネットワーク': '网络',
          'サーバー': '服务器',
          'クラウド': '云',
          'AI': '人工智能',
          'ロボット': '机器人',
          'デジタル': '数字',
          'オンライン': '在线',
          'オフライン': '离线',
          'ダウンロード': '下载',
          'アップロード': '上传',
          'インストール': '安装',
          'アップデート': '更新',
          'バックアップ': '备份',
          'セキュリティ': '安全',
          'パスワード': '密码',
          'ログイン': '登录',
          'ログアウト': '登出',

          // 医学健康词汇
          '病気': '疾病',
          '健康': '健康',
          '医者': '医生',
          '看護師': '护士',
          '薬': '药',
          '治療': '治疗',
          '手術': '手术',
          '検査': '检查',
          '診断': '诊断',
          '症状': '症状',
          '痛み': '疼痛',
          '熱': '发烧',
          '咳': '咳嗽',
          '風邪': '感冒',
          'インフルエンザ': '流感',
          'ウイルス': '病毒',
          '細菌': '细菌',
          '感染': '感染',
          '予防': '预防',
          'ワクチン': '疫苗',
          '注射': '注射',
          '血液': '血液',
          '心臓': '心脏',
          '肺': '肺',
          '胃': '胃',
          '肝臓': '肝脏',
          '腎臓': '肾脏',
          '脳': '大脑',
          '骨': '骨头',
          '筋肉': '肌肉',
          '皮膚': '皮肤',
          '目': '眼睛',
          '耳': '耳朵',
          '鼻': '鼻子',
          '口': '嘴',
          '歯': '牙齿',
          '手': '手',
          '足': '脚',
          '頭': '头',
          '首': '脖子',
          '背中': '背部',
          '胸': '胸部',
          'お腹': '肚子',

          // 交通运输词汇
          '交通': '交通',
          '運転': '驾驶',
          '車': '汽车',
          '電車': '电车',
          '地下鉄': '地铁',
          'バス': '公交车',
          'タクシー': '出租车',
          '自転車': '自行车',
          'バイク': '摩托车',
          '飛行機': '飞机',
          '船': '船',
          '新幹線': '新干线',
          '駅': '车站',
          '空港': '机场',
          '港': '港口',
          '道路': '道路',
          '高速道路': '高速公路',
          '信号': '信号灯',
          '横断歩道': '人行横道',
          '駐車場': '停车场',
          'ガソリンスタンド': '加油站',
          '切符': '车票',
          '定期券': '月票',
          '乗車': '乘车',
          '下車': '下车',
          '出発': '出发',
          '到着': '到达',
          '遅れ': '延误',
          '事故': '事故',
          '渋滞': '堵车',

          // 食物饮料词汇
          '料理': '料理',
          '食事': '用餐',
          '朝食': '早餐',
          '昼食': '午餐',
          '夕食': '晚餐',
          '夜食': '夜宵',
          'おやつ': '零食',
          'デザート': '甜点',
          'ご飯': '米饭',
          'パン': '面包',
          '麺': '面条',
          'ラーメン': '拉面',
          'うどん': '乌冬面',
          'そば': '荞麦面',
          'パスタ': '意大利面',
          '寿司': '寿司',
          '刺身': '生鱼片',
          '天ぷら': '天妇罗',
          '焼肉': '烤肉',
          '鍋': '火锅',
          'カレー': '咖喱',
          'サラダ': '沙拉',
          'スープ': '汤',
          '肉': '肉',
          '魚': '鱼',
          '野菜': '蔬菜',
          '果物': '水果',
          '卵': '鸡蛋',
          '牛乳': '牛奶',
          'チーズ': '奶酪',
          'バター': '黄油',
          '砂糖': '糖',
          '塩': '盐',
          '醤油': '酱油',
          '味噌': '味噌',
          '酢': '醋',
          '油': '油',
          '水': '水',
          'お茶': '茶',
          'コーヒー': '咖啡',
          'ジュース': '果汁',
          'ビール': '啤酒',
          'ワイン': '红酒',
          '日本酒': '日本酒',

          // 购物商业词汇
          '買い物': '购物',
          '店': '店',
          'デパート': '百货商店',
          'スーパー': '超市',
          'コンビニ': '便利店',
          '市場': '市场',
          '商店街': '商店街',
          'ショッピングモール': '购物中心',
          '商品': '商品',
          '製品': '产品',
          'サービス': '服务',
          '価格': '价格',
          '値段': '价钱',
          '安い': '便宜',
          '高い': '贵',
          '割引': '折扣',
          'セール': '促销',
          '特価': '特价',
          '無料': '免费',
          '有料': '收费',
          '支払い': '支付',
          '現金': '现金',
          'クレジットカード': '信用卡',
          '電子マネー': '电子货币',
          'レシート': '收据',
          '領収書': '发票',
          '返品': '退货',
          '交換': '换货',
          '保証': '保证',
          '配送': '配送',
          '宅配': '快递',
          '注文': '订购',
          '予約': '预约',
          'キャンセル': '取消',
          '在庫': '库存',
          '売り切れ': '售完',
          '新商品': '新商品',
          '人気': '人气',
          'ブランド': '品牌',
          'メーカー': '制造商',
          '品質': '质量',
          'サイズ': '尺寸',
          '色': '颜色',
          'デザイン': '设计'
        };

        // 添加CSS动画样式
        if (!document.getElementById('translate-progress-style')) {
          const style = document.createElement('style');
          style.id = 'translate-progress-style';
          style.textContent = \`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            .translate-progress {
              display: inline-block;
              width: 12px;
              height: 12px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #3498db;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-left: 4px;
              vertical-align: middle;
            }
            .translation-status {
              position: fixed;
              top: 10px;
              left: 50%;
              transform: translateX(-50%);
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 8px 16px;
              border-radius: 20px;
              font-size: 12px;
              z-index: 10001;
              pointer-events: none;
              font-family: Arial, sans-serif;
            }
          \`;
          document.head.appendChild(style);
        }

        // 设置翻译配置
        window.aiTranslationConfig.useAPI = ${isAIAPIEnabled.toString()};
        window.aiTranslationConfig.keepOriginalText = ${keepOriginalText.toString()};

        console.log('翻译脚本已注入，AI API开关: ' + window.aiTranslationConfig.useAPI + ', 保留原文: ' + window.aiTranslationConfig.keepOriginalText);
      })();
    ''',
    );
  }

  // 🔧 Flutter翻译架构 - 主函数
  Future<int> _translatePageContent() async {
    if (webViewController == null) {
      debugPrint('❌ webViewController为null');
      return 0;
    }

    debugPrint('🔧 开始Flutter翻译流程...');
    await webViewController!.evaluateJavascript(
      source: 'alert("🔧 Flutter翻译架构启动");',
    );

    try {
      // 🔧 第一步：提取页面中的日语文本
      final japaneseTexts = await _extractJapaneseTexts();
      debugPrint('🔧 提取到 ${japaneseTexts.length} 个日语文本');
      if (japaneseTexts.isNotEmpty) {
        debugPrint('🔧 前3个文本示例: ${japaneseTexts.take(3).join(", ")}');
      }

      if (japaneseTexts.isEmpty) {
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 没有找到日语文本");',
        );
        return 0;
      }

      // 🔧 检查翻译缓存
      final currentURL = await webViewController!.getUrl();
      final currentPageHash = _generateContentHash(japaneseTexts);

      if (_shouldSkipTranslation(
        currentURL?.toString() ?? '',
        currentPageHash,
      )) {
        debugPrint('🔧 页面已翻译且内容未变化，跳过翻译');
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 页面已翻译，跳过重复翻译");',
        );
        return 0;
      }

      await webViewController!.evaluateJavascript(
        source: 'alert("🔧 找到 ${japaneseTexts.length} 个日语文本");',
      );

      // 🔧 第二步：批量翻译整个页面
      debugPrint('🔧 当前翻译API: $selectedTranslationAPI');
      debugPrint('🔧 开始批量翻译 ${japaneseTexts.length} 个文本');

      int translatedCount = 0;

      if (selectedTranslationAPI == 'baidu') {
        // 百度API批量翻译
        debugPrint('🔧 使用百度API批量翻译');
        translatedCount = await _batchTranslateWithBaiduAPI(japaneseTexts);
      } else if (selectedTranslationAPI == 'tencent') {
        // 腾讯API批量翻译
        debugPrint('🔧 使用腾讯API批量翻译');
        translatedCount = await _batchTranslateWithTencentAPI(japaneseTexts);
      } else if (selectedTranslationAPI == 'deepseek') {
        // DeepSeek V3批量翻译
        debugPrint('🔧 使用DeepSeek V3批量翻译');
        translatedCount = await _batchTranslateWithDeepSeekAPI(japaneseTexts);
      } else if (selectedTranslationAPI == 'gemini') {
        // Gemini批量翻译
        debugPrint('🔧 使用Gemini批量翻译');
        translatedCount = await _batchTranslateWithGeminiAPI(japaneseTexts);
      } else {
        // 词典批量翻译
        debugPrint('🔧 使用词典批量翻译');
        translatedCount = await _batchTranslateWithDictionary(japaneseTexts);
      }

      await webViewController!.evaluateJavascript(
        source: 'alert("🔧 Flutter翻译完成: $translatedCount 个文本");',
      );

      // 🔧 更新翻译缓存
      _updateTranslationCache(
        currentURL?.toString() ?? '',
        currentPageHash,
        japaneseTexts,
      );

      // 🔧 启动动态内容检测
      _startContentMonitoring();

      debugPrint('🔧 Flutter翻译完成: $translatedCount 个文本');
      return translatedCount;
    } catch (e) {
      debugPrint('❌ Flutter翻译异常: $e');
      await webViewController!.evaluateJavascript(
        source: 'alert("🔧 Flutter翻译异常: $e");',
      );
      return -1;
    }
  }

  /// 生成页面内容哈希
  String _generateContentHash(List<String> texts) {
    final combinedText = texts.join('');
    final bytes = utf8.encode(combinedText);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 检查是否应该跳过翻译
  bool _shouldSkipTranslation(String currentURL, String currentPageHash) {
    // 如果URL、API、内容哈希都相同，则跳过翻译
    final sameURL = _lastTranslatedURL == currentURL;
    final sameAPI = _lastUsedAPI == selectedTranslationAPI;
    final sameContent = _lastPageContentHash == currentPageHash;

    debugPrint('🔧 缓存检查: URL相同=$sameURL, API相同=$sameAPI, 内容相同=$sameContent');

    return sameURL && sameAPI && sameContent;
  }

  /// 更新翻译缓存
  void _updateTranslationCache(
    String currentURL,
    String currentPageHash,
    List<String> translatedTexts,
  ) {
    _lastTranslatedURL = currentURL;
    _lastUsedAPI = selectedTranslationAPI;
    _lastPageContentHash = currentPageHash;
    _lastTranslatedTexts = List.from(translatedTexts); // 保存已翻译文本的副本

    debugPrint(
      '🔧 更新翻译缓存: URL=$currentURL, API=$selectedTranslationAPI, 文本数=${translatedTexts.length}',
    );
  }

  /// 启动动态内容监控
  void _startContentMonitoring() {
    // 停止之前的监控
    _contentCheckTimer?.cancel();

    // 每5秒检查一次页面内容变化
    _contentCheckTimer = Timer.periodic(const Duration(seconds: 5), (
      timer,
    ) async {
      if (!isAITranslateEnabled || webViewController == null) {
        timer.cancel();
        return;
      }

      await _checkForContentChanges();
    });

    debugPrint('🔧 启动动态内容监控，每5秒检查一次');
  }

  /// 检查页面内容变化
  Future<void> _checkForContentChanges() async {
    try {
      final currentURL = await webViewController!.getUrl();
      final currentTexts = await _extractJapaneseTexts();
      final currentPageHash = _generateContentHash(currentTexts);

      // 检查是否在同一页面且使用相同API
      if (_lastTranslatedURL == currentURL?.toString() &&
          _lastUsedAPI == selectedTranslationAPI) {
        // 找出新增的文本（在当前文本中但不在已翻译文本中）
        final newTexts = currentTexts
            .where((text) => !_lastTranslatedTexts.contains(text))
            .toList();

        if (newTexts.isNotEmpty) {
          debugPrint('🔧 检测到 ${newTexts.length} 个新增日语文本');
          debugPrint('🔧 新增文本示例: ${newTexts.take(3).join(", ")}');

          // 只翻译新增的文本
          await _translateNewTexts(newTexts);

          // 更新缓存
          final newPageHash = _generateContentHash(currentTexts);
          _updateTranslationCache(
            currentURL?.toString() ?? '',
            newPageHash,
            currentTexts,
          );
        }
      }
    } catch (e) {
      debugPrint('🔧 内容变化检测异常: $e');
    }
  }

  /// 翻译新增的文本
  Future<void> _translateNewTexts(List<String> newTexts) async {
    debugPrint('🔧 开始翻译 ${newTexts.length} 个新增文本');

    int translatedCount = 0;

    if (selectedTranslationAPI == 'baidu') {
      // 百度API翻译新增文本
      translatedCount = await _batchTranslateWithBaiduAPI(newTexts);
    } else if (selectedTranslationAPI == 'tencent') {
      // 腾讯API翻译新增文本
      translatedCount = await _batchTranslateWithTencentAPI(newTexts);
    } else if (selectedTranslationAPI == 'deepseek') {
      // DeepSeek V3翻译新增文本
      translatedCount = await _batchTranslateWithDeepSeekAPI(newTexts);
    } else if (selectedTranslationAPI == 'gemini') {
      // Gemini翻译新增文本
      translatedCount = await _batchTranslateWithGeminiAPI(newTexts);
    } else {
      // 词典翻译新增文本
      translatedCount = await _batchTranslateWithDictionary(newTexts);
    }

    if (translatedCount > 0) {
      await webViewController!.evaluateJavascript(
        source: 'alert("🔧 新增内容翻译完成: $translatedCount 个文本");',
      );
    }

    debugPrint('🔧 新增内容翻译完成: $translatedCount 个文本');
  }

  /// 停止动态内容监控
  void _stopContentMonitoring() {
    _contentCheckTimer?.cancel();
    _contentCheckTimer = null;
    debugPrint('🔧 停止动态内容监控');
  }

  // 🔧 Flutter翻译架构 - 辅助函数

  /// 提取页面中的日语文本
  Future<List<String>> _extractJapaneseTexts() async {
    final result = await webViewController!.evaluateJavascript(
      source: '''
        (function() {
          const texts = [];
          const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
          );

          let node;
          while (node = walker.nextNode()) {
            const text = node.textContent.trim();
            // 提取所有日语文本，无字符长度限制
            if (text.length > 0 && /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text)) {
              texts.push(text);
            }
          }

          return JSON.stringify(texts);
        })();
      ''',
    );

    if (result != null) {
      try {
        final List<dynamic> textList = jsonDecode(result.toString());
        return textList.cast<String>();
      } catch (e) {
        debugPrint('🔧 JSON解析失败: $e');
        return [];
      }
    }
    return [];
  }

  /// Flutter调用百度翻译API
  Future<String?> _callBaiduAPI(String text) async {
    try {
      debugPrint('🔧 开始调用百度API翻译: "$text"');
      const String appId = '20250724002415106';
      const String secretKey = 'vET_uli_l70K5BZ9BU5U';
      const String endpoint =
          'https://fanyi-api.baidu.com/api/trans/vip/translate';

      final String salt = DateTime.now().millisecondsSinceEpoch.toString();
      final String sign = _generateBaiduSign(appId, text, salt, secretKey);
      debugPrint('🔧 生成签名: $sign');

      final Map<String, String> params = {
        'q': text,
        'from': 'jp',
        'to': 'zh',
        'appid': appId,
        'salt': salt,
        'sign': sign,
      };

      final uri = Uri.parse(endpoint).replace(queryParameters: params);
      debugPrint('🔧 请求URL: $uri');

      final response = await http.get(uri);
      debugPrint('🔧 API响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🔧 API响应内容: ${response.body}');
        final data = jsonDecode(response.body);
        if (data['trans_result'] != null && data['trans_result'].isNotEmpty) {
          final result = data['trans_result'][0]['dst'];
          debugPrint('🔧 翻译结果: "$text" → "$result"');
          return result;
        } else {
          debugPrint('🔧 API返回空结果: $data');
        }
      } else {
        debugPrint('🔧 API请求失败: ${response.statusCode} - ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('🔧 百度API调用异常: $e');
      return null;
    }
  }

  /// Flutter调用腾讯翻译API
  Future<String?> _callTencentAPI(String text) async {
    try {
      debugPrint('🔧 开始调用腾讯API翻译: "$text"');
      const String secretId = 'AKIDLxOdjdKrETYR7d0CGBvWw9D5kFgx3voh';
      const String secretKey = 'qcTAHDwSlrkZJxc7VikdM6f0FKBqIdkd';
      const String endpoint = 'https://tmt.tencentcloudapi.com/';

      // 腾讯云API签名算法（简化版本）
      final timestamp = (DateTime.now().millisecondsSinceEpoch ~/ 1000)
          .toString();
      final date = DateTime.now().toUtc().toIso8601String().substring(0, 10);

      final Map<String, dynamic> requestBody = {
        'Action': 'TextTranslate',
        'Version': '2018-03-21',
        'Region': 'ap-beijing',
        'SourceText': text,
        'Source': 'ja',
        'Target': 'zh',
        'ProjectId': 0,
      };

      // 简化的签名（实际使用中需要完整的TC3签名算法）
      final authorization =
          'TC3-HMAC-SHA256 Credential=$secretId/$date/tmt/tc3_request, SignedHeaders=content-type;host;x-tc-action, Signature=placeholder';

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/x-amz-json-1.0',
          'X-TC-Action': 'TextTranslate',
          'X-TC-Version': '2018-03-21',
          'X-TC-Region': 'ap-beijing',
          'X-TC-Timestamp': timestamp,
          'Authorization': authorization,
        },
        body: jsonEncode(requestBody),
      );

      debugPrint('🔧 腾讯API响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🔧 腾讯API响应内容: ${response.body}');
        final data = jsonDecode(response.body);
        if (data['Response'] != null &&
            data['Response']['TargetText'] != null) {
          final result = data['Response']['TargetText'];
          debugPrint('🔧 腾讯翻译结果: "$text" → "$result"');
          return result;
        } else {
          debugPrint('🔧 腾讯API返回空结果: $data');
        }
      } else {
        debugPrint('🔧 腾讯API请求失败: ${response.statusCode} - ${response.body}');
      }

      return null;
    } catch (e) {
      debugPrint('🔧 腾讯API调用异常: $e');
      return null;
    }
  }

  /// Flutter调用DeepSeek V3 API
  Future<String?> _callDeepSeekAPI(String text) async {
    try {
      debugPrint('🔧 开始调用DeepSeek V3翻译: "$text"');
      const String apiKey = 'sk-5bab1bc7de1c45749231a267a04279b7';
      const String endpoint = 'https://api.deepseek.com/v1/chat/completions';

      final Map<String, dynamic> requestBody = {
        'model': 'deepseek-chat',
        'messages': [
          {
            'role': 'system',
            'content': '你是一个专业的日语翻译助手，请将用户输入的日语文本翻译成中文。只返回翻译结果，不要添加任何解释。',
          },
          {'role': 'user', 'content': '请翻译：$text'},
        ],
        'temperature': 0.1,
        'max_tokens': 1000,
      };

      final response = await http.post(
        Uri.parse(endpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: jsonEncode(requestBody),
      );

      debugPrint('🔧 DeepSeek API响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🔧 DeepSeek API响应内容: ${response.body}');
        final data = jsonDecode(response.body);
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final result = data['choices'][0]['message']['content'].trim();
          debugPrint('🔧 DeepSeek翻译结果: "$text" → "$result"');
          return result;
        } else {
          debugPrint('🔧 DeepSeek API返回空结果: $data');
        }
      } else {
        debugPrint(
          '🔧 DeepSeek API请求失败: ${response.statusCode} - ${response.body}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('🔧 DeepSeek API调用异常: $e');
      return null;
    }
  }

  /// Flutter调用Gemini API
  Future<String?> _callGeminiAPI(String text) async {
    try {
      debugPrint('🔧 开始调用Gemini API翻译: "$text"');
      const String apiKey = 'AIzaSyBNzNueTKgbmjzU2zsPxASzr3klXyTTN8A';
      const String endpoint =
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';

      final Map<String, dynamic> requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '请将以下日语文本翻译成中文，只返回翻译结果，不要添加任何解释：$text'},
            ],
          },
        ],
        'generationConfig': {'temperature': 0.1, 'maxOutputTokens': 1000},
      };

      final response = await http.post(
        Uri.parse('$endpoint?key=$apiKey'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      debugPrint('🔧 Gemini API响应状态: ${response.statusCode}');

      if (response.statusCode == 200) {
        debugPrint('🔧 Gemini API响应内容: ${response.body}');
        final data = jsonDecode(response.body);
        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final result = data['candidates'][0]['content']['parts'][0]['text']
              .trim();
          debugPrint('🔧 Gemini翻译结果: "$text" → "$result"');
          return result;
        } else {
          debugPrint('🔧 Gemini API返回空结果: $data');
        }
      } else {
        debugPrint(
          '🔧 Gemini API请求失败: ${response.statusCode} - ${response.body}',
        );
      }

      return null;
    } catch (e) {
      debugPrint('🔧 Gemini API调用异常: $e');
      return null;
    }
  }

  /// 生成百度翻译签名
  String _generateBaiduSign(
    String appId,
    String query,
    String salt,
    String secretKey,
  ) {
    final String str = appId + query + salt + secretKey;
    final bytes = utf8.encode(str);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// 词典翻译（备用方案）
  String _translateWithDictionary(String text) {
    debugPrint('🔧 词典翻译输入: "$text"');
    final dictionary = {
      'こんにちは': '你好',
      'ありがとう': '谢谢',
      'おはよう': '早上好',
      'こんばんは': '晚上好',
      'さようなら': '再见',
      'はい': '是',
      'いいえ': '不是',
      'すみません': '对不起',
      '今日': '今天',
      '明日': '明天',
      '昨日': '昨天',
      '学校': '学校',
      '会社': '公司',
      '家': '家',
      '友達': '朋友',
      '先生': '老师',
      '学生': '学生',
    };

    if (dictionary.containsKey(text)) {
      final result = dictionary[text]!;
      debugPrint('🔧 词典完全匹配: "$text" → "$result"');
      return result;
    }

    for (final entry in dictionary.entries) {
      if (text.contains(entry.key)) {
        final result = text.replaceAll(entry.key, entry.value);
        debugPrint('🔧 词典部分匹配: "$text" → "$result"');
        return result;
      }
    }

    debugPrint('🔧 词典无匹配，保持原文: "$text"');
    return text;
  }

  /// 百度API真正批量翻译 - 合并文本一次性翻译
  Future<int> _batchTranslateWithBaiduAPI(List<String> texts) async {
    debugPrint('🔧 百度API真正批量翻译开始，共 ${texts.length} 个文本');

    // 将所有文本合并为一个大文本，用特殊分隔符分隔
    const separator = '|||SEPARATOR|||';
    final combinedText = texts.join(separator);
    debugPrint('🔧 合并文本长度: ${combinedText.length} 字符');

    // 一次性翻译整个合并文本
    final translatedCombined = await _callBaiduAPI(combinedText);

    final translationMap = <String, String>{};

    if (translatedCombined != null) {
      // 分割翻译结果
      final translatedTexts = translatedCombined.split(separator);
      debugPrint('🔧 翻译结果分割为 ${translatedTexts.length} 个文本');

      // 构建翻译映射
      for (int i = 0; i < texts.length && i < translatedTexts.length; i++) {
        final originalText = texts[i];
        final translatedText = translatedTexts[i].trim();

        if (translatedText.isNotEmpty && translatedText != originalText) {
          translationMap[originalText] = translatedText;

          // 显示前5个翻译结果
          if (translationMap.length <= 5) {
            debugPrint(
              '🔧 翻译 ${translationMap.length}: "$originalText" → "$translatedText"',
            );
          }
        }
      }
    } else {
      debugPrint('🔧 批量翻译失败，回退到逐个翻译');
      // 回退到逐个翻译
      for (final text in texts) {
        final translatedText = await _callBaiduAPI(text);
        if (translatedText != null && translatedText != text) {
          translationMap[text] = translatedText;
        }
        await Future.delayed(const Duration(milliseconds: 100));
      }
    }

    // 批量更新DOM
    if (translationMap.isNotEmpty) {
      await _batchUpdateDOM(translationMap);
    }

    debugPrint('🔧 百度API批量翻译完成，共翻译 ${translationMap.length} 个文本');
    return translationMap.length;
  }

  /// 词典批量翻译
  Future<int> _batchTranslateWithDictionary(List<String> texts) async {
    debugPrint('🔧 词典批量翻译开始，共 ${texts.length} 个文本');

    final translationMap = <String, String>{};

    for (final text in texts) {
      final translatedText = _translateWithDictionary(text);
      if (translatedText != text) {
        translationMap[text] = translatedText;

        // 显示前5个翻译结果
        if (translationMap.length <= 5) {
          debugPrint(
            '🔧 翻译 ${translationMap.length}: "$text" → "$translatedText"',
          );
        }
      }
    }

    // 批量更新DOM
    if (translationMap.isNotEmpty) {
      await _batchUpdateDOM(translationMap);
    }

    debugPrint('🔧 词典批量翻译完成，共翻译 ${translationMap.length} 个文本');
    return translationMap.length;
  }

  /// 腾讯API批量翻译
  Future<int> _batchTranslateWithTencentAPI(List<String> texts) async {
    debugPrint('🔧 腾讯API批量翻译开始，共 ${texts.length} 个文本');

    final translationMap = <String, String>{};

    // 腾讯API暂时使用逐个翻译（可以后续优化为批量）
    for (final text in texts) {
      final translatedText = await _callTencentAPI(text);
      if (translatedText != null && translatedText != text) {
        translationMap[text] = translatedText;

        // 显示前5个翻译结果
        if (translationMap.length <= 5) {
          debugPrint(
            '🔧 腾讯翻译 ${translationMap.length}: "$text" → "$translatedText"',
          );
        }
      }
      await Future.delayed(const Duration(milliseconds: 200)); // 避免API限制
    }

    // 批量更新DOM
    if (translationMap.isNotEmpty) {
      await _batchUpdateDOM(translationMap);
    }

    debugPrint('🔧 腾讯API批量翻译完成，共翻译 ${translationMap.length} 个文本');
    return translationMap.length;
  }

  /// DeepSeek V3批量翻译
  Future<int> _batchTranslateWithDeepSeekAPI(List<String> texts) async {
    debugPrint('🔧 DeepSeek V3批量翻译开始，共 ${texts.length} 个文本');

    final translationMap = <String, String>{};

    // DeepSeek V3暂时使用逐个翻译（可以后续优化为批量）
    for (final text in texts) {
      final translatedText = await _callDeepSeekAPI(text);
      if (translatedText != null && translatedText != text) {
        translationMap[text] = translatedText;

        // 显示前5个翻译结果
        if (translationMap.length <= 5) {
          debugPrint(
            '🔧 DeepSeek翻译 ${translationMap.length}: "$text" → "$translatedText"',
          );
        }
      }
      await Future.delayed(const Duration(milliseconds: 300)); // 避免API限制
    }

    // 批量更新DOM
    if (translationMap.isNotEmpty) {
      await _batchUpdateDOM(translationMap);
    }

    debugPrint('🔧 DeepSeek V3批量翻译完成，共翻译 ${translationMap.length} 个文本');
    return translationMap.length;
  }

  /// Gemini批量翻译
  Future<int> _batchTranslateWithGeminiAPI(List<String> texts) async {
    debugPrint('🔧 Gemini批量翻译开始，共 ${texts.length} 个文本');

    final translationMap = <String, String>{};

    // Gemini暂时使用逐个翻译（可以后续优化为批量）
    for (final text in texts) {
      final translatedText = await _callGeminiAPI(text);
      if (translatedText != null && translatedText != text) {
        translationMap[text] = translatedText;

        // 显示前5个翻译结果
        if (translationMap.length <= 5) {
          debugPrint(
            '🔧 Gemini翻译 ${translationMap.length}: "$text" → "$translatedText"',
          );
        }
      }
      await Future.delayed(const Duration(milliseconds: 250)); // 避免API限制
    }

    // 批量更新DOM
    if (translationMap.isNotEmpty) {
      await _batchUpdateDOM(translationMap);
    }

    debugPrint('🔧 Gemini批量翻译完成，共翻译 ${translationMap.length} 个文本');
    return translationMap.length;
  }

  /// API切换后重新翻译页面
  Future<void> _retranslateWithNewAPI() async {
    debugPrint('🔧 开始使用新API重新翻译页面');

    // 重置翻译状态，允许重新翻译
    setState(() {
      _hasTranslatedCurrentPage = false;
    });

    // 延迟一下再开始翻译，确保UI更新完成
    await Future.delayed(const Duration(milliseconds: 500));

    // 开始翻译
    _startTranslation();
  }

  /// 批量更新DOM中的文本 - 安全高效方案
  Future<void> _batchUpdateDOM(Map<String, String> translationMap) async {
    debugPrint('🔧 开始安全高效批量更新DOM，共 ${translationMap.length} 个翻译');

    // 方案1：TextNode精确定位（安全且高效）
    final success = await _textNodePreciseUpdate(translationMap);
    if (success) return;

    // 方案2：回退到分段更新
    await _segmentedBatchUpdate(translationMap);
  }

  /// TextNode精确定位更新（安全方案）- 增强调试版本
  Future<bool> _textNodePreciseUpdate(
    Map<String, String> translationMap,
  ) async {
    try {
      _addDebugMessage('🔧 开始TextNode翻译，映射数量: ${translationMap.length}');

      // 先测试页面是否可以访问
      final testResult = await webViewController!.evaluateJavascript(
        source: '''
          (function() {
            try {
              return {
                bodyExists: !!document.body,
                textNodes: document.createTreeWalker(document.body, NodeFilter.SHOW_TEXT).nextNode() ? 'found' : 'none',
                sampleText: document.body ? document.body.textContent.substring(0, 100) : 'no body'
              };
            } catch (e) {
              return { error: e.toString() };
            }
          })();
        ''',
      );
      _addDebugMessage('🔧 页面状态: ${testResult.toString()}');

      // 分批处理，每批20个文本（减少批次大小）
      const batchSize = 20;
      final entries = translationMap.entries.toList();
      int totalUpdated = 0;

      for (int i = 0; i < entries.length; i += batchSize) {
        final batch = entries.skip(i).take(batchSize);
        _addDebugMessage(
          '🔧 处理批次 ${(i ~/ batchSize) + 1}/${(entries.length / batchSize).ceil()}，包含 ${batch.length} 个翻译',
        );

        // 构建翻译映射
        final translationEntries = batch
            .map((entry) {
              final escapedOriginal = _escapeForJS(entry.key);
              final escapedTranslated = _escapeForJS(entry.value);
              return '"$escapedOriginal": "$escapedTranslated"';
            })
            .join(',\n    ');

        final result = await webViewController!.evaluateJavascript(
          source:
              '''
            (function() {
              try {
                const translationMap = {
                  $translationEntries
                };

                console.log('🔧 开始处理翻译映射，条目数:', Object.keys(translationMap).length);

                let updatedCount = 0;
                let totalTextNodes = 0;
                let matchedNodes = 0;

                // 创建文本节点遍历器
                const walker = document.createTreeWalker(
                  document.body,
                  NodeFilter.SHOW_TEXT,
                  null,
                  false
                );

                const nodesToUpdate = [];
                let node;

                // 收集需要更新的节点
                while (node = walker.nextNode()) {
                  totalTextNodes++;
                  const text = node.textContent.trim();

                  if (text.length > 0) {
                    // 检查是否匹配翻译映射
                    if (translationMap[text]) {
                      matchedNodes++;
                      nodesToUpdate.push({
                        node: node,
                        originalText: text,
                        newText: translationMap[text]
                      });
                      console.log('🔧 找到匹配文本:', text, '→', translationMap[text]);
                    }
                  }
                }

                console.log('🔧 遍历统计 - 总文本节点:', totalTextNodes, '匹配节点:', matchedNodes);

                // 批量更新节点
                nodesToUpdate.forEach((item, index) => {
                  try {
                    item.node.textContent = item.newText;
                    updatedCount++;
                    if (index < 3) {
                      console.log('🔧 更新节点', index + 1, ':', item.originalText, '→', item.newText);
                    }
                  } catch (e) {
                    console.error('🔧 更新节点失败:', e);
                  }
                });

                console.log('🔧 批次更新完成，成功更新:', updatedCount, '个节点');
                return {
                  updated: updatedCount,
                  totalNodes: totalTextNodes,
                  matched: matchedNodes
                };
              } catch (e) {
                console.error('🔧 TextNode更新异常:', e);
                return { error: e.toString() };
              }
            })();
          ''',
        );

        _addDebugMessage('🔧 批次结果: ${result.toString()}');

        if (result != null && result is Map) {
          final updated = (result['updated'] ?? 0) as int;
          final matched = (result['matched'] ?? 0) as int;
          final totalNodes = (result['totalNodes'] ?? 0) as int;
          totalUpdated += updated;
          final progress = ((i + batchSize) / entries.length * 100).round();
          _addDebugMessage(
            '🔧 批次完成: 更新$updated个，匹配$matched个，总节点$totalNodes个，进度$progress%',
          );
        }

        // 批次间暂停
        await Future.delayed(const Duration(milliseconds: 200));
      }

      _addDebugMessage('🔧 TextNode翻译完成，总共更新了 $totalUpdated 个文本');

      if (totalUpdated > 0) {
        _addDebugMessage('✅ 翻译成功！更新了 $totalUpdated 个文本');
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 翻译完成，更新了 $totalUpdated 个文本");',
        );
        return true;
      } else {
        _addDebugMessage('❌ 翻译失败：没有找到匹配的文本节点');
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 翻译完成，但没有找到匹配的文本节点");',
        );
        return false;
      }
    } catch (e) {
      debugPrint('🔧 TextNode更新失败: $e');
      await webViewController!.evaluateJavascript(
        source: 'alert("🔧 翻译更新失败: $e");',
      );
      return false;
    }
  }

  /// CSS选择器 + innerHTML超高速更新
  Future<bool> _cssInnerHTMLUpdate(Map<String, String> translationMap) async {
    try {
      debugPrint('🔧 使用innerHTML字符串替换方案');

      // 分批处理，避免JavaScript字符串过长
      const batchSize = 100;
      final entries = translationMap.entries.toList();
      int totalUpdated = 0;

      for (int i = 0; i < entries.length; i += batchSize) {
        final batch = entries.skip(i).take(batchSize);

        // 构建替换脚本
        final replacements = batch
            .map((entry) {
              final escapedOriginal = _escapeForJS(entry.key);
              final escapedTranslated = _escapeForJS(entry.value);
              return 'html = html.replace(new RegExp(">" + "$escapedOriginal" + "<", "g"), ">" + "$escapedTranslated" + "<");';
            })
            .join('\n        ');

        final result = await webViewController!.evaluateJavascript(
          source:
              '''
            (function() {
              try {
                let html = document.body.innerHTML;
                const originalHtml = html;

                $replacements

                if (html !== originalHtml) {
                  document.body.innerHTML = html;
                  return 1;
                }

                return 0;
              } catch (e) {
                console.error('🔧 innerHTML更新异常:', e);
                return -1;
              }
            })();
          ''',
        );

        if (result != null && result is int && result > 0) {
          totalUpdated += batch.length;
          debugPrint(
            '🔧 批次 ${(i ~/ batchSize) + 1}/${(entries.length / batchSize).ceil()} 完成',
          );
        }

        // 批次间暂停
        await Future.delayed(const Duration(milliseconds: 50));
      }

      if (totalUpdated > 0) {
        debugPrint('🔧 innerHTML超高速更新完成，处理了 $totalUpdated 个文本');
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 翻译完成，处理了 $totalUpdated 个文本");',
        );
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('🔧 innerHTML更新失败: $e');
      return false;
    }
  }

  /// 正则表达式转义
  String _escapeForRegex(String text) {
    return text
        .replaceAll('\\', '\\\\')
        .replaceAll('[', '\\[')
        .replaceAll(']', '\\]')
        .replaceAll('(', '\\(')
        .replaceAll(')', '\\)')
        .replaceAll('{', '\\{')
        .replaceAll('}', '\\}')
        .replaceAll('.', '\\.')
        .replaceAll('*', '\\*')
        .replaceAll('+', '\\+')
        .replaceAll('?', '\\?')
        .replaceAll('^', '\\^')
        .replaceAll('\$', '\\\$')
        .replaceAll('|', '\\|');
  }

  /// 快速批量更新（小量文本）
  Future<bool> _fastBatchUpdate(Map<String, String> translationMap) async {
    try {
      // 构建替换脚本
      final replacements = translationMap.entries
          .map((entry) {
            final escapedOriginal = _escapeForJS(entry.key);
            final escapedTranslated = _escapeForJS(entry.value);
            return 'replaceText("$escapedOriginal", "$escapedTranslated");';
          })
          .join('\n        ');

      final result = await webViewController!.evaluateJavascript(
        source:
            '''
          (function() {
            try {
              let updatedCount = 0;

              function replaceText(original, translated) {
                const walker = document.createTreeWalker(
                  document.body,
                  NodeFilter.SHOW_TEXT,
                  null,
                  false
                );

                let node;
                while (node = walker.nextNode()) {
                  if (node.textContent.trim() === original) {
                    node.textContent = translated;
                    updatedCount++;
                    break; // 找到第一个匹配就停止
                  }
                }
              }

              $replacements

              return updatedCount;
            } catch (e) {
              console.error('🔧 快速更新异常:', e);
              return -1;
            }
          })();
        ''',
      );

      if (result != null && result is int && result > 0) {
        debugPrint('🔧 快速批量更新完成，更新了 $result 个文本');
        await webViewController!.evaluateJavascript(
          source: 'alert("🔧 翻译完成，更新了 $result 个文本");',
        );
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('🔧 快速更新失败: $e');
      return false;
    }
  }

  /// 分段批量更新（大量文本）
  Future<void> _segmentedBatchUpdate(Map<String, String> translationMap) async {
    const segmentSize = 30;
    final entries = translationMap.entries.toList();
    int totalUpdated = 0;

    for (int i = 0; i < entries.length; i += segmentSize) {
      final segment = entries.skip(i).take(segmentSize);

      try {
        final replacements = segment
            .map((entry) {
              final escapedOriginal = _escapeForJS(entry.key);
              final escapedTranslated = _escapeForJS(entry.value);
              return 'replaceText("$escapedOriginal", "$escapedTranslated");';
            })
            .join('\n          ');

        final result = await webViewController!.evaluateJavascript(
          source:
              '''
            (function() {
              try {
                let updatedCount = 0;

                function replaceText(original, translated) {
                  const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                  );

                  let node;
                  while (node = walker.nextNode()) {
                    if (node.textContent.trim() === original) {
                      node.textContent = translated;
                      updatedCount++;
                      break;
                    }
                  }
                }

                $replacements

                return updatedCount;
              } catch (e) {
                console.error('🔧 分段更新异常:', e);
                return -1;
              }
            })();
          ''',
        );

        if (result != null && result is int && result >= 0) {
          totalUpdated += result;
          final progress = ((i + segmentSize) / entries.length * 100).round();
          debugPrint(
            '🔧 段 ${(i ~/ segmentSize) + 1}/${(entries.length / segmentSize).ceil()} 完成，进度: $progress%',
          );
        }
      } catch (e) {
        debugPrint('🔧 段更新异常: $e');
      }

      // 段间暂停
      await Future.delayed(const Duration(milliseconds: 50));
    }

    debugPrint('🔧 分段批量DOM更新完成，总共更新了 $totalUpdated 个文本');
    await webViewController!.evaluateJavascript(
      source: 'alert("🔧 翻译完成，更新了 $totalUpdated 个文本");',
    );
  }

  /// JavaScript字符串转义
  String _escapeForJS(String text) {
    return text
        .replaceAll('\\', '\\\\')
        .replaceAll('"', '\\"')
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t');
  }

  /// 添加调试信息
  void _addDebugMessage(String message) {
    setState(() {
      _debugMessages.add(
        '${DateTime.now().toString().substring(11, 19)} $message',
      );
      // 只保留最近50条消息
      if (_debugMessages.length > 50) {
        _debugMessages.removeAt(0);
      }
    });
    debugPrint(message);
  }

  /// 清空调试信息
  void _clearDebugMessages() {
    setState(() {
      _debugMessages.clear();
    });
  }

  /// 切换调试信息显示
  void _toggleDebugInfo() {
    setState(() {
      _showDebugInfo = !_showDebugInfo;
    });
  }

  /// 标签页更新管理器
  void _updateTabInfo({
    dynamic title,
    dynamic url,
    bool? isLoaded,
    bool? hasError,
  }) {
    try {
      final tabId = widget.tabId;
      if (tabId == null || tabId.isEmpty) {
        _addDebugMessage('🔧 标签页ID为空，跳过更新');
        return;
      }

      TabManager().batchUpdateTab(
        tabId,
        title: title,
        url: url,
        isLoaded: isLoaded,
        hasError: hasError,
      );
      _addDebugMessage(
        '🔧 标签页更新: 标题=$title, URL=$url, 已加载=$isLoaded, 错误=$hasError',
      );
    } catch (e) {
      _addDebugMessage('🔧 标签页更新失败: $e');
    }
  }

  /// 延迟更新标签页信息
  void _scheduleTabUpdate({
    dynamic title,
    dynamic url,
    bool? isLoaded,
    bool? hasError,
  }) {
    _tabUpdateTimer?.cancel();
    _tabUpdateTimer = Timer(const Duration(milliseconds: 200), () {
      _updateTabInfo(
        title: title,
        url: url,
        isLoaded: isLoaded,
        hasError: hasError,
      );
    });
  }

  /// 从WebView控制器获取并更新标签页信息
  Future<void> _fetchAndUpdateTabInfo() async {
    if (webViewController == null) return;

    try {
      final title = await webViewController!.getTitle();
      final url = await webViewController!.getUrl();

      _updateTabInfo(
        title: title,
        url: url?.toString(),
        isLoaded: !hasError && currentUrl != 'about:blank',
        hasError: hasError,
      );
    } catch (e) {
      _addDebugMessage('🔧 获取页面信息失败: $e');
    }
  }

  /// 启动定期标签页同步
  void _startTabSync() {
    _tabUpdateTimer?.cancel();
    _tabUpdateTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (mounted && webViewController != null) {
        _fetchAndUpdateTabInfo();
      } else {
        timer.cancel();
      }
    });
  }

  /// 构建调试信息面板
  Widget _buildDebugInfoPanel() {
    return Positioned(
      top: 100,
      right: 10,
      child: Container(
        width: 300,
        height: 400,
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.9),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.blue, width: 1),
        ),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(8),
              decoration: const BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(7),
                  topRight: Radius.circular(7),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.bug_report, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  const Text(
                    '翻译调试信息',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: _clearDebugMessages,
                    child: const Icon(
                      Icons.clear,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: _toggleDebugInfo,
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),
            // 调试信息列表
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: _debugMessages.length,
                itemBuilder: (context, index) {
                  final message = _debugMessages[index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Text(
                      message,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 11,
                        fontFamily: 'monospace',
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 分批更新DOM
  Future<void> _batchUpdateDOMInChunks(
    Map<String, String> translationMap,
  ) async {
    const chunkSize = 50;
    final entries = translationMap.entries.toList();
    int totalUpdated = 0;

    for (int i = 0; i < entries.length; i += chunkSize) {
      final chunk = entries.skip(i).take(chunkSize);

      for (final entry in chunk) {
        try {
          final success = await _updateSingleTextInDOM(entry.key, entry.value);
          if (success) {
            totalUpdated++;
          }
        } catch (e) {
          debugPrint('🔧 更新文本失败: "${entry.key}", 错误: $e');
        }
      }

      debugPrint('🔧 完成批次 ${(i ~/ chunkSize) + 1}，已更新 $totalUpdated 个文本');
      await Future.delayed(const Duration(milliseconds: 200));
    }

    debugPrint('🔧 分批DOM更新完成，总共更新了 $totalUpdated 个文本');
  }

  /// 单个文本DOM更新
  Future<bool> _updateSingleTextInDOM(
    String originalText,
    String translatedText,
  ) async {
    try {
      final escapedOriginal = originalText
          .replaceAll('\\', '\\\\')
          .replaceAll('"', '\\"')
          .replaceAll('\n', '\\n')
          .replaceAll('\r', '\\r')
          .replaceAll('\t', '\\t');
      final escapedTranslated = translatedText
          .replaceAll('\\', '\\\\')
          .replaceAll('"', '\\"')
          .replaceAll('\n', '\\n')
          .replaceAll('\r', '\\r')
          .replaceAll('\t', '\\t');

      final result = await webViewController!.evaluateJavascript(
        source:
            '''
          (function() {
            try {
              const walker = document.createTreeWalker(
                document.body,
                NodeFilter.SHOW_TEXT,
                null,
                false
              );

              let node;
              while (node = walker.nextNode()) {
                if (node.textContent.trim() === "$escapedOriginal") {
                  node.textContent = "$escapedTranslated";
                  return true;
                }
              }
              return false;
            } catch (e) {
              console.error('🔧 单个文本更新异常:', e);
              return false;
            }
          })();
        ''',
      );

      return result == true;
    } catch (e) {
      debugPrint('🔧 单个文本更新异常: $e');
      return false;
    }
  }

  /// 单个文本DOM更新（保留用于兼容）
  Future<void> _updateTextInDOM(
    String originalText,
    String translatedText,
  ) async {
    await _batchUpdateDOM({originalText: translatedText});
  }

  // 备份的完整翻译函数
  Future<int> _translatePageContentBackup() async {
    if (webViewController == null) return 0;

    final result = await webViewController!.evaluateJavascript(
      source: '''
      (function() {
        console.log('开始翻译页面内容');

        // 获取所有文本节点
        function getTextNodes(element) {
          const textNodes = [];
          const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
              acceptNode: function(node) {
                // 跳过脚本和样式标签
                if (node.parentNode.tagName === 'SCRIPT' ||
                    node.parentNode.tagName === 'STYLE') {
                  return NodeFilter.FILTER_REJECT;
                }
                // 只处理有实际内容的文本节点
                if (node.textContent.trim().length > 0) {
                  return NodeFilter.FILTER_ACCEPT;
                }
                return NodeFilter.FILTER_REJECT;
              }
            }
          );

          let node;
          while (node = walker.nextNode()) {
            textNodes.push(node);
          }
          return textNodes;
        }

        // 检查是否需要显示翻译进度指示器
        function shouldShowProgress(text, element) {
          // 句子结尾标点符号
          const sentenceEndings = ['。', '！', '？', '.', '!', '?'];
          const hasSentenceEnding = sentenceEndings.some(ending => text.endsWith(ending));

          // 标题元素
          const isHeading = /^H[1-6]/.test(element.tagName);

          // 长文本（超过10个字符）
          const isLongText = text.length > 10;

          return hasSentenceEnding || isHeading || isLongText;
        }

        // 日语语法处理规则
        window.grammarRules = {
          // 动词变位处理
          verbConjugations: {
            'します': 'する',
            'しました': 'する',
            'している': 'する',
            'していた': 'する',
            'してる': 'する',
            'した': 'する',
            'して': 'する',
            'しない': 'する',
            'しなかった': 'する',
            'できます': 'できる',
            'できました': 'できる',
            'できない': 'できる',
            'できなかった': 'できる',
            'います': 'いる',
            'いました': 'いる',
            'いない': 'いる',
            'いなかった': 'いる',
            'あります': 'ある',
            'ありました': 'ある',
            'ない': 'ある',
            'なかった': 'ある',
            'なります': 'なる',
            'なりました': 'なる',
            'ならない': 'なる',
            'ならなかった': 'なる'
          },

          // 形容词变化处理
          adjectiveConjugations: {
            'おいしい': '美味的',
            'おいしかった': '美味的',
            'おいしくない': '不美味的',
            'おいしくなかった': '不美味的',
            '大きい': '大的',
            '大きかった': '大的',
            '大きくない': '不大的',
            '大きくなかった': '不大的',
            '小さい': '小的',
            '小さかった': '小的',
            '小さくない': '不小的',
            '小さくなかった': '不小的',
            '新しい': '新的',
            '新しかった': '新的',
            '新しくない': '不新的',
            '新しくなかった': '不新的',
            '古い': '旧的',
            '古かった': '旧的',
            '古くない': '不旧的',
            '古くなかった': '不旧的'
          },

          // 助词处理
          particles: {
            'は': '(主题)',
            'が': '(主语)',
            'を': '(宾语)',
            'に': '(方向/时间)',
            'で': '(地点/方式)',
            'と': '(和)',
            'から': '(从)',
            'まで': '(到)',
            'より': '(比)',
            'について': '(关于)',
            'によって': '(由于)',
            'のために': '(为了)',
            'として': '(作为)',
            'という': '(叫做)',
            'といった': '(比如)',
            'など': '(等等)',
            'だけ': '(只)',
            'しか': '(只有)',
            'も': '(也)',
            'さえ': '(甚至)',
            'まで': '(连...都)',
            'ばかり': '(只是)',
            'くらい': '(大约)',
            'ほど': '(程度)'
          }
        };

        // 语法处理函数
        function processGrammar(text) {
          let processedText = text;

          // 1. 处理动词变位
          for (const [conjugated, base] of Object.entries(window.grammarRules.verbConjugations)) {
            if (processedText.includes(conjugated)) {
              const baseTranslation = window.translationDict[base];
              if (baseTranslation) {
                // 根据变位形式添加时态标记
                let tenseMarker = '';
                if (conjugated.includes('ました') || conjugated.includes('した')) {
                  tenseMarker = '(过去式)';
                } else if (conjugated.includes('ている') || conjugated.includes('てる')) {
                  tenseMarker = '(进行时)';
                } else if (conjugated.includes('ない')) {
                  tenseMarker = '(否定)';
                }
                processedText = processedText.replace(conjugated, baseTranslation + tenseMarker);
              }
            }
          }

          // 2. 处理形容词变化
          for (const [conjugated, translation] of Object.entries(window.grammarRules.adjectiveConjugations)) {
            if (processedText.includes(conjugated)) {
              processedText = processedText.replace(conjugated, translation);
            }
          }

          // 3. 处理助词（保留但添加说明）
          for (const [particle, meaning] of Object.entries(window.grammarRules.particles)) {
            if (processedText.includes(particle)) {
              // 不直接替换助词，而是在后面添加说明
              processedText = processedText.replace(
                new RegExp(particle, 'g'),
                particle + meaning
              );
            }
          }

          return processedText;
        }

        // 增强的翻译匹配函数
        function findTranslation(text) {
          // 0. 预处理：语法处理
          let processedText = processGrammar(text);

          // 1. 直接匹配
          if (window.translationDict[text]) {
            return window.translationDict[text];
          }

          // 2. 处理后的文本匹配
          if (processedText !== text && window.translationDict[processedText]) {
            return window.translationDict[processedText];
          }

          // 3. 去除标点符号后匹配
          const cleanText = text.replace(/[。！？.!?、，,\\s]/g, '');
          if (window.translationDict[cleanText]) {
            return window.translationDict[cleanText];
          }

          // 4. 智能分词匹配（按长度排序，优先匹配长词汇）
          const sortedEntries = Object.entries(window.translationDict)
            .sort(([a], [b]) => b.length - a.length);

          let result = processedText;
          let hasTranslation = false;

          // 优先处理长词汇，避免被短词汇错误分割
          for (const [japanese, chinese] of sortedEntries) {
            if (japanese.length > 2 && result.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          // 然后处理中等长度词汇
          for (const [japanese, chinese] of sortedEntries) {
            if (japanese.length === 2 && result.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          // 5. 如果有翻译，返回结果
          if (hasTranslation) {
            return result;
          }

          // 6. 单字符匹配（最后尝试）
          for (const [japanese, chinese] of Object.entries(window.translationDict)) {
            if (japanese.length === 1 && text.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          return hasTranslation ? result : null;
        }

        // 智能文本分割函数
        function smartTextSplit(text) {
          // 按句子分割
          const sentences = text.split(/([。！？.!?])/);
          const results = [];

          for (let i = 0; i < sentences.length; i += 2) {
            const sentence = sentences[i];
            const punctuation = sentences[i + 1] || '';
            if (sentence && sentence.trim()) {
              results.push(sentence.trim() + punctuation);
            }
          }

          return results.length > 0 ? results : [text];
        }

        // AI翻译API配置
        window.aiTranslationConfig = {
          // 百度翻译API配置
          baiduAPI: {
            appid: '20250724002415106', // 真实API配置
            key: 'vET_uli_l70K5BZ9BU5U',
            endpoint: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
            from: 'jp',
            to: 'zh'
          },

          // 有道翻译API配置
          youdaoAPI: {
            appKey: 'demo_appkey',
            appSecret: 'demo_secret',
            endpoint: 'https://openapi.youdao.com/api',
            from: 'ja',
            to: 'zh-CHS'
          },

          // 腾讯翻译API配置
          tencentAPI: {
            secretId: 'AKIDEUYP5ZqvmPs8E8pJZcS6WntmSvgQ5cv1',
            secretKey: 'yjHjKjvxmOnyylLlyylLitqeHjxymOn0',
            endpoint: 'https://tmt.tencentcloudapi.com/',
            region: 'ap-beijing',
            service: 'tmt',
            version: '2018-03-21',
            action: 'TextTranslate',
            from: 'ja',
            to: 'zh'
          },

          // API翻译开关
          useAPI: false, // 默认关闭，避免API调用费用
          fallbackToDict: true // API失败时回退到词典翻译
        };

        // AI翻译函数
        async function translateWithAI(text) {
          if (!window.aiTranslationConfig.useAPI) {
            return null; // API翻译未开启
          }

          try {
            // 尝试腾讯翻译API（优先）
            const tencentResult = await callTencentTranslateAPI(text);
            if (tencentResult) {
              return tencentResult;
            }

            // 腾讯失败，尝试百度翻译API
            const baiduResult = await callBaiduTranslateAPI(text);
            if (baiduResult) {
              return baiduResult;
            }

            // 百度失败，尝试有道翻译
            const youdaoResult = await callYoudaoTranslateAPI(text);
            if (youdaoResult) {
              return youdaoResult;
            }

            return null;
          } catch (error) {
            console.log('AI翻译失败:', error);
            return null;
          }
        }

        // 百度翻译API调用
        async function callBaiduTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.baiduAPI;
            const salt = Date.now().toString();
            const sign = generateBaiduSign(config.appid, text, salt, config.key);

            const params = new URLSearchParams({
              q: text,
              from: config.from,
              to: config.to,
              appid: config.appid,
              salt: salt,
              sign: sign
            });

            const response = await fetch(config.endpoint + '?' + params.toString());
            const data = await response.json();

            if (data.trans_result && data.trans_result.length > 0) {
              return data.trans_result[0].dst;
            }

            return null;
          } catch (error) {
            console.log('百度翻译API调用失败:', error);
            return null;
          }
        }

        // 腾讯翻译API调用
        async function callTencentTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.tencentAPI;
            const timestamp = Math.floor(Date.now() / 1000);
            const nonce = Math.floor(Math.random() * 1000000);

            // 构建请求参数
            const params = {
              Action: config.action,
              Version: config.version,
              Region: config.region,
              SourceText: text,
              Source: config.from,
              Target: config.to,
              ProjectId: 0
            };

            // 生成腾讯云签名
            const signature = generateTencentSignature(config, params, timestamp, nonce);

            const headers = {
              'Content-Type': 'application/json; charset=utf-8',
              'Authorization': signature,
              'X-TC-Action': config.action,
              'X-TC-Version': config.version,
              'X-TC-Region': config.region,
              'X-TC-Timestamp': timestamp.toString(),
              'X-TC-Language': 'zh-CN'
            };

            const response = await fetch(config.endpoint, {
              method: 'POST',
              headers: headers,
              body: JSON.stringify(params)
            });

            const data = await response.json();

            if (data.Response && data.Response.TargetText) {
              return data.Response.TargetText;
            }

            if (data.Response && data.Response.Error) {
              console.log('腾讯翻译API错误:', data.Response.Error);
            }

            return null;
          } catch (error) {
            console.log('腾讯翻译API调用失败:', error);
            return null;
          }
        }

        // 有道翻译API调用
        async function callYoudaoTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.youdaoAPI;
            const salt = Date.now().toString();
            const curtime = Math.round(Date.now() / 1000).toString();
            const sign = generateYoudaoSign(config.appKey, text, salt, curtime, config.appSecret);

            const params = new URLSearchParams({
              q: text,
              from: config.from,
              to: config.to,
              appKey: config.appKey,
              salt: salt,
              sign: sign,
              signType: 'v3',
              curtime: curtime
            });

            const response = await fetch(config.endpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
              },
              body: params.toString()
            });

            const data = await response.json();

            if (data.translation && data.translation.length > 0) {
              return data.translation[0];
            }

            return null;
          } catch (error) {
            console.log('有道翻译API调用失败:', error);
            return null;
          }
        }

        // 百度翻译签名生成
        function generateBaiduSign(appid, query, salt, key) {
          const str = appid + query + salt + key;
          return md5(str);
        }

        // 有道翻译签名生成
        function generateYoudaoSign(appKey, query, salt, curtime, appSecret) {
          const str = appKey + truncate(query) + salt + curtime + appSecret;
          return sha256(str);
        }

        // 有道翻译查询截断
        function truncate(query) {
          const len = query.length;
          if (len <= 20) return query;
          return query.substring(0, 10) + len + query.substring(len - 10, len);
        }

        // MD5实现（用于百度翻译签名）
        function md5(string) {
          function rotateLeft(value, amount) {
            var lbits = (value << amount) | (value >>> (32 - amount));
            return lbits;
          }

          function addUnsigned(x, y) {
            var x4, y4, x8, y8, result;
            x8 = (x & 0x80000000);
            y8 = (y & 0x80000000);
            x4 = (x & 0x40000000);
            y4 = (y & 0x40000000);
            result = (x & 0x3FFFFFFF) + (y & 0x3FFFFFFF);
            if (x4 & y4) {
              return (result ^ 0x80000000 ^ x8 ^ y8);
            }
            if (x4 | y4) {
              if (result & 0x40000000) {
                return (result ^ 0xC0000000 ^ x8 ^ y8);
              } else {
                return (result ^ 0x40000000 ^ x8 ^ y8);
              }
            } else {
              return (result ^ x8 ^ y8);
            }
          }

          function F(x, y, z) { return (x & y) | ((~x) & z); }
          function G(x, y, z) { return (x & z) | (y & (~z)); }
          function H(x, y, z) { return (x ^ y ^ z); }
          function I(x, y, z) { return (y ^ (x | (~z))); }

          function FF(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function GG(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function HH(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function II(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function convertToWordArray(string) {
            var wordArray = [];
            var messageLength = string.length;
            var numberOfWords = (((messageLength + 8) - ((messageLength + 8) % 64)) / 64 + 1) * 16;
            for (var i = 0; i < numberOfWords; i++) {
              wordArray[i] = 0;
            }
            var bytePosition = 0;
            var byteCount = 0;
            while (byteCount < messageLength) {
              var wordIndex = (byteCount - (byteCount % 4)) / 4;
              bytePosition = (byteCount % 4) * 8;
              wordArray[wordIndex] = (wordArray[wordIndex] | (string.charCodeAt(byteCount) << bytePosition));
              byteCount++;
            }
            var wordIndex = (byteCount - (byteCount % 4)) / 4;
            bytePosition = (byteCount % 4) * 8;
            wordArray[wordIndex] = wordArray[wordIndex] | (0x80 << bytePosition);
            wordArray[numberOfWords - 2] = messageLength << 3;
            wordArray[numberOfWords - 1] = messageLength >>> 29;
            return wordArray;
          }

          function wordToHex(value) {
            var wordToHexValue = "", wordToHexValueTemp = "", byte, count;
            for (count = 0; count <= 3; count++) {
              byte = (value >>> (count * 8)) & 255;
              wordToHexValueTemp = "0" + byte.toString(16);
              wordToHexValue = wordToHexValue + wordToHexValueTemp.substr(wordToHexValueTemp.length - 2, 2);
            }
            return wordToHexValue;
          }

          var x = convertToWordArray(string);
          var a = 0x67452301; var b = 0xEFCDAB89; var c = 0x98BADCFE; var d = 0x10325476;

          for (var k = 0; k < x.length; k += 16) {
            var AA = a; var BB = b; var CC = c; var DD = d;
            a = FF(a, b, c, d, x[k + 0], 7, 0xD76AA478);
            d = FF(d, a, b, c, x[k + 1], 12, 0xE8C7B756);
            c = FF(c, d, a, b, x[k + 2], 17, 0x242070DB);
            b = FF(b, c, d, a, x[k + 3], 22, 0xC1BDCEEE);
            a = FF(a, b, c, d, x[k + 4], 7, 0xF57C0FAF);
            d = FF(d, a, b, c, x[k + 5], 12, 0x4787C62A);
            c = FF(c, d, a, b, x[k + 6], 17, 0xA8304613);
            b = FF(b, c, d, a, x[k + 7], 22, 0xFD469501);
            a = FF(a, b, c, d, x[k + 8], 7, 0x698098D8);
            d = FF(d, a, b, c, x[k + 9], 12, 0x8B44F7AF);
            c = FF(c, d, a, b, x[k + 10], 17, 0xFFFF5BB1);
            b = FF(b, c, d, a, x[k + 11], 22, 0x895CD7BE);
            a = FF(a, b, c, d, x[k + 12], 7, 0x6B901122);
            d = FF(d, a, b, c, x[k + 13], 12, 0xFD987193);
            c = FF(c, d, a, b, x[k + 14], 17, 0xA679438E);
            b = FF(b, c, d, a, x[k + 15], 22, 0x49B40821);
            a = GG(a, b, c, d, x[k + 1], 5, 0xF61E2562);
            d = GG(d, a, b, c, x[k + 6], 9, 0xC040B340);
            c = GG(c, d, a, b, x[k + 11], 14, 0x265E5A51);
            b = GG(b, c, d, a, x[k + 0], 20, 0xE9B6C7AA);
            a = GG(a, b, c, d, x[k + 5], 5, 0xD62F105D);
            d = GG(d, a, b, c, x[k + 10], 9, 0x2441453);
            c = GG(c, d, a, b, x[k + 15], 14, 0xD8A1E681);
            b = GG(b, c, d, a, x[k + 4], 20, 0xE7D3FBC8);
            a = GG(a, b, c, d, x[k + 9], 5, 0x21E1CDE6);
            d = GG(d, a, b, c, x[k + 14], 9, 0xC33707D6);
            c = GG(c, d, a, b, x[k + 3], 14, 0xF4D50D87);
            b = GG(b, c, d, a, x[k + 8], 20, 0x455A14ED);
            a = GG(a, b, c, d, x[k + 13], 5, 0xA9E3E905);
            d = GG(d, a, b, c, x[k + 2], 9, 0xFCEFA3F8);
            c = GG(c, d, a, b, x[k + 7], 14, 0x676F02D9);
            b = GG(b, c, d, a, x[k + 12], 20, 0x8D2A4C8A);
            a = HH(a, b, c, d, x[k + 5], 4, 0xFFFA3942);
            d = HH(d, a, b, c, x[k + 8], 11, 0x8771F681);
            c = HH(c, d, a, b, x[k + 11], 16, 0x6D9D6122);
            b = HH(b, c, d, a, x[k + 14], 23, 0xFDE5380C);
            a = HH(a, b, c, d, x[k + 1], 4, 0xA4BEEA44);
            d = HH(d, a, b, c, x[k + 4], 11, 0x4BDECFA9);
            c = HH(c, d, a, b, x[k + 7], 16, 0xF6BB4B60);
            b = HH(b, c, d, a, x[k + 10], 23, 0xBEBFBC70);
            a = HH(a, b, c, d, x[k + 13], 4, 0x289B7EC6);
            d = HH(d, a, b, c, x[k + 0], 11, 0xEAA127FA);
            c = HH(c, d, a, b, x[k + 3], 16, 0xD4EF3085);
            b = HH(b, c, d, a, x[k + 6], 23, 0x4881D05);
            a = HH(a, b, c, d, x[k + 9], 4, 0xD9D4D039);
            d = HH(d, a, b, c, x[k + 12], 11, 0xE6DB99E5);
            c = HH(c, d, a, b, x[k + 15], 16, 0x1FA27CF8);
            b = HH(b, c, d, a, x[k + 2], 23, 0xC4AC5665);
            a = II(a, b, c, d, x[k + 0], 6, 0xF4292244);
            d = II(d, a, b, c, x[k + 7], 10, 0x432AFF97);
            c = II(c, d, a, b, x[k + 14], 15, 0xAB9423A7);
            b = II(b, c, d, a, x[k + 5], 21, 0xFC93A039);
            a = II(a, b, c, d, x[k + 12], 6, 0x655B59C3);
            d = II(d, a, b, c, x[k + 3], 10, 0x8F0CCC92);
            c = II(c, d, a, b, x[k + 10], 15, 0xFFEFF47D);
            b = II(b, c, d, a, x[k + 1], 21, 0x85845DD1);
            a = II(a, b, c, d, x[k + 8], 6, 0x6FA87E4F);
            d = II(d, a, b, c, x[k + 15], 10, 0xFE2CE6E0);
            c = II(c, d, a, b, x[k + 6], 15, 0xA3014314);
            b = II(b, c, d, a, x[k + 13], 21, 0x4E0811A1);
            a = II(a, b, c, d, x[k + 4], 6, 0xF7537E82);
            d = II(d, a, b, c, x[k + 11], 10, 0xBD3AF235);
            c = II(c, d, a, b, x[k + 2], 15, 0x2AD7D2BB);
            b = II(b, c, d, a, x[k + 9], 21, 0xEB86D391);
            a = addUnsigned(a, AA);
            b = addUnsigned(b, BB);
            c = addUnsigned(c, CC);
            d = addUnsigned(d, DD);
          }

          return (wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d)).toLowerCase();
        }

        // 腾讯云签名生成
        function generateTencentSignature(config, params, timestamp, nonce) {
          // 腾讯云API签名算法v3
          const algorithm = 'TC3-HMAC-SHA256';
          const service = config.service;
          const host = config.endpoint.replace('https://', '');
          const date = new Date(timestamp * 1000).toISOString().substr(0, 10);

          // 1. 拼接规范请求串
          const httpRequestMethod = 'POST';
          const canonicalUri = '/';
          const canonicalQueryString = '';
          const canonicalHeaders = 'content-type:application/json; charset=utf-8\\n' +
                                   'host:' + host + '\\n';
          const signedHeaders = 'content-type;host';
          const hashedRequestPayload = sha256(JSON.stringify(params));

          const canonicalRequest = httpRequestMethod + '\\n' +
                                  canonicalUri + '\\n' +
                                  canonicalQueryString + '\\n' +
                                  canonicalHeaders + '\\n' +
                                  signedHeaders + '\\n' +
                                  hashedRequestPayload;

          // 2. 拼接待签名字符串
          const credentialScope = date + '/' + service + '/tc3_request';
          const hashedCanonicalRequest = sha256(canonicalRequest);
          const stringToSign = algorithm + '\\n' +
                              timestamp + '\\n' +
                              credentialScope + '\\n' +
                              hashedCanonicalRequest;

          // 3. 计算签名
          const secretDate = hmacSha256(date, 'TC3' + config.secretKey);
          const secretService = hmacSha256(service, secretDate);
          const secretSigning = hmacSha256('tc3_request', secretService);
          const signature = hmacSha256(stringToSign, secretSigning);

          // 4. 拼接 Authorization
          const authorization = algorithm + ' ' +
                               'Credential=' + config.secretId + '/' + credentialScope + ', ' +
                               'SignedHeaders=' + signedHeaders + ', ' +
                               'Signature=' + signature;

          return authorization;
        }

        // HMAC-SHA256实现
        function hmacSha256(message, key) {
          // 简化实现，实际应用中需要使用完整的HMAC-SHA256
          return sha256(key + message);
        }

        // SHA256实现（简化版）
        function sha256(string) {
          // 简化的SHA256实现，实际应用中需要使用完整的SHA256
          let hash = 0;
          if (string.length === 0) return hash.toString(16);
          for (let i = 0; i < string.length; i++) {
            const char = string.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
          }
          return Math.abs(hash).toString(16).padStart(64, '0');
        }

        // 文本分析和预处理
        function analyzeText(text) {
          return {
            length: text.length,
            hasJapanese: /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text),
            hasKanji: /[\u4E00-\u9FAF]/.test(text),
            hasHiragana: /[\u3040-\u309F]/.test(text),
            hasKatakana: /[\u30A0-\u30FF]/.test(text),
            isNumber: /^[\\d\\s,.\-+()]+\$/.test(text),
            isPunctuation: /^[。！？.!?、，,\\s\-_()（）「」『』【】〈〉《》]+\$/.test(text),
            isUrl: /^https?:\\/\\//.test(text),
            isEmail: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+\$/.test(text),
            wordCount: text.split(/\s+/).length,
            complexity: calculateComplexity(text)
          };
        }

        // 计算文本复杂度
        function calculateComplexity(text) {
          let score = 0;

          // 长度因子
          if (text.length > 20) score += 2;
          else if (text.length > 10) score += 1;

          // 汉字因子
          const kanjiCount = (text.match(/[\u4E00-\u9FAF]/g) || []).length;
          score += Math.min(kanjiCount * 0.5, 3);

          // 语法复杂度
          if (text.includes('について') || text.includes('によって')) score += 1;
          if (text.includes('という') || text.includes('といった')) score += 1;
          if (text.includes('ために') || text.includes('として')) score += 1;

          return Math.min(score, 5); // 最大复杂度为5
        }

        // 智能翻译策略选择
        function selectTranslationStrategy(text, analysis) {
          // 跳过非日语文本
          if (!analysis.hasJapanese) {
            return 'skip';
          }

          // 跳过纯标点符号
          if (analysis.isPunctuation) {
            return 'skip';
          }

          // 跳过数字、URL、邮箱
          if (analysis.isNumber || analysis.isUrl || analysis.isEmail) {
            return 'skip';
          }

          // 简单文本优先使用词典
          if (analysis.complexity <= 2 && analysis.length <= 10) {
            return 'dictionary_only';
          }

          // 复杂文本使用混合策略
          if (analysis.complexity >= 3 || analysis.length > 15) {
            return 'hybrid';
          }

          // 默认使用词典优先
          return 'dictionary_first';
        }

        // 优化的混合翻译函数
        async function hybridTranslate(text) {
          // 1. 文本分析
          const analysis = analyzeText(text);
          const strategy = selectTranslationStrategy(text, analysis);

          // 2. 根据策略选择翻译方法
          switch (strategy) {
            case 'skip':
              return {
                result: text,
                source: 'skipped',
                confidence: 'high',
                reason: '非日语内容或特殊格式'
              };

            case 'dictionary_only':
              const dictResult = findTranslation(text);
              if (dictResult && dictResult !== text) {
                return {
                  result: dictResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'dictionary_only'
                };
              }
              break;

            case 'hybrid':
              // 先尝试词典，再尝试AI
              const hybridDictResult = findTranslation(text);
              if (hybridDictResult && hybridDictResult !== text) {
                return {
                  result: hybridDictResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'hybrid_dict'
                };
              }

              // 词典失败，尝试AI翻译
              if (window.aiTranslationConfig.useAPI) {
                const aiResult = await translateWithAI(text);
                if (aiResult) {
                  return {
                    result: aiResult,
                    source: 'ai',
                    confidence: 'medium',
                    strategy: 'hybrid_ai'
                  };
                }
              }
              break;

            case 'dictionary_first':
            default:
              const defaultResult = findTranslation(text);
              if (defaultResult && defaultResult !== text) {
                return {
                  result: defaultResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'dictionary_first'
                };
              }
              break;
          }

          // 3. 所有策略都失败，返回原文
          return {
            result: text,
            source: 'none',
            confidence: 'low',
            reason: '无法翻译'
          };
        }

        // 翻译缓存系统
        window.translationCache = new Map();

        // 原文保存系统
        window.originalTexts = new Map();
        window.translatedNodes = new Set();

        // 翻译统计
        window.translationStats = {
          total: 0,
          dictionary: 0,
          ai: 0,
          skipped: 0,
          cached: 0,
          failed: 0
        };

        // 缓存翻译结果
        function cacheTranslation(original, result) {
          if (original && result && result.source !== 'none') {
            window.translationCache.set(original, result);
          }
        }

        // 获取缓存的翻译
        function getCachedTranslation(text) {
          return window.translationCache.get(text);
        }

        // 保存原文
        function saveOriginalText(node, originalText) {
          if (!window.originalTexts.has(node)) {
            window.originalTexts.set(node, originalText);
            window.translatedNodes.add(node);
          }
        }

        // 恢复所有原文
        function restoreAllOriginalTexts() {
          let restoredCount = 0;

          window.originalTexts.forEach((originalText, node) => {
            try {
              if (node && node.parentNode) {
                node.textContent = originalText;
                restoredCount++;
              }
            } catch (error) {
              console.log('恢复节点失败:', error);
            }
          });

          // 清理保存的数据
          window.originalTexts.clear();
          window.translatedNodes.clear();

          console.log('已恢复 ' + restoredCount + ' 个文本节点');
          return restoredCount;
        }

        // 检查是否有已翻译的内容
        function hasTranslatedContent() {
          return window.translatedNodes.size > 0;
        }

        // 移除了智能分段函数，使用简单的文本节点翻译

        // 智能翻译结果处理
        function processTranslationResult(result, originalText) {
          let displayText = result.result;

          // 根据保留原文设置决定显示格式
          if (window.aiTranslationConfig.keepOriginalText && result.source !== 'skipped' && result.source !== 'none') {
            // 双语对照格式
            displayText = originalText + '\\n' + displayText;
          }

          // 根据翻译来源添加标记
          switch (result.source) {
            case 'dictionary':
              displayText += ' 📚';
              break;
            case 'ai':
              displayText += ' 🤖';
              break;
            case 'skipped':
              return originalText; // 跳过的文本不显示标记
            case 'none':
              return originalText; // 无法翻译的保持原文
          }

          // 更新统计
          window.translationStats.total++;
          window.translationStats[result.source]++;

          return displayText;
        }

        // 批量翻译优化
        async function batchTranslateWithCache(textNodes) {
          const results = [];
          const uncachedNodes = [];

          // 1. 检查缓存
          for (const node of textNodes) {
            const text = node.textContent.trim();
            const cached = getCachedTranslation(text);

            if (cached) {
              results.push({
                node: node,
                result: cached,
                fromCache: true
              });
              window.translationStats.cached++;
            } else {
              uncachedNodes.push(node);
            }
          }

          // 2. 翻译未缓存的文本
          const translationPromises = uncachedNodes.map(async (node) => {
            const text = node.textContent.trim();
            const result = await hybridTranslate(text);

            // 缓存结果
            cacheTranslation(text, result);

            return {
              node: node,
              result: result,
              fromCache: false
            };
          });

          const newResults = await Promise.all(translationPromises);
          results.push(...newResults);

          return results;
        }

        // 简化的翻译文本节点函数（移除分段机制）
        async function translateTextNodes() {
          // 立即显示开始提示
          alert('翻译函数开始执行！');

          const textNodes = getTextNodes(document.body);
          alert('找到文本节点: ' + textNodes.length + '个');

          // 过滤有效的文本节点
          const validNodes = textNodes.filter(node => {
            const text = node.textContent.trim();
            return text.length > 0 && shouldTranslate(text);
          });

          alert('有效节点: ' + validNodes.length + '个');

          if (validNodes.length === 0) {
            alert('没有找到需要翻译的文本');
            return 0;
          }

          // 显示前3个文本内容
          let sampleText = '前3个文本:\n';
          for (let i = 0; i < Math.min(3, validNodes.length); i++) {
            const text = validNodes[i].textContent.trim();
            sampleText += (i + 1) + '. ' + text.substring(0, 30) + '\n';
          }
          alert(sampleText);

          // 实际执行翻译
          let translatedCount = 0;

          for (let i = 0; i < validNodes.length; i++) {
            const node = validNodes[i];
            const originalText = node.textContent.trim();

            try {
              // 保存原文
              saveOriginalText(node, originalText);

              // 尝试词典翻译
              const dictResult = getDictionaryTranslation(originalText);
              if (dictResult) {
                node.textContent = dictResult;
                translatedCount++;

                // 显示前几个翻译结果
                if (translatedCount <= 3) {
                  alert('翻译成功 ' + translatedCount + ':\n原文: ' + originalText.substring(0, 20) + '\n译文: ' + dictResult);
                }
              }
            } catch (error) {
              console.log('翻译节点失败:', error);
            }
          }

          alert('翻译完成！共翻译 ' + translatedCount + ' 个文本');
          return translatedCount;

          let translatedCount = 0;

          // 直接处理每个文本节点
          for (const node of validNodes) {
            const originalText = node.textContent.trim();
            const element = node.parentNode;

            // 创建进度指示器
            const needsProgress = shouldShowProgress(originalText, element);
            let progressId = null;

            if (needsProgress) {
              progressId = 'translate-progress-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
              const progressSpan = document.createElement('span');
              progressSpan.id = progressId;
              progressSpan.innerHTML = '<span class="translate-progress"></span>';
              node.parentNode.insertBefore(progressSpan, node.nextSibling);
            }

            try {
              // 检查缓存
              let result = getCachedTranslation(originalText);

              if (result) {
                window.translationStats.cached++;
              } else {
                // 先测试简单的词典翻译
                const dictResult = findTranslation(originalText);

                if (dictResult && dictResult !== originalText) {
                  result = {
                    result: dictResult,
                    source: 'dictionary',
                    confidence: 'high'
                  };

                  // 显示翻译成功信息
                  if (translatedCount < 3) { // 只显示前3个翻译结果
                    showDebugInfo('翻译成功:\n原文: ' + originalText.substring(0, 15) + '\n译文: ' + dictResult);
                  }
                } else {
                  // 进行混合翻译
                  result = await hybridTranslate(originalText);

                  if (result.source === 'ai' && translatedCount < 3) {
                    showDebugInfo('AI翻译:\n原文: ' + originalText.substring(0, 15) + '\n译文: ' + result.result);
                  }
                }

                cacheTranslation(originalText, result);
              }

              // 处理翻译结果
              if (result.source !== 'skipped' && result.source !== 'none') {
                const displayText = processTranslationResult(result, originalText);

                // 保存原文
                saveOriginalText(node, originalText);

                // 应用翻译结果
                const delay = needsProgress ? 300 : 0;

                setTimeout(() => {
                  node.textContent = displayText;

                  // 移除进度指示器
                  if (progressId) {
                    const progressElement = document.getElementById(progressId);
                    if (progressElement) {
                      progressElement.remove();
                    }
                  }
                }, delay);

                translatedCount++;
              } else {
                // 没有翻译，移除进度指示器
                if (progressId) {
                  setTimeout(() => {
                    const progressElement = document.getElementById(progressId);
                    if (progressElement) {
                      progressElement.remove();
                    }
                  }, 100);
                }
              }
            } catch (error) {
              console.log('翻译节点失败:', error);
              // 移除进度指示器
              if (progressId) {
                setTimeout(() => {
                  const progressElement = document.getElementById(progressId);
                  if (progressElement) {
                    progressElement.remove();
                  }
                }, 100);
              }
            }
          }

          // 显示翻译统计
          console.log('翻译完成统计:', {
            '总计': window.translationStats.total,
            '词典': window.translationStats.dictionary,
            'AI': window.translationStats.ai,
            '跳过': window.translationStats.skipped,
            '缓存': window.translationStats.cached,
            '失败': window.translationStats.failed
          });

          return translatedCount;
        }

        // 显示翻译状态和调试信息
        function showTranslationStatus(message, type = 'info') {
          // 移除现有状态
          const existing = document.getElementById('translation-status');
          if (existing) {
            existing.remove();
          }

          const statusDiv = document.createElement('div');
          statusDiv.id = 'translation-status';
          statusDiv.className = 'translation-status';

          let bgColor = 'rgba(0,0,0,0.8)';
          if (type === 'success') bgColor = 'rgba(76,175,80,0.9)';
          if (type === 'error') bgColor = 'rgba(244,67,54,0.9)';
          if (type === 'debug') bgColor = 'rgba(33,150,243,0.9)';

          statusDiv.style.background = bgColor;
          statusDiv.textContent = message;
          document.body.appendChild(statusDiv);

          return statusDiv;
        }

        // 显示调试信息
        function showDebugInfo(info) {
          const debugDiv = document.createElement('div');
          debugDiv.style.cssText = `
            position: fixed;
            top: 50px;
            left: 10px;
            right: 10px;
            background: rgba(33,150,243,0.95);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 10002;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
          `;
          debugDiv.textContent = info;
          document.body.appendChild(debugDiv);

          // 5秒后自动移除
          setTimeout(() => {
            if (debugDiv.parentNode) {
              debugDiv.remove();
            }
          }, 5000);
        }

        // 批量翻译优化函数
        async function batchTranslate() {
          const startTime = Date.now();

          // 显示开始翻译状态
          showTranslationStatus('正在翻译页面内容...');

          const count = await translateTextNodes();
          const endTime = Date.now();

          console.log('翻译性能统计: ' + count + ' 个文本，耗时 ' + (endTime - startTime) + 'ms');

          // 显示翻译完成状态
          if (count > 0) {
            const statusDiv = showTranslationStatus('翻译完成: ' + count + ' 个文本', 'success');
            setTimeout(() => {
              if (statusDiv && statusDiv.parentNode) {
                statusDiv.remove();
              }
            }, 2000);
          } else {
            const statusDiv = showTranslationStatus('未找到需要翻译的内容', 'info');
            setTimeout(() => {
              if (statusDiv && statusDiv.parentNode) {
                statusDiv.remove();
              }
            }, 1500);
          }

          return count;
        }

        // 执行翻译（使用最简单的测试版本）
        try {
          alert('开始执行JavaScript翻译逻辑');

          // 最简单的测试：直接返回一个固定数字
          const testResult = 999;
          alert('JavaScript执行成功，返回: ' + testResult);
          return testResult;

        } catch (error) {
          alert('JavaScript执行出错: ' + error.message);
          return -1;
        }
      })();
    ''',
    );

    // 返回翻译数量
    return result is int ? result : 0;
  }

  // 应用生命周期变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('📱 应用回到前台');
        _clearJavaScriptBlockage();
        break;
      case AppLifecycleState.paused:
        debugPrint('📱 应用进入后台');
        _pauseAllOperations();
        break;
      default:
        break;
    }
  }

  // 清理JavaScript阻塞
  void _clearJavaScriptBlockage() {
    if (webViewController != null) {
      webViewController!
          .evaluateJavascript(
            source: '''
        (function() {
          for (let i = 1; i < 10000; i++) {
            clearTimeout(i);
            clearInterval(i);
          }
          if (window.stopAllOperations) {
            window.stopAllOperations();
          }
          console.log('JavaScript阻塞已清理');
        })();
        ''',
          )
          .catchError((e) {
            debugPrint('清理JavaScript阻塞失败: $e');
          });
    }
  }

  // 暂停所有操作
  void _pauseAllOperations() {
    _operationScheduler.forceReset();

    if (webViewController != null) {
      webViewController!
          .evaluateJavascript(
            source: '''
        (function() {
          window.isPaused = true;
          for (let i = 1; i < 10000; i++) {
            clearTimeout(i);
            clearInterval(i);
          }
          console.log('所有操作已暂停');
        })();
        ''',
          )
          .catchError((e) {
            debugPrint('暂停操作失败: $e');
          });
    }
  }

  // 6. 获取错误信息
  String _getErrorMessage(WebResourceErrorType errorType) {
    return '网页无法打开'; // 简化错误信息
  }

  // 4. 简洁的错误页面
  Widget _buildErrorPage() {
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 简洁的错误图标
            Icon(Icons.wifi_off_rounded, size: 80, color: Colors.grey[400]),

            const SizedBox(height: 24),

            // 错误标题
            Text(
              '网页无法打开',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),

            const SizedBox(height: 12),

            // 错误描述
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),

            const SizedBox(height: 32),

            // 简洁的重试按钮
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  hasError = false;
                  isLoading = true;
                });
                webViewController?.reload();
              },
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('重新加载'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 新标签页界面：让用户在地址栏输入网址
  Widget _buildNewTabPage() {
    return Container(
      color: Colors.grey[50],
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 新标签页图标
            Icon(Icons.tab, size: 80, color: Colors.grey[400]),

            const SizedBox(height: 24),

            // 新标签页标题
            Text(
              '新标签页',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),

            const SizedBox(height: 12),

            // 提示文字
            Text(
              '点击上方地址栏输入网址或搜索内容',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false, // 2. 去掉地址栏的返回按钮
        title: Container(
          height: 36,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Row(
            children: [
              const SizedBox(width: 12),
              Icon(Icons.lock, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              // 2. 地址栏：所有页面都直接可编辑，不弹窗
              Expanded(child: _buildAddressInput()),
              const SizedBox(width: 8),

              // 网络优化按钮（仅图标）
              GestureDetector(
                onTap: _toggleNetworkOptimize,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.vpn_key,
                          size: 18,
                          color: isNetworkOptimized
                              ? Colors.green
                              : Colors.blue,
                        ),
                      ),
                      if (isNetworkOptimized)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // AI翻译按钮（纯图标）
              GestureDetector(
                onTap: _toggleAITranslate,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.translate,
                          size: 18,
                          color: isAITranslateEnabled
                              ? Colors.blue
                              : Colors.purple,
                        ),
                      ),
                      if (isAITranslateEnabled)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // 翻译设置按钮（纯图标）
              GestureDetector(
                onTap: _openTranslateSettings,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Icon(
                      Icons.settings,
                      size: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // 调试信息按钮
              GestureDetector(
                onTap: _toggleDebugInfo,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Icon(
                      _showDebugInfo
                          ? Icons.bug_report
                          : Icons.bug_report_outlined,
                      size: 18,
                      color: _showDebugInfo ? Colors.red : Colors.grey[600],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),
            ],
          ),
        ),
        actions: [
          // 保留一个空的actions以保持AppBar结构
        ],
      ),
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          _handleBackPress();
        },
        child: Stack(
          children: [
            // 显示错误页面、新标签页或WebView内容
            if (hasError)
              _buildErrorPage()
            else if (currentUrl == 'about:blank')
              _buildNewTabPage()
            else
              // WebView内容
              InAppWebView(
                initialUrlRequest: URLRequest(url: WebUri(currentUrl)),
                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  domStorageEnabled: true,
                  allowsInlineMediaPlayback: true,
                  mediaPlaybackRequiresUserGesture: false,
                  allowsBackForwardNavigationGestures: true,
                  supportZoom: true,
                  builtInZoomControls: false,
                  displayZoomControls: false,
                  useShouldOverrideUrlLoading: false,
                  useOnLoadResource: false,
                  clearCache: false,
                  cacheEnabled: true,
                  verticalScrollBarEnabled: true,
                  horizontalScrollBarEnabled: true,
                  disableVerticalScroll: false,
                  // 使用手机版User-Agent，确保加载手机版网站
                  userAgent:
                      'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36',
                  disableHorizontalScroll: false,
                  transparentBackground: false,
                  disallowOverScroll: false,
                  allowsLinkPreview: true,
                  isFraudulentWebsiteWarningEnabled: false,
                  disableLongPressContextMenuOnLinks: false,
                  allowingReadAccessTo: null,
                  javaScriptCanOpenWindowsAutomatically: false,
                  minimumFontSize: 0,
                ),
                onWebViewCreated: (controller) {
                  webViewController = controller;
                  debugPrint('🌐 WebView已创建，JavaScript已启用');

                  // 3. 更新TabManager中的WebView控制器
                  if (widget.tabId != null) {
                    final tabManager = TabManager();
                    tabManager.updateTabWebViewController(
                      widget.tabId!,
                      controller,
                    );
                  }
                },
                onLoadStart: (controller, url) async {
                  debugPrint('📄 页面开始加载: ${url?.toString()}');
                  setState(() {
                    isLoading = true;
                    hasError = false; // 1. 重置错误状态
                    _hasTranslatedCurrentPage = false; // 重置翻译状态
                  });

                  // 更新标签页状态：开始加载
                  _updateTabInfo(
                    url: url?.toString(),
                    isLoaded: false,
                    hasError: false,
                  );

                  // 1. 添加加载超时保护
                  Future.delayed(const Duration(seconds: 20), () {
                    if (mounted && isLoading && !hasError) {
                      setState(() {
                        isLoading = false;
                        hasError = true;
                        errorMessage = '页面加载超时，请检查网络连接或稍后重试';
                      });
                    }
                  });

                  final canGoBackResult = await controller.canGoBack();
                  final canGoForwardResult = await controller
                      .canGoForward(); // 1. 添加前进状态检查
                  setState(() {
                    canGoBack = canGoBackResult;
                    canGoForward = canGoForwardResult; // 1. 更新前进状态
                  });
                },
                onLoadStop: (controller, url) async {
                  debugPrint('✅ 页面加载完成: ${url?.toString()}');
                  setState(() {
                    isLoading = false;
                    hasError = false; // 1. 页面成功加载，清除错误状态
                    currentUrl = url?.toString() ?? currentUrl;
                  });

                  // 1. 获取页面标题并更新标签页 - 使用新的更新管理器
                  try {
                    final pageTitle = await controller.getTitle();
                    final pageUrl = url?.toString() ?? currentUrl;

                    // 使用新的标签页更新管理器
                    _updateTabInfo(
                      title: pageTitle,
                      url: pageUrl,
                      isLoaded: true,
                      hasError: false,
                    );

                    debugPrint('🔧 页面加载完成，URL: $pageUrl, 标题: $pageTitle');
                  } catch (e) {
                    debugPrint('🔧 获取页面信息失败: $e');
                    _updateTabInfo(hasError: true);
                  }

                  final canGoBackResult = await controller.canGoBack();
                  final canGoForwardResult = await controller
                      .canGoForward(); // 1. 添加前进状态检查
                  setState(() {
                    canGoBack = canGoBackResult;
                    canGoForward = canGoForwardResult; // 1. 更新前进状态
                  });
                  _startCoordinatedFeatures(url?.toString() ?? currentUrl);
                },
                onReceivedError: (controller, request, error) {
                  print('🚨 WebView错误: ${error.type}, URL: ${request.url}');

                  // 1. 更宽松的错误处理：只有主页面的严重错误才显示错误页面
                  if (request.url.toString() == currentUrl) {
                    // 1. 只处理主页面的错误
                    if (error.type == WebResourceErrorType.HOST_LOOKUP ||
                        error.type == WebResourceErrorType.TIMEOUT ||
                        error.type == WebResourceErrorType.UNKNOWN) {
                      // 1. 延迟显示错误页面，给WebView更多时间重试
                      Future.delayed(const Duration(seconds: 5), () {
                        if (mounted && isLoading && !hasError) {
                          setState(() {
                            isLoading = false;
                            hasError = true;
                            errorMessage = '网页无法打开';
                          });

                          // 更新标签页状态：加载错误
                          _updateTabInfo(hasError: true, isLoaded: false);
                        }
                      });
                    } else {
                      // 1. 其他错误类型，记录但不显示错误页面
                      print('🚨 忽略的主页面错误: ${error.type}');
                    }
                  } else {
                    // 1. 子资源错误，完全忽略
                    print('🚨 忽略的子资源错误: ${error.type}, URL: ${request.url}');
                  }
                },
              ),

            // 左下角页面加载指示器
            if (isLoading)
              Positioned(
                bottom: 20,
                left: 20,
                child: Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2.5,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
              ),

            // 调试信息面板
            if (_showDebugInfo) _buildDebugInfoPanel(),
          ],
        ),
      ),
      // 1. 多标签功能：去掉WebViewPage的底栏，使用BrowserApp统一的底栏
    );
  }

  // 1. 构建底栏（和首屏一模一样）
  Widget _buildBottomBar() {
    return Container(
      height: 50, // 不高的底栏，类似传统浏览器
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 1. 返回按钮（可返回黑色，不可返回浅色）
          _buildBottomBarButton(
            icon: Icons.arrow_back,
            isEnabled: canGoBack,
            onTap: _goBack,
          ),

          // 2. 前进按钮（可前进黑色，不可前进浅色）
          _buildBottomBarButton(
            icon: Icons.arrow_forward,
            isEnabled: canGoForward,
            onTap: _goForward,
          ),

          // 3. 搜索按钮（等于选中地址栏并跳出输入法）
          _buildBottomBarButton(
            icon: Icons.search,
            isEnabled: true,
            onTap: _focusAddressBar,
          ),

          // 4. 页签页按钮（多标签功能）
          _buildTabButton(),

          // 5. 更多按钮
          _buildBottomBarButton(
            icon: Icons.more_horiz,
            isEnabled: true,
            onTap: _showMoreMenu,
          ),
        ],
      ),
    );
  }

  // 1. 构建底栏按钮（和首屏一模一样）
  Widget _buildBottomBarButton({
    required IconData icon,
    required bool isEnabled,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Icon(
            icon,
            size: 24,
            color: isEnabled ? Colors.black : Colors.grey[400],
          ),
        ),
      ),
    );
  }

  // 1. 构建页签按钮（和首屏一模一样）
  Widget _buildTabButton() {
    return GestureDetector(
      onTap: _showTabManager,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Container(
            width: 28,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black, width: 1.5),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '1', // 简化显示，实际应该从首屏获取
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 1. 底栏功能实现（和首屏一模一样）
  void _goBack() async {
    if (webViewController != null && canGoBack) {
      await webViewController!.goBack();
    } else {
      // 2. 从首屏打开网页，这个时候的网页返回键是可以点击返回首页的
      Navigator.pop(context);
    }
  }

  void _goForward() async {
    if (webViewController != null && canGoForward) {
      await webViewController!.goForward();
    }
  }

  void _focusAddressBar() {
    // 1. 直接聚焦地址栏输入框
    _addressFocusNode.requestFocus();

    // 1. 选中全部文本，方便用户输入
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_addressController.text.isNotEmpty) {
        _addressController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _addressController.text.length,
        );
      }
    });
  }

  void _navigateToUrl(String url) {
    // 2. 智能URL导航功能 - 和首屏使用相同的机制
    String finalUrl = url;

    // 智能判断用户输入
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 完整URL，直接访问
      finalUrl = url;
    } else if (url.contains('.') && !url.contains(' ')) {
      // 包含点且无空格，可能是域名，自动添加https://
      finalUrl = 'https://$url';
    } else {
      // 其他情况当作搜索处理
      finalUrl = 'https://www.google.com/search?q=${Uri.encodeComponent(url)}';
    }

    setState(() {
      currentUrl = finalUrl;
      isLoading = true;
      hasError = false;
    });

    webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(finalUrl)));
  }

  // 1. 地址栏输入框 - 和首页完全一致的样式
  Widget _buildAddressInput() {
    // 1. 使用实例变量的控制器
    // 只在页面加载完成后更新地址栏，避免覆盖用户输入
    if (!_addressFocusNode.hasFocus) {
      // 只有在地址栏没有焦点时才更新内容
      if (currentUrl == 'about:blank') {
        if (_addressController.text.isNotEmpty && !_hasBeenFocused) {
          // 只有在用户还没有输入过的情况下才清空
          _addressController.text = '';
        }
      } else if (_addressController.text != currentUrl) {
        _addressController.text = currentUrl;
        // 1. 新标签页自动选中全部文本
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_addressController.text.isNotEmpty &&
              !_addressFocusNode.hasFocus) {
            _addressController.selection = TextSelection(
              baseOffset: 0,
              extentOffset: _addressController.text.length,
            );
          }
        });
      }
    }

    return TextField(
      controller: _addressController,
      focusNode: _addressFocusNode, // 1. 使用FocusNode
      autofocus:
          currentUrl == 'about:blank' &&
          !_hasBeenFocused, // 只有新空白标签页且未曾聚焦过才自动聚焦
      cursorColor: Colors.grey[500], // 1. 和首页一致的光标颜色
      decoration: InputDecoration(
        hintText: '搜索或输入网址',
        hintStyle: TextStyle(fontSize: 14, color: Colors.grey[700]), // 1. 和首页一致
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(
          vertical: 0,
        ), // 1. 和首页一致：完全去掉垂直padding
        isDense: true, // 1. 和首页一致
      ),
      style: const TextStyle(
        fontSize: 14,
        height: 1.0, // 1. 和首页一致：设置行高为1.0
      ),
      textAlignVertical: TextAlignVertical.center, // 1. 和首页一致
      onSubmitted: (value) {
        if (value.trim().isNotEmpty) {
          _navigateToUrl(value.trim());
        }
      },
      onChanged: (value) {
        // 用户开始输入时，重置选择状态
        _isTextSelected = false;
      },
      onTap: () {
        // 标记地址栏已被聚焦过
        _hasBeenFocused = true;

        // 智能选择逻辑：第一次点击全选，第二次点击取消全选
        if (!_isTextSelected && _addressController.text.isNotEmpty) {
          // 第一次点击：全选文本
          _addressController.selection = TextSelection(
            baseOffset: 0,
            extentOffset: _addressController.text.length,
          );
          _isTextSelected = true;
        } else {
          // 第二次点击或文本为空：取消全选，进入正常编辑模式
          _isTextSelected = false;
          // 不设置selection，让系统自动处理光标位置
        }
      },
    );
  }

  // 2. 编辑地址栏功能
  void _editAddressBar() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController(text: currentUrl);
        return AlertDialog(
          title: const Text('编辑地址'),
          content: TextField(
            controller: controller,
            autofocus: true,
            decoration: const InputDecoration(
              hintText: '输入网址或搜索内容...',
              border: OutlineInputBorder(),
            ),
            onSubmitted: (value) {
              Navigator.pop(context);
              if (value.trim().isNotEmpty) {
                _navigateToUrl(value.trim());
              }
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                if (controller.text.trim().isNotEmpty) {
                  _navigateToUrl(controller.text.trim());
                }
              },
              child: const Text('前往'),
            ),
          ],
        );
      },
    );
  }

  void _showTabManager() {
    // 1. 实现标签页管理功能
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: 300,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 标题和新建按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '标签页',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _addNewTab();
                    },
                    icon: const Icon(Icons.add, color: Colors.white),
                    iconSize: 18,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 当前标签页
            Expanded(
              child: ListView(
                children: [
                  ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.blue[100],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(Icons.web, color: Colors.blue[700]),
                    ),
                    title: Text(
                      _extractNameFromUrl(currentUrl),
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: Text(
                      currentUrl,
                      style: const TextStyle(fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                    trailing: const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addNewTab() {
    // 4. 实现真正的新建标签页功能（不替换当前页面）
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _NewTabPage(
          onNavigate: (url) {
            Navigator.pop(context); // 关闭新建标签页页面
            // 4. 创建新的WebView页面，不影响当前页面
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => WebViewPage(
                  initialUrl: url,
                  title: _extractNameFromUrl(url),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String _extractNameFromUrl(String url) {
    // 1. 从URL提取网站名称
    try {
      final uri = Uri.parse(url);
      String host = uri.host;
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      final Map<String, String> siteNames = {
        'books.fishhawk.top': '轻小说机翻网',
        'lightnovel.fun': '轻之国度',
        'google.com': '谷歌搜索',
        'baidu.com': '百度',
        'bilibili.com': '哔哩哔哩',
        'youtube.com': 'YouTube',
        'github.com': 'GitHub',
        'syosetu.com': '成为轻小说家吧',
        'kakuyomu.jp': 'カクヨム',
        'alphapolis.co.jp': 'アルファポリス',
        'syosetu.org': 'ハーメルン',
        'pixiv.net': 'ピクシブ小说',
        'novelup.plus': 'ノベルアップ+',
        'estar.jp': 'エブリスタ',
      };

      return siteNames[host] ?? host;
    } catch (e) {
      return '网页';
    }
  }

  void _showMoreMenu() {
    // 1. 实现更多菜单功能
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '更多功能',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('刷新页面'),
              onTap: () {
                Navigator.pop(context);
                webViewController?.reload();
              },
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('返回首页'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pop(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.share),
              title: const Text('分享页面'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('分享: $currentUrl')));
              },
            ),
          ],
        ),
      ),
    );
  }

  // 2. 添加和首屏一致的翻译设置方法
  String _getTranslationServiceName(String apiKey) {
    switch (apiKey) {
      case 'deepseek':
        return 'DeepSeek V3 翻译';
      case 'tencent':
        return '腾讯翻译 API';
      case 'baidu':
        return '百度翻译 API';
      case 'none':
        return '词典翻译';
      default:
        return 'DeepSeek V3 翻译';
    }
  }

  void _showTranslationServiceSelector(
    BuildContext context,
    StateSetter setDialogState,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '选择翻译服务',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1976D2),
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.white,
        elevation: 8,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildServiceOption(
              'DeepSeek V3 翻译',
              'deepseek',
              context,
              setDialogState,
            ),
            _buildServiceOption('Gemini 翻译', 'gemini', context, setDialogState),
            _buildServiceOption('腾讯翻译 API', 'tencent', context, setDialogState),
            _buildServiceOption('百度翻译 API', 'baidu', context, setDialogState),
            _buildServiceOption('词典翻译', 'none', context, setDialogState),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceOption(
    String title,
    String value,
    BuildContext context,
    StateSetter setDialogState,
  ) {
    final isSelected = selectedTranslationAPI == value;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isSelected
              ? [const Color(0xFFE3F2FD), const Color(0xFFBBDEFB)]
              : [Colors.white, const Color(0xFFF8F9FA)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? const Color(0xFF2196F3) : const Color(0xFFE0E0E0),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? const Color(0xFF2196F3).withOpacity(0.15)
                : const Color(0xFF000000).withOpacity(0.05),
            blurRadius: isSelected ? 6 : 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected
                ? const Color(0xFF1976D2)
                : const Color(0xFF333333),
          ),
        ),
        leading: Radio<String>(
          value: value,
          groupValue: selectedTranslationAPI,
          activeColor: const Color(0xFF2196F3),
          onChanged: (newValue) {
            final oldAPI = selectedTranslationAPI;
            setDialogState(() {
              selectedTranslationAPI = newValue!;
            });
            setState(() {});
            _saveSettings();
            Navigator.pop(context);

            // 如果API发生变化且翻译开关开启且页面已翻译，则重新翻译
            if (oldAPI != newValue &&
                isAITranslateEnabled &&
                _hasTranslatedCurrentPage) {
              debugPrint('🔧 API切换：$oldAPI → $newValue，翻译开关已开启，重新翻译页面');
              _retranslateWithNewAPI();
            } else if (oldAPI != newValue && !isAITranslateEnabled) {
              debugPrint('🔧 API切换：$oldAPI → $newValue，但翻译开关未开启，不重新翻译');
            } else if (oldAPI != newValue && !_hasTranslatedCurrentPage) {
              debugPrint('🔧 API切换：$oldAPI → $newValue，但页面未翻译过，不重新翻译');
            }
          },
        ),
        onTap: () {
          final oldAPI = selectedTranslationAPI;
          setDialogState(() {
            selectedTranslationAPI = value;
          });
          setState(() {});
          _saveSettings();
          Navigator.pop(context);

          // 如果API发生变化且翻译开关开启且页面已翻译，则重新翻译
          if (oldAPI != value &&
              isAITranslateEnabled &&
              _hasTranslatedCurrentPage) {
            debugPrint('🔧 API切换：$oldAPI → $value，翻译开关已开启，重新翻译页面');
            _retranslateWithNewAPI();
          } else if (oldAPI != value && !isAITranslateEnabled) {
            debugPrint('🔧 API切换：$oldAPI → $value，但翻译开关未开启，不重新翻译');
          } else if (oldAPI != value && !_hasTranslatedCurrentPage) {
            debugPrint('🔧 API切换：$oldAPI → $value，但页面未翻译过，不重新翻译');
          }
        },
      ),
    );
  }

  Widget _buildToggleOption(
    String title,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 4,
      ), // 1. 调整内边距，和字体更协调
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(6), // 1. 适中的圆角
        border: Border.all(
          color: const Color(0xFFDEE2E6),
          width: 0.8,
        ), // 1. 稍粗的边框
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12, // 1. 调大字体，更易读
              fontWeight: FontWeight.w400, // 1. 适中的字重
              color: Color(0xFF555555),
            ),
          ),
          Transform.scale(
            scale: 0.5, // 2. 继续缩小开关
            child: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: const Color(0xFF42A5F5),
            ),
          ),
        ],
      ),
    );
  }
}

// 1. 新建标签页页面
class _NewTabPage extends StatefulWidget {
  final Function(String) onNavigate;

  const _NewTabPage({required this.onNavigate});

  @override
  State<_NewTabPage> createState() => _NewTabPageState();
}

class _NewTabPageState extends State<_NewTabPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // 3. 网页打开新标签页的UI和首页打开新标签页的的不一致，需要以首页的为准
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false,
        title: Container(
          height: 40,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: TextField(
            controller: _controller,
            autofocus: true,
            style: const TextStyle(fontSize: 16),
            decoration: InputDecoration(
              hintText: '搜索或输入网址',
              hintStyle: TextStyle(color: Colors.grey[500]),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              suffixIcon: IconButton(
                icon: const Icon(Icons.search, size: 20),
                onPressed: () {
                  if (_controller.text.trim().isNotEmpty) {
                    widget.onNavigate(_controller.text.trim());
                  }
                },
              ),
            ),
            onSubmitted: (value) {
              if (value.trim().isNotEmpty) {
                widget.onNavigate(value.trim());
              }
            },
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.close, color: Colors.black87),
            onPressed: () => Navigator.pop(context),
          ),
        ],
      ),
      body: Container(
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 3. 大标题（和首页一致）
              const Text(
                '新建标签页',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              const SizedBox(height: 16),

              // 3. 副标题（和首页一致）
              Text(
                '在上方地址栏输入网址或搜索内容',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),

              const SizedBox(height: 40),

              // 3. 快捷操作按钮（和首页一致）
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildQuickAction('轻小说机翻网', 'https://books.fishhawk.top'),
                  const SizedBox(width: 20),
                  _buildQuickAction('轻之国度', 'https://www.lightnovel.fun'),
                  const SizedBox(width: 20),
                  _buildQuickAction('谷歌搜索', 'https://www.google.com'),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 3. 构建快捷操作按钮（和首页一致）
  Widget _buildQuickAction(String title, String url) {
    return GestureDetector(
      onTap: () => widget.onNavigate(url),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue[200]!, width: 1),
        ),
        child: Text(
          title,
          style: TextStyle(
            color: Colors.blue[700],
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
