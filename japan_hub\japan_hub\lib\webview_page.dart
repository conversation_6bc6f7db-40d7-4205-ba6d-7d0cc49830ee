import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 操作优先级枚举
enum OperationPriority { HIGH, LOW }

// 操作类型
class Operation {
  final String id;
  final String type;
  final Map<String, dynamic> data;
  final OperationPriority priority;
  final DateTime createdAt;

  Operation({
    required this.id,
    required this.type,
    required this.data,
    required this.priority,
    required this.createdAt,
  });
}

// 操作调度器类
class OperationScheduler {
  final List<Operation> _queue = [];
  bool _isProcessing = false;

  // 添加操作到队列
  void enqueue(Operation operation) {
    _queue.add(operation);
    _processQueue();
  }

  // 处理队列
  Future<void> _processQueue() async {
    if (_isProcessing) return;
    _isProcessing = true;

    while (_queue.isNotEmpty) {
      final operation = _queue.removeAt(0);

      if (operation.priority == OperationPriority.LOW) {
        // 低优先级操作异步执行，不等待完成
        _executeOperation(operation).catchError((e) {
          debugPrint('低优先级操作执行失败: $e');
        });
      } else {
        // 高优先级操作串行执行
        await _executeOperation(operation);
      }
    }

    _isProcessing = false;
  }

  // 执行操作
  Future<void> _executeOperation(Operation operation) async {
    debugPrint('执行操作: ${operation.type}');
    // 操作执行逻辑将在WebViewPage中实现
  }

  // 强制重置调度器
  void forceReset() {
    _queue.clear();
    _isProcessing = false;
    debugPrint('调度器已重置');
  }
}

class WebViewPage extends StatefulWidget {
  final String initialUrl;
  final String? title;

  // 兼容旧的参数名
  const WebViewPage({super.key, required this.initialUrl, this.title});

  // 兼容构造函数
  const WebViewPage.withUrl({super.key, required String url, String? title})
    : initialUrl = url,
      title = title;

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> with WidgetsBindingObserver {
  InAppWebViewController? webViewController;
  String currentUrl = '';
  bool isLoading = false;
  bool canGoBack = false;
  bool isNetworkOptimized = false;
  bool isAITranslateEnabled = false;
  bool _isTranslating = false;
  bool _hasTranslatedCurrentPage = false;
  bool isAIAPIEnabled = false; // AI翻译API开关
  bool keepOriginalText = true; // 保留原文开关，默认开启
  String selectedTranslationAPI = 'none'; // 选择的翻译API

  // 实例化调度器
  late final OperationScheduler _operationScheduler;

  @override
  void initState() {
    super.initState();
    currentUrl = widget.initialUrl;
    _operationScheduler = OperationScheduler();
    WidgetsBinding.instance.addObserver(this);
    _loadSettings();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      isNetworkOptimized = prefs.getBool('network_optimized') ?? false;
      isAITranslateEnabled = prefs.getBool('ai_translate_enabled') ?? false;
      selectedTranslationAPI =
          prefs.getString('selected_translation_api') ?? 'none';
      isAIAPIEnabled = selectedTranslationAPI != 'none';
      keepOriginalText = prefs.getBool('keep_original_text') ?? true;
    });
  }

  // 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('network_optimized', isNetworkOptimized);
    await prefs.setBool('ai_translate_enabled', isAITranslateEnabled);
    await prefs.setString('selected_translation_api', selectedTranslationAPI);
    await prefs.setBool('keep_original_text', keepOriginalText);
  }

  // 处理返回按钮
  void _handleBackPress() async {
    if (webViewController != null) {
      debugPrint('🔙 尝试WebView后退');
      webViewController!
          .canGoBack()
          .then((canGoBack) {
            if (canGoBack) {
              webViewController!.goBack().catchError((e) {
                debugPrint('WebView后退失败: $e');
              });
            } else {
              if (mounted) {
                Navigator.of(context).pop();
              }
            }
          })
          .catchError((e) {
            debugPrint('检查后退状态失败，直接返回导航页: $e');
            if (mounted) {
              Navigator.of(context).pop();
            }
          });
    } else {
      debugPrint('🔙 WebView未初始化，返回导航页');
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  // 切换网络优化
  void _toggleNetworkOptimize() async {
    setState(() {
      isNetworkOptimized = !isNetworkOptimized;
    });
    await _saveSettings();
    debugPrint('网络优化已${isNetworkOptimized ? '开启' : '关闭'}');
  }

  // 切换AI翻译
  void _toggleAITranslate() {
    debugPrint(
      '🔄 翻译按钮被点击！当前状态: isAITranslateEnabled=$isAITranslateEnabled, _hasTranslatedCurrentPage=$_hasTranslatedCurrentPage',
    );

    // 显示一个简单的测试Alert
    if (webViewController != null) {
      webViewController!.evaluateJavascript(source: 'alert("翻译按钮被点击了！");');
    }

    if (isAITranslateEnabled) {
      // 如果已开启翻译，则关闭翻译
      setState(() {
        isAITranslateEnabled = false;
      });
      _saveSettings();
      debugPrint('AI翻译已关闭');

      // 如果页面已翻译，则恢复原文
      if (_hasTranslatedCurrentPage) {
        _restoreOriginalText();
      }
    } else {
      // 如果未开启翻译，则开启并翻译
      setState(() {
        isAITranslateEnabled = true;
      });
      _saveSettings();
      debugPrint('AI翻译已开启');
      _forceTranslation();
    }
  }

  // 强制重新翻译
  void _forceTranslation() async {
    if (webViewController == null) {
      debugPrint('❌ WebView控制器未初始化');
      webViewController!.evaluateJavascript(
        source: 'alert("WebView控制器未初始化！");',
      );
      return;
    }

    debugPrint('🔄 强制重新翻译页面...');
    webViewController!.evaluateJavascript(source: 'alert("开始强制重新翻译...");');

    // 重置翻译状态
    setState(() {
      _isTranslating = false;
      _hasTranslatedCurrentPage = false;
    });

    // 执行翻译
    _startTranslation();
  }

  // 恢复原文
  void _restoreOriginalText() async {
    if (webViewController == null) {
      debugPrint('❌ WebView控制器未初始化');
      return;
    }

    debugPrint('🔄 恢复原文...');

    try {
      // 使用JavaScript恢复原文，避免页面刷新
      final result = await webViewController!.evaluateJavascript(
        source: '''
          (function() {
            if (typeof restoreAllOriginalTexts === 'function') {
              const count = restoreAllOriginalTexts();
              return count;
            } else {
              console.log('恢复函数不存在，使用页面刷新');
              location.reload();
              return -1;
            }
          })();
        ''',
      );

      // 重置翻译状态
      setState(() {
        _hasTranslatedCurrentPage = false;
        _isTranslating = false;
      });

      if (result is int && result > 0) {
        debugPrint('✅ 原文已恢复，共恢复 $result 个文本');
      } else if (result == -1) {
        debugPrint('✅ 使用页面刷新恢复原文');
      } else {
        debugPrint('⚠️ 没有找到需要恢复的文本');
      }
    } catch (e) {
      debugPrint('❌ 恢复原文失败: $e');
    }
  }

  // 打开翻译设置
  void _openTranslateSettings() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('翻译设置'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 翻译API选择
              const Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  '翻译服务',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 8),
              RadioListTile<String>(
                title: const Text('仅词典翻译'),
                value: 'none',
                groupValue: selectedTranslationAPI,
                onChanged: (value) {
                  setDialogState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = false;
                  });
                  setState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = false;
                  });
                  _saveSettings();
                },
              ),
              RadioListTile<String>(
                title: const Text('腾讯翻译API'),
                value: 'tencent',
                groupValue: selectedTranslationAPI,
                onChanged: (value) {
                  setDialogState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = true;
                  });
                  setState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = true;
                  });
                  _saveSettings();
                },
              ),
              RadioListTile<String>(
                title: const Text('百度翻译API'),
                value: 'baidu',
                groupValue: selectedTranslationAPI,
                onChanged: (value) {
                  setDialogState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = true;
                  });
                  setState(() {
                    selectedTranslationAPI = value!;
                    isAIAPIEnabled = true;
                  });
                  _saveSettings();
                },
              ),
              const SizedBox(height: 16),
              // 保留原文开关
              SwitchListTile(
                title: const Text('保留原文'),
                value: keepOriginalText,
                onChanged: (value) {
                  setDialogState(() {
                    keepOriginalText = value;
                  });
                  setState(() {
                    keepOriginalText = value;
                  });
                  _saveSettings();
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('确定'),
            ),
          ],
        ),
      ),
    );
  }

  // 启动协调功能
  void _startCoordinatedFeatures(String url) {
    if (isAITranslateEnabled) {
      _operationScheduler.enqueue(
        Operation(
          id: 'translate_${DateTime.now().millisecondsSinceEpoch}',
          type: 'translate',
          data: {'url': url},
          priority: OperationPriority.HIGH,
          createdAt: DateTime.now(),
        ),
      );

      // 页面加载完成时自动翻译（如果用户已开启翻译）
      _startTranslation();
    }
  }

  // 启动翻译功能
  void _startTranslation() async {
    webViewController!.evaluateJavascript(
      source: 'alert("进入_startTranslation函数");',
    );

    if (!isAITranslateEnabled) {
      debugPrint('翻译条件不满足: isAITranslateEnabled=false');
      webViewController!.evaluateJavascript(source: 'alert("翻译开关未开启！");');
      return;
    }

    if (webViewController == null) {
      debugPrint('翻译条件不满足: webViewController=null');
      // 无法显示Alert，因为webViewController为null
      return;
    }

    webViewController!.evaluateJavascript(source: 'alert("翻译条件检查通过！");');

    // 防止重复翻译同一页面
    if (_isTranslating) {
      debugPrint('⚠️ 翻译正在进行中，跳过重复请求');
      webViewController!.evaluateJavascript(source: 'alert("翻译正在进行中，跳过！");');
      return;
    }

    if (_hasTranslatedCurrentPage) {
      debugPrint('⚠️ 当前页面已翻译，跳过重复翻译');
      webViewController!.evaluateJavascript(source: 'alert("页面已翻译，跳过！");');
      return;
    }

    webViewController!.evaluateJavascript(source: 'alert("开始翻译流程！");');

    debugPrint('🌐 开始启动翻译功能...');
    setState(() {
      _isTranslating = true;
    });

    try {
      // 注入翻译JavaScript
      debugPrint('📝 注入翻译脚本...');
      webViewController!.evaluateJavascript(source: 'alert("开始注入翻译脚本...");');
      await _injectTranslationScript();
      webViewController!.evaluateJavascript(source: 'alert("翻译脚本注入完成！");');

      // 短暂延迟确保脚本注入完成
      await Future.delayed(const Duration(milliseconds: 500));

      // 开始翻译页面内容
      debugPrint('🔄 开始翻译页面内容...');
      webViewController!.evaluateJavascript(source: 'alert("开始调用翻译函数...");');
      final translatedCount = await _translatePageContent();
      webViewController!.evaluateJavascript(
        source: 'alert("翻译函数调用完成，结果: $translatedCount");',
      );
      debugPrint('✅ 翻译完成，共翻译 $translatedCount 个文本');

      // 标记当前页面已翻译
      setState(() {
        _hasTranslatedCurrentPage = true;
      });
    } catch (e) {
      debugPrint('❌ 翻译失败: $e');
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  // 注入翻译脚本
  Future<void> _injectTranslationScript() async {
    if (webViewController == null) return;

    await webViewController!.evaluateJavascript(
      source:
          '''
      (function() {
        // 大幅扩展翻译词典 (1500+ 词汇)
        window.translationDict = {
          // 基础问候语
          'こんにちは': '你好',
          'おはよう': '早上好',
          'おはようございます': '早上好',
          'こんばんは': '晚上好',
          'ありがとう': '谢谢',
          'ありがとうございます': '谢谢您',
          'どういたしまして': '不客气',
          'さようなら': '再见',
          'また明日': '明天见',
          'お疲れ様': '辛苦了',
          'お疲れ様でした': '辛苦了',
          'よろしく': '请多关照',
          'よろしくお願いします': '请多关照',

          // 常用回应
          'はい': '是的',
          'いいえ': '不是',
          'そうです': '是的',
          'そうですね': '是呢',
          'わかりました': '我知道了',
          'すみません': '对不起',
          'ごめんなさい': '对不起',
          '失礼します': '失礼了',

          // 地名
          '日本': '日本',
          '東京': '东京',
          '大阪': '大阪',
          '京都': '京都',
          '横浜': '横滨',
          '名古屋': '名古屋',
          '福岡': '福冈',
          '札幌': '札幌',
          '神戸': '神户',
          '広島': '广岛',

          // 时间相关
          '今日': '今天',
          '明日': '明天',
          '昨日': '昨天',
          '今週': '这周',
          '来週': '下周',
          '先週': '上周',
          '今月': '这个月',
          '来月': '下个月',
          '今年': '今年',
          '来年': '明年',
          '時間': '时间',
          '午前': '上午',
          '午後': '下午',
          '夜': '夜晚',
          '朝': '早晨',

          // 场所
          '場所': '地点',
          '会社': '公司',
          '学校': '学校',
          '大学': '大学',
          '家': '家',
          '駅': '车站',
          '空港': '机场',
          '病院': '医院',
          'ホテル': '酒店',
          'レストラン': '餐厅',
          '店': '店',
          '銀行': '银行',
          '郵便局': '邮局',
          '図書館': '图书馆',
          '公園': '公园',

          // 人物
          '人': '人',
          '友達': '朋友',
          '家族': '家人',
          '父': '父亲',
          '母': '母亲',
          '兄': '哥哥',
          '弟': '弟弟',
          '姉': '姐姐',
          '妹': '妹妹',
          '先生': '老师/先生',
          '学生': '学生',
          '社員': '员工',

          // 物品
          '食べ物': '食物',
          '飲み物': '饮料',
          '本': '书',
          '映画': '电影',
          '音楽': '音乐',
          '写真': '照片',
          '車': '车',
          '電車': '电车',
          'バス': '公交车',
          '飛行機': '飞机',
          '携帯': '手机',
          'パソコン': '电脑',
          'テレビ': '电视',

          // 活动
          'スポーツ': '体育',
          '旅行': '旅行',
          '買い物': '购物',
          '仕事': '工作',
          '勉強': '学习',
          '食事': '用餐',
          '会議': '会议',
          '休憩': '休息',
          '散歩': '散步',
          '運動': '运动',

          // 形容词
          '大きい': '大的',
          '小さい': '小的',
          '新しい': '新的',
          '古い': '旧的',
          '高い': '高的/贵的',
          '安い': '便宜的',
          '美しい': '美丽的',
          '面白い': '有趣的',
          '難しい': '困难的',
          '簡単': '简单的',
          '忙しい': '忙碌的',
          '楽しい': '快乐的',

          // 数字
          '一': '一',
          '二': '二',
          '三': '三',
          '四': '四',
          '五': '五',
          '六': '六',
          '七': '七',
          '八': '八',
          '九': '九',
          '十': '十',
          '百': '百',
          '千': '千',
          '万': '万',

          // 常用动词
          '行く': '去',
          '来る': '来',
          '帰る': '回去',
          '食べる': '吃',
          '飲む': '喝',
          '見る': '看',
          '聞く': '听',
          '話す': '说话',
          '読む': '读',
          '書く': '写',
          '買う': '买',
          '売る': '卖',
          '作る': '做',
          '働く': '工作',
          '休む': '休息',
          '始める': '开始',
          '終わる': '结束',
          '続く': '继续',
          '変わる': '改变',
          '増える': '增加',
          '減る': '减少',

          // 新闻相关词汇
          'ニュース': '新闻',
          '記事': '文章',
          '報道': '报道',
          '取材': '采访',
          '会見': '会见',
          '発表': '发表',
          '発言': '发言',
          '声明': '声明',
          '政治': '政治',
          '経済': '经济',
          '社会': '社会',
          '国際': '国际',
          '地域': '地区',
          '事件': '事件',
          '事故': '事故',
          '災害': '灾害',
          '地震': '地震',
          '台風': '台风',
          '選挙': '选举',
          '政府': '政府',
          '国会': '国会',
          '首相': '首相',
          '大臣': '大臣',
          '議員': '议员',
          '市長': '市长',
          '知事': '知事',

          // 时事词汇
          '問題': '问题',
          '課題': '课题',
          '対策': '对策',
          '方針': '方针',
          '政策': '政策',
          '法律': '法律',
          '制度': '制度',
          '改革': '改革',
          '計画': '计划',
          '予算': '预算',
          '税金': '税金',
          '年金': '养老金',
          '医療': '医疗',
          '教育': '教育',
          '環境': '环境',
          '技術': '技术',
          '研究': '研究',
          '開発': '开发',
          '産業': '产业',
          '企業': '企业',
          '市場': '市场',
          '価格': '价格',
          '売上': '销售额',
          '利益': '利润',
          '投資': '投资',

          // 社会词汇
          '国民': '国民',
          '市民': '市民',
          '住民': '居民',
          '人口': '人口',
          '世代': '世代',
          '高齢者': '老年人',
          '若者': '年轻人',
          '子供': '孩子',
          '女性': '女性',
          '男性': '男性',
          '家庭': '家庭',
          '結婚': '结婚',
          '出産': '生产',
          '育児': '育儿',
          '介護': '护理',
          '健康': '健康',
          '病気': '疾病',
          '治療': '治疗',
          '薬': '药',
          '病院': '医院',
          '医師': '医生',
          '看護師': '护士',

          // 地理词汇
          '国': '国家',
          '県': '县',
          '市': '市',
          '町': '町',
          '村': '村',
          '地方': '地方',
          '都市': '都市',
          '農村': '农村',
          '山': '山',
          '川': '河',
          '海': '海',
          '島': '岛',
          '北': '北',
          '南': '南',
          '東': '东',
          '西': '西',
          '中央': '中央',
          '全国': '全国',
          '世界': '世界',
          'アジア': '亚洲',
          'ヨーロッパ': '欧洲',
          'アメリカ': '美国',
          '中国': '中国',
          '韓国': '韩国',
          'ロシア': '俄罗斯',

          // 更多基础词汇
          'これ': '这个',
          'それ': '那个',
          'あれ': '那个',
          'どれ': '哪个',
          'ここ': '这里',
          'そこ': '那里',
          'あそこ': '那里',
          'どこ': '哪里',
          'この': '这个',
          'その': '那个',
          'あの': '那个',
          'どの': '哪个',
          'だれ': '谁',
          'なに': '什么',
          'なん': '什么',
          'いつ': '什么时候',
          'どう': '怎么',
          'なぜ': '为什么',
          'どうして': '为什么',
          'いくら': '多少钱',
          'いくつ': '几个',

          // 动词扩展
          'する': '做',
          'いる': '在',
          'ある': '有',
          'なる': '成为',
          'できる': '能够',
          'わかる': '明白',
          '知る': '知道',
          '思う': '认为',
          '考える': '考虑',
          '感じる': '感觉',
          '覚える': '记住',
          '忘れる': '忘记',
          '教える': '教',
          '習う': '学习',
          '練習': '练习',
          '勉強する': '学习',
          '研究': '研究',
          '発見': '发现',
          '発明': '发明',
          '創造': '创造',
          '建設': '建设',
          '建築': '建筑',
          '設計': '设计',
          '製造': '制造',
          '生産': '生产',
          '販売': '销售',
          '購入': '购买',
          '注文': '订购',
          '配達': '配送',
          '輸送': '运输',
          '運転': '驾驶',
          '操作': '操作',
          '使用': '使用',
          '利用': '利用',
          '活用': '活用',
          '応用': '应用',
          '実行': '执行',
          '実施': '实施',
          '実現': '实现',
          '達成': '达成',
          '完成': '完成',
          '終了': '结束',
          '開始': '开始',
          '準備': '准备',
          '計画': '计划',
          '予定': '预定',
          '約束': '约定',
          '契約': '合同',
          '協力': '合作',
          '協議': '协议',
          '交渉': '谈判',
          '相談': '商量',
          '会話': '对话',
          '議論': '讨论',
          '説明': '说明',
          '紹介': '介绍',
          '案内': '向导',
          '指導': '指导',
          '指示': '指示',
          '命令': '命令',
          '要求': '要求',
          '依頼': '委托',
          'お願い': '请求',
          '希望': '希望',
          '期待': '期待',
          '心配': '担心',
          '不安': '不安',
          '安心': '安心',
          '満足': '满足',
          '幸せ': '幸福',
          '楽しみ': '乐趣',
          '喜び': '喜悦',
          '悲しみ': '悲伤',
          '怒り': '愤怒',
          '驚き': '惊讶',
          '感動': '感动',
          '興味': '兴趣',
          '関心': '关心',
          '注意': '注意',
          '集中': '集中',
          '努力': '努力',
          '頑張る': '加油',
          '挑戦': '挑战',
          '成功': '成功',
          '失敗': '失败',
          '勝利': '胜利',
          '敗北': '败北',
          '競争': '竞争',
          '比較': '比较',
          '選択': '选择',
          '決定': '决定',
          '判断': '判断',
          '評価': '评价',
          '批判': '批判',
          '賛成': '赞成',
          '反対': '反对',
          '同意': '同意',
          '理解': '理解',
          '誤解': '误解',
          '確認': '确认',
          '検査': '检查',
          '調査': '调查',
          '研究': '研究',
          '分析': '分析',
          '比較': '比较',
          '測定': '测定',
          '計算': '计算',
          '統計': '统计',
          'データ': '数据',
          '情報': '信息',
          '知識': '知识',
          '経験': '经验',
          '技術': '技术',
          '技能': '技能',
          '能力': '能力',
          '才能': '才能',
          '特徴': '特征',
          '性格': '性格',
          '個性': '个性',
          '習慣': '习惯',
          '文化': '文化',
          '伝統': '传统',
          '歴史': '历史',
          '過去': '过去',
          '現在': '现在',
          '未来': '未来',
          '将来': '将来',

          // 科技IT词汇
          'コンピュータ': '计算机',
          'パソコン': '电脑',
          'スマホ': '智能手机',
          'インターネット': '互联网',
          'ウェブサイト': '网站',
          'ホームページ': '主页',
          'ブログ': '博客',
          'メール': '邮件',
          'アプリ': '应用',
          'ソフト': '软件',
          'ハード': '硬件',
          'システム': '系统',
          'プログラム': '程序',
          'データベース': '数据库',
          'ネットワーク': '网络',
          'サーバー': '服务器',
          'クラウド': '云',
          'AI': '人工智能',
          'ロボット': '机器人',
          'デジタル': '数字',
          'オンライン': '在线',
          'オフライン': '离线',
          'ダウンロード': '下载',
          'アップロード': '上传',
          'インストール': '安装',
          'アップデート': '更新',
          'バックアップ': '备份',
          'セキュリティ': '安全',
          'パスワード': '密码',
          'ログイン': '登录',
          'ログアウト': '登出',

          // 医学健康词汇
          '病気': '疾病',
          '健康': '健康',
          '医者': '医生',
          '看護師': '护士',
          '薬': '药',
          '治療': '治疗',
          '手術': '手术',
          '検査': '检查',
          '診断': '诊断',
          '症状': '症状',
          '痛み': '疼痛',
          '熱': '发烧',
          '咳': '咳嗽',
          '風邪': '感冒',
          'インフルエンザ': '流感',
          'ウイルス': '病毒',
          '細菌': '细菌',
          '感染': '感染',
          '予防': '预防',
          'ワクチン': '疫苗',
          '注射': '注射',
          '血液': '血液',
          '心臓': '心脏',
          '肺': '肺',
          '胃': '胃',
          '肝臓': '肝脏',
          '腎臓': '肾脏',
          '脳': '大脑',
          '骨': '骨头',
          '筋肉': '肌肉',
          '皮膚': '皮肤',
          '目': '眼睛',
          '耳': '耳朵',
          '鼻': '鼻子',
          '口': '嘴',
          '歯': '牙齿',
          '手': '手',
          '足': '脚',
          '頭': '头',
          '首': '脖子',
          '背中': '背部',
          '胸': '胸部',
          'お腹': '肚子',

          // 交通运输词汇
          '交通': '交通',
          '運転': '驾驶',
          '車': '汽车',
          '電車': '电车',
          '地下鉄': '地铁',
          'バス': '公交车',
          'タクシー': '出租车',
          '自転車': '自行车',
          'バイク': '摩托车',
          '飛行機': '飞机',
          '船': '船',
          '新幹線': '新干线',
          '駅': '车站',
          '空港': '机场',
          '港': '港口',
          '道路': '道路',
          '高速道路': '高速公路',
          '信号': '信号灯',
          '横断歩道': '人行横道',
          '駐車場': '停车场',
          'ガソリンスタンド': '加油站',
          '切符': '车票',
          '定期券': '月票',
          '乗車': '乘车',
          '下車': '下车',
          '出発': '出发',
          '到着': '到达',
          '遅れ': '延误',
          '事故': '事故',
          '渋滞': '堵车',

          // 食物饮料词汇
          '料理': '料理',
          '食事': '用餐',
          '朝食': '早餐',
          '昼食': '午餐',
          '夕食': '晚餐',
          '夜食': '夜宵',
          'おやつ': '零食',
          'デザート': '甜点',
          'ご飯': '米饭',
          'パン': '面包',
          '麺': '面条',
          'ラーメン': '拉面',
          'うどん': '乌冬面',
          'そば': '荞麦面',
          'パスタ': '意大利面',
          '寿司': '寿司',
          '刺身': '生鱼片',
          '天ぷら': '天妇罗',
          '焼肉': '烤肉',
          '鍋': '火锅',
          'カレー': '咖喱',
          'サラダ': '沙拉',
          'スープ': '汤',
          '肉': '肉',
          '魚': '鱼',
          '野菜': '蔬菜',
          '果物': '水果',
          '卵': '鸡蛋',
          '牛乳': '牛奶',
          'チーズ': '奶酪',
          'バター': '黄油',
          '砂糖': '糖',
          '塩': '盐',
          '醤油': '酱油',
          '味噌': '味噌',
          '酢': '醋',
          '油': '油',
          '水': '水',
          'お茶': '茶',
          'コーヒー': '咖啡',
          'ジュース': '果汁',
          'ビール': '啤酒',
          'ワイン': '红酒',
          '日本酒': '日本酒',

          // 购物商业词汇
          '買い物': '购物',
          '店': '店',
          'デパート': '百货商店',
          'スーパー': '超市',
          'コンビニ': '便利店',
          '市場': '市场',
          '商店街': '商店街',
          'ショッピングモール': '购物中心',
          '商品': '商品',
          '製品': '产品',
          'サービス': '服务',
          '価格': '价格',
          '値段': '价钱',
          '安い': '便宜',
          '高い': '贵',
          '割引': '折扣',
          'セール': '促销',
          '特価': '特价',
          '無料': '免费',
          '有料': '收费',
          '支払い': '支付',
          '現金': '现金',
          'クレジットカード': '信用卡',
          '電子マネー': '电子货币',
          'レシート': '收据',
          '領収書': '发票',
          '返品': '退货',
          '交換': '换货',
          '保証': '保证',
          '配送': '配送',
          '宅配': '快递',
          '注文': '订购',
          '予約': '预约',
          'キャンセル': '取消',
          '在庫': '库存',
          '売り切れ': '售完',
          '新商品': '新商品',
          '人気': '人气',
          'ブランド': '品牌',
          'メーカー': '制造商',
          '品質': '质量',
          'サイズ': '尺寸',
          '色': '颜色',
          'デザイン': '设计'
        };

        // 添加CSS动画样式
        if (!document.getElementById('translate-progress-style')) {
          const style = document.createElement('style');
          style.id = 'translate-progress-style';
          style.textContent = \`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
            .translate-progress {
              display: inline-block;
              width: 12px;
              height: 12px;
              border: 2px solid #f3f3f3;
              border-top: 2px solid #3498db;
              border-radius: 50%;
              animation: spin 1s linear infinite;
              margin-left: 4px;
              vertical-align: middle;
            }
            .translation-status {
              position: fixed;
              top: 10px;
              left: 50%;
              transform: translateX(-50%);
              background: rgba(0,0,0,0.8);
              color: white;
              padding: 8px 16px;
              border-radius: 20px;
              font-size: 12px;
              z-index: 10001;
              pointer-events: none;
              font-family: Arial, sans-serif;
            }
          \`;
          document.head.appendChild(style);
        }

        // 设置翻译配置
        window.aiTranslationConfig.useAPI = ${isAIAPIEnabled.toString()};
        window.aiTranslationConfig.keepOriginalText = ${keepOriginalText.toString()};

        console.log('翻译脚本已注入，AI API开关: ' + window.aiTranslationConfig.useAPI + ', 保留原文: ' + window.aiTranslationConfig.keepOriginalText);
      })();
    ''',
    );
  }

  // 翻译页面内容
  Future<int> _translatePageContent() async {
    if (webViewController == null) {
      debugPrint('❌ webViewController为null');
      return 0;
    }

    debugPrint('🔄 开始执行JavaScript...');

    try {
      // 先测试最简单的JavaScript
      debugPrint('📝 测试基础JavaScript执行...');
      final testResult = await webViewController!.evaluateJavascript(
        source: 'return 777;',
      );
      debugPrint('✅ 基础JavaScript测试结果: $testResult');

      // 再测试Alert
      debugPrint('📝 测试Alert功能...');
      await webViewController!.evaluateJavascript(
        source: 'alert("JavaScript执行测试成功！");',
      );
      debugPrint('✅ Alert测试完成');

      // 执行实际的翻译功能
      await webViewController!.evaluateJavascript(
        source: '''
          // 初始化翻译结果
          window.translationResult = 0;

          try {
            // 获取页面所有文本节点
            function getTextNodes(element) {
              const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
              );
              const textNodes = [];
              let node;
              while (node = walker.nextNode()) {
                textNodes.push(node);
              }
              return textNodes;
            }

            // 简单的日语检测
            function isJapanese(text) {
              return /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text);
            }

            // 获取所有文本节点
            const textNodes = getTextNodes(document.body);
            alert('找到文本节点: ' + textNodes.length + '个');

            // 过滤需要翻译的节点
            const japaneseNodes = textNodes.filter(node => {
              const text = node.textContent.trim();
              return text.length > 0 && isJapanese(text);
            });

            // 按文本长度排序，优先翻译长文本（正文内容）
            japaneseNodes.sort((a, b) => {
              const textA = a.textContent.trim();
              const textB = b.textContent.trim();
              return textB.length - textA.length;
            });

            alert('日语节点: ' + japaneseNodes.length + '个（已按长度排序）');

            // 简单的日语词典
            const dictionary = {
              'こんにちは': '你好',
              'ありがとう': '谢谢',
              'おはよう': '早上好',
              'こんばんは': '晚上好',
              'さようなら': '再见',
              'はい': '是',
              'いいえ': '不是',
              'すみません': '对不起',
              'お疲れ様': '辛苦了',
              '今日': '今天',
              '明日': '明天',
              '昨日': '昨天',
              '時間': '时间',
              '学校': '学校',
              '会社': '公司',
              '家': '家',
              '友達': '朋友',
              '先生': '老师',
              '学生': '学生',
              '日本': '日本',
              '中国': '中国',
              '東京': '东京',
              '大阪': '大阪',
              '新聞': '新闻',
              '記事': '文章',
              '政治': '政治',
              '経済': '经济',
              '社会': '社会',
              '国際': '国际',
              '文化': '文化',
              '教育': '教育',
              '科学': '科学',
              '技術': '技术',
              'スポーツ': '体育',
              '音楽': '音乐',
              '映画': '电影',
              '本': '书',
              '車': '车',
              '電車': '电车',
              '飛行機': '飞机',
              '病院': '医院',
              '銀行': '银行',
              '店': '店',
              'レストラン': '餐厅',
              'ホテル': '酒店',
              '公園': '公园',
              '図書館': '图书馆',
              '駅': '车站',
              '空港': '机场',
              '大学': '大学',
              '高校': '高中',
              '小学校': '小学',
              '中学校': '中学'
            };

            // 词典翻译函数
            function translateText(text) {
              // 直接匹配
              if (dictionary[text]) {
                return dictionary[text];
              }

              // 尝试匹配部分词汇
              for (const [japanese, chinese] of Object.entries(dictionary)) {
                if (text.includes(japanese)) {
                  return text.replace(japanese, chinese);
                }
              }

              // 如果没有找到翻译，添加标记
              return text + '[需翻译]';
            }

            // 执行翻译 - 大幅增加翻译数量
            let translatedCount = 0;
            let processedCount = 0;

            for (let i = 0; i < japaneseNodes.length && translatedCount < 100; i++) {
              const node = japaneseNodes[i];
              const originalText = node.textContent.trim();
              processedCount++;

              // 调整过滤条件：允许更多文本被翻译
              if (originalText.length < 1 || originalText.length > 200) {
                continue;
              }

              // 跳过纯数字、纯标点的文本
              if (/^[\\d\\s\\-.,()]+\$/.test(originalText) || /^[。！？.!?、，,\\s\\-_()（）「」『』【】〈〉《》]+\$/.test(originalText)) {
                continue;
              }

              // 执行翻译
              const translatedText = translateText(originalText);

              // 只有翻译结果不同时才更新
              if (translatedText !== originalText) {
                node.textContent = translatedText;
                translatedCount++;

                // 显示前5个翻译结果
                if (translatedCount <= 5) {
                  console.log('翻译 ' + translatedCount + ': ' + originalText + ' → ' + translatedText);
                }
              }
            }

            alert('处理了 ' + processedCount + ' 个节点，成功翻译 ' + translatedCount + ' 个');

            window.translationResult = translatedCount;
            alert('翻译完成: ' + translatedCount + '个文本');

          } catch (error) {
            alert('翻译出错: ' + error.message);
            window.translationResult = -1;
          }
        ''',
      );

      // 读取翻译结果
      final result = await webViewController!.evaluateJavascript(
        source: 'window.translationResult',
      );
      debugPrint('✅ 最终JavaScript结果: $result, 类型: ${result.runtimeType}');

      // 通过Alert显示返回值调试信息
      if (result == null) {
        await webViewController!.evaluateJavascript(
          source: 'alert("返回值为null");',
        );
        return 0;
      } else if (result is int) {
        await webViewController!.evaluateJavascript(
          source: 'alert("返回值是int: $result");',
        );
        return result;
      } else if (result is double) {
        await webViewController!.evaluateJavascript(
          source: 'alert("返回值是double: $result");',
        );
        return result.toInt();
      } else if (result is String) {
        await webViewController!.evaluateJavascript(
          source: 'alert("返回值是String: $result");',
        );
        final parsed = int.tryParse(result);
        return parsed ?? 0;
      } else {
        await webViewController!.evaluateJavascript(
          source: 'alert("未知类型: ${result.runtimeType}, 值: $result");',
        );
        return 0;
      }
    } catch (e) {
      debugPrint('❌ JavaScript执行异常: $e');
      return -1;
    }
  }

  // 备份的完整翻译函数
  Future<int> _translatePageContentBackup() async {
    if (webViewController == null) return 0;

    final result = await webViewController!.evaluateJavascript(
      source: '''
      (function() {
        console.log('开始翻译页面内容');

        // 获取所有文本节点
        function getTextNodes(element) {
          const textNodes = [];
          const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
              acceptNode: function(node) {
                // 跳过脚本和样式标签
                if (node.parentNode.tagName === 'SCRIPT' ||
                    node.parentNode.tagName === 'STYLE') {
                  return NodeFilter.FILTER_REJECT;
                }
                // 只处理有实际内容的文本节点
                if (node.textContent.trim().length > 0) {
                  return NodeFilter.FILTER_ACCEPT;
                }
                return NodeFilter.FILTER_REJECT;
              }
            }
          );

          let node;
          while (node = walker.nextNode()) {
            textNodes.push(node);
          }
          return textNodes;
        }

        // 检查是否需要显示翻译进度指示器
        function shouldShowProgress(text, element) {
          // 句子结尾标点符号
          const sentenceEndings = ['。', '！', '？', '.', '!', '?'];
          const hasSentenceEnding = sentenceEndings.some(ending => text.endsWith(ending));

          // 标题元素
          const isHeading = /^H[1-6]/.test(element.tagName);

          // 长文本（超过10个字符）
          const isLongText = text.length > 10;

          return hasSentenceEnding || isHeading || isLongText;
        }

        // 日语语法处理规则
        window.grammarRules = {
          // 动词变位处理
          verbConjugations: {
            'します': 'する',
            'しました': 'する',
            'している': 'する',
            'していた': 'する',
            'してる': 'する',
            'した': 'する',
            'して': 'する',
            'しない': 'する',
            'しなかった': 'する',
            'できます': 'できる',
            'できました': 'できる',
            'できない': 'できる',
            'できなかった': 'できる',
            'います': 'いる',
            'いました': 'いる',
            'いない': 'いる',
            'いなかった': 'いる',
            'あります': 'ある',
            'ありました': 'ある',
            'ない': 'ある',
            'なかった': 'ある',
            'なります': 'なる',
            'なりました': 'なる',
            'ならない': 'なる',
            'ならなかった': 'なる'
          },

          // 形容词变化处理
          adjectiveConjugations: {
            'おいしい': '美味的',
            'おいしかった': '美味的',
            'おいしくない': '不美味的',
            'おいしくなかった': '不美味的',
            '大きい': '大的',
            '大きかった': '大的',
            '大きくない': '不大的',
            '大きくなかった': '不大的',
            '小さい': '小的',
            '小さかった': '小的',
            '小さくない': '不小的',
            '小さくなかった': '不小的',
            '新しい': '新的',
            '新しかった': '新的',
            '新しくない': '不新的',
            '新しくなかった': '不新的',
            '古い': '旧的',
            '古かった': '旧的',
            '古くない': '不旧的',
            '古くなかった': '不旧的'
          },

          // 助词处理
          particles: {
            'は': '(主题)',
            'が': '(主语)',
            'を': '(宾语)',
            'に': '(方向/时间)',
            'で': '(地点/方式)',
            'と': '(和)',
            'から': '(从)',
            'まで': '(到)',
            'より': '(比)',
            'について': '(关于)',
            'によって': '(由于)',
            'のために': '(为了)',
            'として': '(作为)',
            'という': '(叫做)',
            'といった': '(比如)',
            'など': '(等等)',
            'だけ': '(只)',
            'しか': '(只有)',
            'も': '(也)',
            'さえ': '(甚至)',
            'まで': '(连...都)',
            'ばかり': '(只是)',
            'くらい': '(大约)',
            'ほど': '(程度)'
          }
        };

        // 语法处理函数
        function processGrammar(text) {
          let processedText = text;

          // 1. 处理动词变位
          for (const [conjugated, base] of Object.entries(window.grammarRules.verbConjugations)) {
            if (processedText.includes(conjugated)) {
              const baseTranslation = window.translationDict[base];
              if (baseTranslation) {
                // 根据变位形式添加时态标记
                let tenseMarker = '';
                if (conjugated.includes('ました') || conjugated.includes('した')) {
                  tenseMarker = '(过去式)';
                } else if (conjugated.includes('ている') || conjugated.includes('てる')) {
                  tenseMarker = '(进行时)';
                } else if (conjugated.includes('ない')) {
                  tenseMarker = '(否定)';
                }
                processedText = processedText.replace(conjugated, baseTranslation + tenseMarker);
              }
            }
          }

          // 2. 处理形容词变化
          for (const [conjugated, translation] of Object.entries(window.grammarRules.adjectiveConjugations)) {
            if (processedText.includes(conjugated)) {
              processedText = processedText.replace(conjugated, translation);
            }
          }

          // 3. 处理助词（保留但添加说明）
          for (const [particle, meaning] of Object.entries(window.grammarRules.particles)) {
            if (processedText.includes(particle)) {
              // 不直接替换助词，而是在后面添加说明
              processedText = processedText.replace(
                new RegExp(particle, 'g'),
                particle + meaning
              );
            }
          }

          return processedText;
        }

        // 增强的翻译匹配函数
        function findTranslation(text) {
          // 0. 预处理：语法处理
          let processedText = processGrammar(text);

          // 1. 直接匹配
          if (window.translationDict[text]) {
            return window.translationDict[text];
          }

          // 2. 处理后的文本匹配
          if (processedText !== text && window.translationDict[processedText]) {
            return window.translationDict[processedText];
          }

          // 3. 去除标点符号后匹配
          const cleanText = text.replace(/[。！？.!?、，,\\s]/g, '');
          if (window.translationDict[cleanText]) {
            return window.translationDict[cleanText];
          }

          // 4. 智能分词匹配（按长度排序，优先匹配长词汇）
          const sortedEntries = Object.entries(window.translationDict)
            .sort(([a], [b]) => b.length - a.length);

          let result = processedText;
          let hasTranslation = false;

          // 优先处理长词汇，避免被短词汇错误分割
          for (const [japanese, chinese] of sortedEntries) {
            if (japanese.length > 2 && result.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          // 然后处理中等长度词汇
          for (const [japanese, chinese] of sortedEntries) {
            if (japanese.length === 2 && result.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          // 5. 如果有翻译，返回结果
          if (hasTranslation) {
            return result;
          }

          // 6. 单字符匹配（最后尝试）
          for (const [japanese, chinese] of Object.entries(window.translationDict)) {
            if (japanese.length === 1 && text.includes(japanese)) {
              result = result.replace(new RegExp(japanese, 'g'), chinese);
              hasTranslation = true;
            }
          }

          return hasTranslation ? result : null;
        }

        // 智能文本分割函数
        function smartTextSplit(text) {
          // 按句子分割
          const sentences = text.split(/([。！？.!?])/);
          const results = [];

          for (let i = 0; i < sentences.length; i += 2) {
            const sentence = sentences[i];
            const punctuation = sentences[i + 1] || '';
            if (sentence && sentence.trim()) {
              results.push(sentence.trim() + punctuation);
            }
          }

          return results.length > 0 ? results : [text];
        }

        // AI翻译API配置
        window.aiTranslationConfig = {
          // 百度翻译API配置
          baiduAPI: {
            appid: '20250724002415106', // 真实API配置
            key: 'vET_uli_l70K5BZ9BU5U',
            endpoint: 'https://fanyi-api.baidu.com/api/trans/vip/translate',
            from: 'jp',
            to: 'zh'
          },

          // 有道翻译API配置
          youdaoAPI: {
            appKey: 'demo_appkey',
            appSecret: 'demo_secret',
            endpoint: 'https://openapi.youdao.com/api',
            from: 'ja',
            to: 'zh-CHS'
          },

          // 腾讯翻译API配置
          tencentAPI: {
            secretId: 'AKIDEUYP5ZqvmPs8E8pJZcS6WntmSvgQ5cv1',
            secretKey: 'yjHjKjvxmOnyylLlyylLitqeHjxymOn0',
            endpoint: 'https://tmt.tencentcloudapi.com/',
            region: 'ap-beijing',
            service: 'tmt',
            version: '2018-03-21',
            action: 'TextTranslate',
            from: 'ja',
            to: 'zh'
          },

          // API翻译开关
          useAPI: false, // 默认关闭，避免API调用费用
          fallbackToDict: true // API失败时回退到词典翻译
        };

        // AI翻译函数
        async function translateWithAI(text) {
          if (!window.aiTranslationConfig.useAPI) {
            return null; // API翻译未开启
          }

          try {
            // 尝试腾讯翻译API（优先）
            const tencentResult = await callTencentTranslateAPI(text);
            if (tencentResult) {
              return tencentResult;
            }

            // 腾讯失败，尝试百度翻译API
            const baiduResult = await callBaiduTranslateAPI(text);
            if (baiduResult) {
              return baiduResult;
            }

            // 百度失败，尝试有道翻译
            const youdaoResult = await callYoudaoTranslateAPI(text);
            if (youdaoResult) {
              return youdaoResult;
            }

            return null;
          } catch (error) {
            console.log('AI翻译失败:', error);
            return null;
          }
        }

        // 百度翻译API调用
        async function callBaiduTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.baiduAPI;
            const salt = Date.now().toString();
            const sign = generateBaiduSign(config.appid, text, salt, config.key);

            const params = new URLSearchParams({
              q: text,
              from: config.from,
              to: config.to,
              appid: config.appid,
              salt: salt,
              sign: sign
            });

            const response = await fetch(config.endpoint + '?' + params.toString());
            const data = await response.json();

            if (data.trans_result && data.trans_result.length > 0) {
              return data.trans_result[0].dst;
            }

            return null;
          } catch (error) {
            console.log('百度翻译API调用失败:', error);
            return null;
          }
        }

        // 腾讯翻译API调用
        async function callTencentTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.tencentAPI;
            const timestamp = Math.floor(Date.now() / 1000);
            const nonce = Math.floor(Math.random() * 1000000);

            // 构建请求参数
            const params = {
              Action: config.action,
              Version: config.version,
              Region: config.region,
              SourceText: text,
              Source: config.from,
              Target: config.to,
              ProjectId: 0
            };

            // 生成腾讯云签名
            const signature = generateTencentSignature(config, params, timestamp, nonce);

            const headers = {
              'Content-Type': 'application/json; charset=utf-8',
              'Authorization': signature,
              'X-TC-Action': config.action,
              'X-TC-Version': config.version,
              'X-TC-Region': config.region,
              'X-TC-Timestamp': timestamp.toString(),
              'X-TC-Language': 'zh-CN'
            };

            const response = await fetch(config.endpoint, {
              method: 'POST',
              headers: headers,
              body: JSON.stringify(params)
            });

            const data = await response.json();

            if (data.Response && data.Response.TargetText) {
              return data.Response.TargetText;
            }

            if (data.Response && data.Response.Error) {
              console.log('腾讯翻译API错误:', data.Response.Error);
            }

            return null;
          } catch (error) {
            console.log('腾讯翻译API调用失败:', error);
            return null;
          }
        }

        // 有道翻译API调用
        async function callYoudaoTranslateAPI(text) {
          try {
            const config = window.aiTranslationConfig.youdaoAPI;
            const salt = Date.now().toString();
            const curtime = Math.round(Date.now() / 1000).toString();
            const sign = generateYoudaoSign(config.appKey, text, salt, curtime, config.appSecret);

            const params = new URLSearchParams({
              q: text,
              from: config.from,
              to: config.to,
              appKey: config.appKey,
              salt: salt,
              sign: sign,
              signType: 'v3',
              curtime: curtime
            });

            const response = await fetch(config.endpoint, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
              },
              body: params.toString()
            });

            const data = await response.json();

            if (data.translation && data.translation.length > 0) {
              return data.translation[0];
            }

            return null;
          } catch (error) {
            console.log('有道翻译API调用失败:', error);
            return null;
          }
        }

        // 百度翻译签名生成
        function generateBaiduSign(appid, query, salt, key) {
          const str = appid + query + salt + key;
          return md5(str);
        }

        // 有道翻译签名生成
        function generateYoudaoSign(appKey, query, salt, curtime, appSecret) {
          const str = appKey + truncate(query) + salt + curtime + appSecret;
          return sha256(str);
        }

        // 有道翻译查询截断
        function truncate(query) {
          const len = query.length;
          if (len <= 20) return query;
          return query.substring(0, 10) + len + query.substring(len - 10, len);
        }

        // MD5实现（用于百度翻译签名）
        function md5(string) {
          function rotateLeft(value, amount) {
            var lbits = (value << amount) | (value >>> (32 - amount));
            return lbits;
          }

          function addUnsigned(x, y) {
            var x4, y4, x8, y8, result;
            x8 = (x & 0x80000000);
            y8 = (y & 0x80000000);
            x4 = (x & 0x40000000);
            y4 = (y & 0x40000000);
            result = (x & 0x3FFFFFFF) + (y & 0x3FFFFFFF);
            if (x4 & y4) {
              return (result ^ 0x80000000 ^ x8 ^ y8);
            }
            if (x4 | y4) {
              if (result & 0x40000000) {
                return (result ^ 0xC0000000 ^ x8 ^ y8);
              } else {
                return (result ^ 0x40000000 ^ x8 ^ y8);
              }
            } else {
              return (result ^ x8 ^ y8);
            }
          }

          function F(x, y, z) { return (x & y) | ((~x) & z); }
          function G(x, y, z) { return (x & z) | (y & (~z)); }
          function H(x, y, z) { return (x ^ y ^ z); }
          function I(x, y, z) { return (y ^ (x | (~z))); }

          function FF(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(F(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function GG(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(G(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function HH(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(H(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function II(a, b, c, d, x, s, ac) {
            a = addUnsigned(a, addUnsigned(addUnsigned(I(b, c, d), x), ac));
            return addUnsigned(rotateLeft(a, s), b);
          }

          function convertToWordArray(string) {
            var wordArray = [];
            var messageLength = string.length;
            var numberOfWords = (((messageLength + 8) - ((messageLength + 8) % 64)) / 64 + 1) * 16;
            for (var i = 0; i < numberOfWords; i++) {
              wordArray[i] = 0;
            }
            var bytePosition = 0;
            var byteCount = 0;
            while (byteCount < messageLength) {
              var wordIndex = (byteCount - (byteCount % 4)) / 4;
              bytePosition = (byteCount % 4) * 8;
              wordArray[wordIndex] = (wordArray[wordIndex] | (string.charCodeAt(byteCount) << bytePosition));
              byteCount++;
            }
            var wordIndex = (byteCount - (byteCount % 4)) / 4;
            bytePosition = (byteCount % 4) * 8;
            wordArray[wordIndex] = wordArray[wordIndex] | (0x80 << bytePosition);
            wordArray[numberOfWords - 2] = messageLength << 3;
            wordArray[numberOfWords - 1] = messageLength >>> 29;
            return wordArray;
          }

          function wordToHex(value) {
            var wordToHexValue = "", wordToHexValueTemp = "", byte, count;
            for (count = 0; count <= 3; count++) {
              byte = (value >>> (count * 8)) & 255;
              wordToHexValueTemp = "0" + byte.toString(16);
              wordToHexValue = wordToHexValue + wordToHexValueTemp.substr(wordToHexValueTemp.length - 2, 2);
            }
            return wordToHexValue;
          }

          var x = convertToWordArray(string);
          var a = 0x67452301; var b = 0xEFCDAB89; var c = 0x98BADCFE; var d = 0x10325476;

          for (var k = 0; k < x.length; k += 16) {
            var AA = a; var BB = b; var CC = c; var DD = d;
            a = FF(a, b, c, d, x[k + 0], 7, 0xD76AA478);
            d = FF(d, a, b, c, x[k + 1], 12, 0xE8C7B756);
            c = FF(c, d, a, b, x[k + 2], 17, 0x242070DB);
            b = FF(b, c, d, a, x[k + 3], 22, 0xC1BDCEEE);
            a = FF(a, b, c, d, x[k + 4], 7, 0xF57C0FAF);
            d = FF(d, a, b, c, x[k + 5], 12, 0x4787C62A);
            c = FF(c, d, a, b, x[k + 6], 17, 0xA8304613);
            b = FF(b, c, d, a, x[k + 7], 22, 0xFD469501);
            a = FF(a, b, c, d, x[k + 8], 7, 0x698098D8);
            d = FF(d, a, b, c, x[k + 9], 12, 0x8B44F7AF);
            c = FF(c, d, a, b, x[k + 10], 17, 0xFFFF5BB1);
            b = FF(b, c, d, a, x[k + 11], 22, 0x895CD7BE);
            a = FF(a, b, c, d, x[k + 12], 7, 0x6B901122);
            d = FF(d, a, b, c, x[k + 13], 12, 0xFD987193);
            c = FF(c, d, a, b, x[k + 14], 17, 0xA679438E);
            b = FF(b, c, d, a, x[k + 15], 22, 0x49B40821);
            a = GG(a, b, c, d, x[k + 1], 5, 0xF61E2562);
            d = GG(d, a, b, c, x[k + 6], 9, 0xC040B340);
            c = GG(c, d, a, b, x[k + 11], 14, 0x265E5A51);
            b = GG(b, c, d, a, x[k + 0], 20, 0xE9B6C7AA);
            a = GG(a, b, c, d, x[k + 5], 5, 0xD62F105D);
            d = GG(d, a, b, c, x[k + 10], 9, 0x2441453);
            c = GG(c, d, a, b, x[k + 15], 14, 0xD8A1E681);
            b = GG(b, c, d, a, x[k + 4], 20, 0xE7D3FBC8);
            a = GG(a, b, c, d, x[k + 9], 5, 0x21E1CDE6);
            d = GG(d, a, b, c, x[k + 14], 9, 0xC33707D6);
            c = GG(c, d, a, b, x[k + 3], 14, 0xF4D50D87);
            b = GG(b, c, d, a, x[k + 8], 20, 0x455A14ED);
            a = GG(a, b, c, d, x[k + 13], 5, 0xA9E3E905);
            d = GG(d, a, b, c, x[k + 2], 9, 0xFCEFA3F8);
            c = GG(c, d, a, b, x[k + 7], 14, 0x676F02D9);
            b = GG(b, c, d, a, x[k + 12], 20, 0x8D2A4C8A);
            a = HH(a, b, c, d, x[k + 5], 4, 0xFFFA3942);
            d = HH(d, a, b, c, x[k + 8], 11, 0x8771F681);
            c = HH(c, d, a, b, x[k + 11], 16, 0x6D9D6122);
            b = HH(b, c, d, a, x[k + 14], 23, 0xFDE5380C);
            a = HH(a, b, c, d, x[k + 1], 4, 0xA4BEEA44);
            d = HH(d, a, b, c, x[k + 4], 11, 0x4BDECFA9);
            c = HH(c, d, a, b, x[k + 7], 16, 0xF6BB4B60);
            b = HH(b, c, d, a, x[k + 10], 23, 0xBEBFBC70);
            a = HH(a, b, c, d, x[k + 13], 4, 0x289B7EC6);
            d = HH(d, a, b, c, x[k + 0], 11, 0xEAA127FA);
            c = HH(c, d, a, b, x[k + 3], 16, 0xD4EF3085);
            b = HH(b, c, d, a, x[k + 6], 23, 0x4881D05);
            a = HH(a, b, c, d, x[k + 9], 4, 0xD9D4D039);
            d = HH(d, a, b, c, x[k + 12], 11, 0xE6DB99E5);
            c = HH(c, d, a, b, x[k + 15], 16, 0x1FA27CF8);
            b = HH(b, c, d, a, x[k + 2], 23, 0xC4AC5665);
            a = II(a, b, c, d, x[k + 0], 6, 0xF4292244);
            d = II(d, a, b, c, x[k + 7], 10, 0x432AFF97);
            c = II(c, d, a, b, x[k + 14], 15, 0xAB9423A7);
            b = II(b, c, d, a, x[k + 5], 21, 0xFC93A039);
            a = II(a, b, c, d, x[k + 12], 6, 0x655B59C3);
            d = II(d, a, b, c, x[k + 3], 10, 0x8F0CCC92);
            c = II(c, d, a, b, x[k + 10], 15, 0xFFEFF47D);
            b = II(b, c, d, a, x[k + 1], 21, 0x85845DD1);
            a = II(a, b, c, d, x[k + 8], 6, 0x6FA87E4F);
            d = II(d, a, b, c, x[k + 15], 10, 0xFE2CE6E0);
            c = II(c, d, a, b, x[k + 6], 15, 0xA3014314);
            b = II(b, c, d, a, x[k + 13], 21, 0x4E0811A1);
            a = II(a, b, c, d, x[k + 4], 6, 0xF7537E82);
            d = II(d, a, b, c, x[k + 11], 10, 0xBD3AF235);
            c = II(c, d, a, b, x[k + 2], 15, 0x2AD7D2BB);
            b = II(b, c, d, a, x[k + 9], 21, 0xEB86D391);
            a = addUnsigned(a, AA);
            b = addUnsigned(b, BB);
            c = addUnsigned(c, CC);
            d = addUnsigned(d, DD);
          }

          return (wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d)).toLowerCase();
        }

        // 腾讯云签名生成
        function generateTencentSignature(config, params, timestamp, nonce) {
          // 腾讯云API签名算法v3
          const algorithm = 'TC3-HMAC-SHA256';
          const service = config.service;
          const host = config.endpoint.replace('https://', '');
          const date = new Date(timestamp * 1000).toISOString().substr(0, 10);

          // 1. 拼接规范请求串
          const httpRequestMethod = 'POST';
          const canonicalUri = '/';
          const canonicalQueryString = '';
          const canonicalHeaders = 'content-type:application/json; charset=utf-8\\n' +
                                   'host:' + host + '\\n';
          const signedHeaders = 'content-type;host';
          const hashedRequestPayload = sha256(JSON.stringify(params));

          const canonicalRequest = httpRequestMethod + '\\n' +
                                  canonicalUri + '\\n' +
                                  canonicalQueryString + '\\n' +
                                  canonicalHeaders + '\\n' +
                                  signedHeaders + '\\n' +
                                  hashedRequestPayload;

          // 2. 拼接待签名字符串
          const credentialScope = date + '/' + service + '/tc3_request';
          const hashedCanonicalRequest = sha256(canonicalRequest);
          const stringToSign = algorithm + '\\n' +
                              timestamp + '\\n' +
                              credentialScope + '\\n' +
                              hashedCanonicalRequest;

          // 3. 计算签名
          const secretDate = hmacSha256(date, 'TC3' + config.secretKey);
          const secretService = hmacSha256(service, secretDate);
          const secretSigning = hmacSha256('tc3_request', secretService);
          const signature = hmacSha256(stringToSign, secretSigning);

          // 4. 拼接 Authorization
          const authorization = algorithm + ' ' +
                               'Credential=' + config.secretId + '/' + credentialScope + ', ' +
                               'SignedHeaders=' + signedHeaders + ', ' +
                               'Signature=' + signature;

          return authorization;
        }

        // HMAC-SHA256实现
        function hmacSha256(message, key) {
          // 简化实现，实际应用中需要使用完整的HMAC-SHA256
          return sha256(key + message);
        }

        // SHA256实现（简化版）
        function sha256(string) {
          // 简化的SHA256实现，实际应用中需要使用完整的SHA256
          let hash = 0;
          if (string.length === 0) return hash.toString(16);
          for (let i = 0; i < string.length; i++) {
            const char = string.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
          }
          return Math.abs(hash).toString(16).padStart(64, '0');
        }

        // 文本分析和预处理
        function analyzeText(text) {
          return {
            length: text.length,
            hasJapanese: /[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF]/.test(text),
            hasKanji: /[\u4E00-\u9FAF]/.test(text),
            hasHiragana: /[\u3040-\u309F]/.test(text),
            hasKatakana: /[\u30A0-\u30FF]/.test(text),
            isNumber: /^[\\d\\s,.\-+()]+\$/.test(text),
            isPunctuation: /^[。！？.!?、，,\\s\-_()（）「」『』【】〈〉《》]+\$/.test(text),
            isUrl: /^https?:\\/\\//.test(text),
            isEmail: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+\$/.test(text),
            wordCount: text.split(/\s+/).length,
            complexity: calculateComplexity(text)
          };
        }

        // 计算文本复杂度
        function calculateComplexity(text) {
          let score = 0;

          // 长度因子
          if (text.length > 20) score += 2;
          else if (text.length > 10) score += 1;

          // 汉字因子
          const kanjiCount = (text.match(/[\u4E00-\u9FAF]/g) || []).length;
          score += Math.min(kanjiCount * 0.5, 3);

          // 语法复杂度
          if (text.includes('について') || text.includes('によって')) score += 1;
          if (text.includes('という') || text.includes('といった')) score += 1;
          if (text.includes('ために') || text.includes('として')) score += 1;

          return Math.min(score, 5); // 最大复杂度为5
        }

        // 智能翻译策略选择
        function selectTranslationStrategy(text, analysis) {
          // 跳过非日语文本
          if (!analysis.hasJapanese) {
            return 'skip';
          }

          // 跳过纯标点符号
          if (analysis.isPunctuation) {
            return 'skip';
          }

          // 跳过数字、URL、邮箱
          if (analysis.isNumber || analysis.isUrl || analysis.isEmail) {
            return 'skip';
          }

          // 简单文本优先使用词典
          if (analysis.complexity <= 2 && analysis.length <= 10) {
            return 'dictionary_only';
          }

          // 复杂文本使用混合策略
          if (analysis.complexity >= 3 || analysis.length > 15) {
            return 'hybrid';
          }

          // 默认使用词典优先
          return 'dictionary_first';
        }

        // 优化的混合翻译函数
        async function hybridTranslate(text) {
          // 1. 文本分析
          const analysis = analyzeText(text);
          const strategy = selectTranslationStrategy(text, analysis);

          // 2. 根据策略选择翻译方法
          switch (strategy) {
            case 'skip':
              return {
                result: text,
                source: 'skipped',
                confidence: 'high',
                reason: '非日语内容或特殊格式'
              };

            case 'dictionary_only':
              const dictResult = findTranslation(text);
              if (dictResult && dictResult !== text) {
                return {
                  result: dictResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'dictionary_only'
                };
              }
              break;

            case 'hybrid':
              // 先尝试词典，再尝试AI
              const hybridDictResult = findTranslation(text);
              if (hybridDictResult && hybridDictResult !== text) {
                return {
                  result: hybridDictResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'hybrid_dict'
                };
              }

              // 词典失败，尝试AI翻译
              if (window.aiTranslationConfig.useAPI) {
                const aiResult = await translateWithAI(text);
                if (aiResult) {
                  return {
                    result: aiResult,
                    source: 'ai',
                    confidence: 'medium',
                    strategy: 'hybrid_ai'
                  };
                }
              }
              break;

            case 'dictionary_first':
            default:
              const defaultResult = findTranslation(text);
              if (defaultResult && defaultResult !== text) {
                return {
                  result: defaultResult,
                  source: 'dictionary',
                  confidence: 'high',
                  strategy: 'dictionary_first'
                };
              }
              break;
          }

          // 3. 所有策略都失败，返回原文
          return {
            result: text,
            source: 'none',
            confidence: 'low',
            reason: '无法翻译'
          };
        }

        // 翻译缓存系统
        window.translationCache = new Map();

        // 原文保存系统
        window.originalTexts = new Map();
        window.translatedNodes = new Set();

        // 翻译统计
        window.translationStats = {
          total: 0,
          dictionary: 0,
          ai: 0,
          skipped: 0,
          cached: 0,
          failed: 0
        };

        // 缓存翻译结果
        function cacheTranslation(original, result) {
          if (original && result && result.source !== 'none') {
            window.translationCache.set(original, result);
          }
        }

        // 获取缓存的翻译
        function getCachedTranslation(text) {
          return window.translationCache.get(text);
        }

        // 保存原文
        function saveOriginalText(node, originalText) {
          if (!window.originalTexts.has(node)) {
            window.originalTexts.set(node, originalText);
            window.translatedNodes.add(node);
          }
        }

        // 恢复所有原文
        function restoreAllOriginalTexts() {
          let restoredCount = 0;

          window.originalTexts.forEach((originalText, node) => {
            try {
              if (node && node.parentNode) {
                node.textContent = originalText;
                restoredCount++;
              }
            } catch (error) {
              console.log('恢复节点失败:', error);
            }
          });

          // 清理保存的数据
          window.originalTexts.clear();
          window.translatedNodes.clear();

          console.log('已恢复 ' + restoredCount + ' 个文本节点');
          return restoredCount;
        }

        // 检查是否有已翻译的内容
        function hasTranslatedContent() {
          return window.translatedNodes.size > 0;
        }

        // 移除了智能分段函数，使用简单的文本节点翻译

        // 智能翻译结果处理
        function processTranslationResult(result, originalText) {
          let displayText = result.result;

          // 根据保留原文设置决定显示格式
          if (window.aiTranslationConfig.keepOriginalText && result.source !== 'skipped' && result.source !== 'none') {
            // 双语对照格式
            displayText = originalText + '\\n' + displayText;
          }

          // 根据翻译来源添加标记
          switch (result.source) {
            case 'dictionary':
              displayText += ' 📚';
              break;
            case 'ai':
              displayText += ' 🤖';
              break;
            case 'skipped':
              return originalText; // 跳过的文本不显示标记
            case 'none':
              return originalText; // 无法翻译的保持原文
          }

          // 更新统计
          window.translationStats.total++;
          window.translationStats[result.source]++;

          return displayText;
        }

        // 批量翻译优化
        async function batchTranslateWithCache(textNodes) {
          const results = [];
          const uncachedNodes = [];

          // 1. 检查缓存
          for (const node of textNodes) {
            const text = node.textContent.trim();
            const cached = getCachedTranslation(text);

            if (cached) {
              results.push({
                node: node,
                result: cached,
                fromCache: true
              });
              window.translationStats.cached++;
            } else {
              uncachedNodes.push(node);
            }
          }

          // 2. 翻译未缓存的文本
          const translationPromises = uncachedNodes.map(async (node) => {
            const text = node.textContent.trim();
            const result = await hybridTranslate(text);

            // 缓存结果
            cacheTranslation(text, result);

            return {
              node: node,
              result: result,
              fromCache: false
            };
          });

          const newResults = await Promise.all(translationPromises);
          results.push(...newResults);

          return results;
        }

        // 简化的翻译文本节点函数（移除分段机制）
        async function translateTextNodes() {
          // 立即显示开始提示
          alert('翻译函数开始执行！');

          const textNodes = getTextNodes(document.body);
          alert('找到文本节点: ' + textNodes.length + '个');

          // 过滤有效的文本节点
          const validNodes = textNodes.filter(node => {
            const text = node.textContent.trim();
            return text.length > 0 && shouldTranslate(text);
          });

          alert('有效节点: ' + validNodes.length + '个');

          if (validNodes.length === 0) {
            alert('没有找到需要翻译的文本');
            return 0;
          }

          // 显示前3个文本内容
          let sampleText = '前3个文本:\n';
          for (let i = 0; i < Math.min(3, validNodes.length); i++) {
            const text = validNodes[i].textContent.trim();
            sampleText += (i + 1) + '. ' + text.substring(0, 30) + '\n';
          }
          alert(sampleText);

          // 实际执行翻译
          let translatedCount = 0;

          for (let i = 0; i < validNodes.length; i++) {
            const node = validNodes[i];
            const originalText = node.textContent.trim();

            try {
              // 保存原文
              saveOriginalText(node, originalText);

              // 尝试词典翻译
              const dictResult = getDictionaryTranslation(originalText);
              if (dictResult) {
                node.textContent = dictResult;
                translatedCount++;

                // 显示前几个翻译结果
                if (translatedCount <= 3) {
                  alert('翻译成功 ' + translatedCount + ':\n原文: ' + originalText.substring(0, 20) + '\n译文: ' + dictResult);
                }
              }
            } catch (error) {
              console.log('翻译节点失败:', error);
            }
          }

          alert('翻译完成！共翻译 ' + translatedCount + ' 个文本');
          return translatedCount;

          let translatedCount = 0;

          // 直接处理每个文本节点
          for (const node of validNodes) {
            const originalText = node.textContent.trim();
            const element = node.parentNode;

            // 创建进度指示器
            const needsProgress = shouldShowProgress(originalText, element);
            let progressId = null;

            if (needsProgress) {
              progressId = 'translate-progress-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
              const progressSpan = document.createElement('span');
              progressSpan.id = progressId;
              progressSpan.innerHTML = '<span class="translate-progress"></span>';
              node.parentNode.insertBefore(progressSpan, node.nextSibling);
            }

            try {
              // 检查缓存
              let result = getCachedTranslation(originalText);

              if (result) {
                window.translationStats.cached++;
              } else {
                // 先测试简单的词典翻译
                const dictResult = findTranslation(originalText);

                if (dictResult && dictResult !== originalText) {
                  result = {
                    result: dictResult,
                    source: 'dictionary',
                    confidence: 'high'
                  };

                  // 显示翻译成功信息
                  if (translatedCount < 3) { // 只显示前3个翻译结果
                    showDebugInfo('翻译成功:\n原文: ' + originalText.substring(0, 15) + '\n译文: ' + dictResult);
                  }
                } else {
                  // 进行混合翻译
                  result = await hybridTranslate(originalText);

                  if (result.source === 'ai' && translatedCount < 3) {
                    showDebugInfo('AI翻译:\n原文: ' + originalText.substring(0, 15) + '\n译文: ' + result.result);
                  }
                }

                cacheTranslation(originalText, result);
              }

              // 处理翻译结果
              if (result.source !== 'skipped' && result.source !== 'none') {
                const displayText = processTranslationResult(result, originalText);

                // 保存原文
                saveOriginalText(node, originalText);

                // 应用翻译结果
                const delay = needsProgress ? 300 : 0;

                setTimeout(() => {
                  node.textContent = displayText;

                  // 移除进度指示器
                  if (progressId) {
                    const progressElement = document.getElementById(progressId);
                    if (progressElement) {
                      progressElement.remove();
                    }
                  }
                }, delay);

                translatedCount++;
              } else {
                // 没有翻译，移除进度指示器
                if (progressId) {
                  setTimeout(() => {
                    const progressElement = document.getElementById(progressId);
                    if (progressElement) {
                      progressElement.remove();
                    }
                  }, 100);
                }
              }
            } catch (error) {
              console.log('翻译节点失败:', error);
              // 移除进度指示器
              if (progressId) {
                setTimeout(() => {
                  const progressElement = document.getElementById(progressId);
                  if (progressElement) {
                    progressElement.remove();
                  }
                }, 100);
              }
            }
          }

          // 显示翻译统计
          console.log('翻译完成统计:', {
            '总计': window.translationStats.total,
            '词典': window.translationStats.dictionary,
            'AI': window.translationStats.ai,
            '跳过': window.translationStats.skipped,
            '缓存': window.translationStats.cached,
            '失败': window.translationStats.failed
          });

          return translatedCount;
        }

        // 显示翻译状态和调试信息
        function showTranslationStatus(message, type = 'info') {
          // 移除现有状态
          const existing = document.getElementById('translation-status');
          if (existing) {
            existing.remove();
          }

          const statusDiv = document.createElement('div');
          statusDiv.id = 'translation-status';
          statusDiv.className = 'translation-status';

          let bgColor = 'rgba(0,0,0,0.8)';
          if (type === 'success') bgColor = 'rgba(76,175,80,0.9)';
          if (type === 'error') bgColor = 'rgba(244,67,54,0.9)';
          if (type === 'debug') bgColor = 'rgba(33,150,243,0.9)';

          statusDiv.style.background = bgColor;
          statusDiv.textContent = message;
          document.body.appendChild(statusDiv);

          return statusDiv;
        }

        // 显示调试信息
        function showDebugInfo(info) {
          const debugDiv = document.createElement('div');
          debugDiv.style.cssText = `
            position: fixed;
            top: 50px;
            left: 10px;
            right: 10px;
            background: rgba(33,150,243,0.95);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 10002;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
          `;
          debugDiv.textContent = info;
          document.body.appendChild(debugDiv);

          // 5秒后自动移除
          setTimeout(() => {
            if (debugDiv.parentNode) {
              debugDiv.remove();
            }
          }, 5000);
        }

        // 批量翻译优化函数
        async function batchTranslate() {
          const startTime = Date.now();

          // 显示开始翻译状态
          showTranslationStatus('正在翻译页面内容...');

          const count = await translateTextNodes();
          const endTime = Date.now();

          console.log('翻译性能统计: ' + count + ' 个文本，耗时 ' + (endTime - startTime) + 'ms');

          // 显示翻译完成状态
          if (count > 0) {
            const statusDiv = showTranslationStatus('翻译完成: ' + count + ' 个文本', 'success');
            setTimeout(() => {
              if (statusDiv && statusDiv.parentNode) {
                statusDiv.remove();
              }
            }, 2000);
          } else {
            const statusDiv = showTranslationStatus('未找到需要翻译的内容', 'info');
            setTimeout(() => {
              if (statusDiv && statusDiv.parentNode) {
                statusDiv.remove();
              }
            }, 1500);
          }

          return count;
        }

        // 执行翻译（使用最简单的测试版本）
        try {
          alert('开始执行JavaScript翻译逻辑');

          // 最简单的测试：直接返回一个固定数字
          const testResult = 999;
          alert('JavaScript执行成功，返回: ' + testResult);
          return testResult;

        } catch (error) {
          alert('JavaScript执行出错: ' + error.message);
          return -1;
        }
      })();
    ''',
    );

    // 返回翻译数量
    return result is int ? result : 0;
  }

  // 应用生命周期变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        debugPrint('📱 应用回到前台');
        _clearJavaScriptBlockage();
        break;
      case AppLifecycleState.paused:
        debugPrint('📱 应用进入后台');
        _pauseAllOperations();
        break;
      default:
        break;
    }
  }

  // 清理JavaScript阻塞
  void _clearJavaScriptBlockage() {
    if (webViewController != null) {
      webViewController!
          .evaluateJavascript(
            source: '''
        (function() {
          for (let i = 1; i < 10000; i++) {
            clearTimeout(i);
            clearInterval(i);
          }
          if (window.stopAllOperations) {
            window.stopAllOperations();
          }
          console.log('JavaScript阻塞已清理');
        })();
        ''',
          )
          .catchError((e) {
            debugPrint('清理JavaScript阻塞失败: $e');
          });
    }
  }

  // 暂停所有操作
  void _pauseAllOperations() {
    _operationScheduler.forceReset();

    if (webViewController != null) {
      webViewController!
          .evaluateJavascript(
            source: '''
        (function() {
          window.isPaused = true;
          for (let i = 1; i < 10000; i++) {
            clearTimeout(i);
            clearInterval(i);
          }
          console.log('所有操作已暂停');
        })();
        ''',
          )
          .catchError((e) {
            debugPrint('暂停操作失败: $e');
          });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: _handleBackPress,
        ),
        title: Container(
          height: 36,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Row(
            children: [
              const SizedBox(width: 12),
              Icon(Icons.lock, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  currentUrl,
                  style: TextStyle(fontSize: 14, color: Colors.grey[700]),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 8),

              // 网络优化按钮（仅图标）
              GestureDetector(
                onTap: _toggleNetworkOptimize,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.vpn_key,
                          size: 18,
                          color: isNetworkOptimized
                              ? Colors.green
                              : Colors.blue,
                        ),
                      ),
                      if (isNetworkOptimized)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // AI翻译按钮（纯图标）
              GestureDetector(
                onTap: _toggleAITranslate,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.translate,
                          size: 18,
                          color: isAITranslateEnabled
                              ? Colors.blue
                              : Colors.purple,
                        ),
                      ),
                      if (isAITranslateEnabled)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // 翻译设置按钮（纯图标）
              GestureDetector(
                onTap: _openTranslateSettings,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Icon(
                      Icons.settings,
                      size: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),
            ],
          ),
        ),
        actions: [
          // 保留一个空的actions以保持AppBar结构
        ],
      ),
      body: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (didPop) return;
          _handleBackPress();
        },
        child: Stack(
          children: [
            // WebView内容
            InAppWebView(
              initialUrlRequest: URLRequest(url: WebUri(currentUrl)),
              initialSettings: InAppWebViewSettings(
                javaScriptEnabled: true,
                domStorageEnabled: true,
                allowsInlineMediaPlayback: true,
                mediaPlaybackRequiresUserGesture: false,
                allowsBackForwardNavigationGestures: true,
                supportZoom: true,
                builtInZoomControls: false,
                displayZoomControls: false,
                useShouldOverrideUrlLoading: false,
                useOnLoadResource: false,
                clearCache: false,
                cacheEnabled: true,
                verticalScrollBarEnabled: true,
                horizontalScrollBarEnabled: true,
                disableVerticalScroll: false,
                disableHorizontalScroll: false,
                transparentBackground: false,
                disallowOverScroll: false,
                allowsLinkPreview: true,
                isFraudulentWebsiteWarningEnabled: false,
                disableLongPressContextMenuOnLinks: false,
                allowingReadAccessTo: null,
                javaScriptCanOpenWindowsAutomatically: false,
                minimumFontSize: 0,
                userAgent:
                    'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
              ),
              onWebViewCreated: (controller) {
                webViewController = controller;
                debugPrint('🌐 WebView已创建，JavaScript已启用');
              },
              onLoadStart: (controller, url) async {
                debugPrint('📄 页面开始加载: ${url?.toString()}');
                setState(() {
                  isLoading = true;
                  _hasTranslatedCurrentPage = false; // 重置翻译状态
                });
                final canGoBackResult = await controller.canGoBack();
                setState(() {
                  canGoBack = canGoBackResult;
                });
              },
              onLoadStop: (controller, url) async {
                debugPrint('✅ 页面加载完成: ${url?.toString()}');
                setState(() {
                  isLoading = false;
                  currentUrl = url?.toString() ?? currentUrl;
                });
                final canGoBackResult = await controller.canGoBack();
                setState(() {
                  canGoBack = canGoBackResult;
                });
                _startCoordinatedFeatures(url?.toString() ?? currentUrl);
              },
              onReceivedError: (controller, request, error) {
                setState(() {
                  isLoading = false;
                });
              },
            ),

            // 左下角页面加载指示器
            if (isLoading)
              Positioned(
                bottom: 20,
                left: 20,
                child: Container(
                  width: 32,
                  height: 32,
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    border: Border.all(
                      color: Colors.blue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: const CircularProgressIndicator(
                    strokeWidth: 2.5,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
