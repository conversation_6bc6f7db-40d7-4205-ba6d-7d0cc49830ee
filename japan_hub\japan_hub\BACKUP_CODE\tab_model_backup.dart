// 备份：标签页数据模型
// 保存时间：2025-07-28 02:30
// 来源：当前账号的完整实现

/// 标签页数据模型
/// 表示单个标签页的所有信息
class TabModel {
  /// 标签页唯一ID
  final String id;
  
  /// 标签页标题
  final String title;
  
  /// 标签页URL
  final String url;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后访问时间
  final DateTime lastAccessedAt;
  
  /// 是否可以后退
  final bool canGoBack;
  
  /// 是否可以前进
  final bool canGoForward;
  
  /// 页面图标URL
  final String? faviconUrl;

  const TabModel({
    required this.id,
    required this.title,
    required this.url,
    this.isLoading = false,
    DateTime? createdAt,
    DateTime? lastAccessedAt,
    this.canGoBack = false,
    this.canGoForward = false,
    this.faviconUrl,
  }) : createdAt = createdAt ?? const Duration(),
       lastAccessedAt = lastAccessedAt ?? const Duration();

  /// 创建副本并更新指定字段
  TabModel copyWith({
    String? id,
    String? title,
    String? url,
    bool? isLoading,
    DateTime? createdAt,
    DateTime? lastAccessedAt,
    bool? canGoBack,
    bool? canGoForward,
    String? faviconUrl,
  }) {
    return TabModel(
      id: id ?? this.id,
      title: title ?? this.title,
      url: url ?? this.url,
      isLoading: isLoading ?? this.isLoading,
      createdAt: createdAt ?? this.createdAt,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      canGoBack: canGoBack ?? this.canGoBack,
      canGoForward: canGoForward ?? this.canGoForward,
      faviconUrl: faviconUrl ?? this.faviconUrl,
    );
  }

  /// 更新最后访问时间
  TabModel updateLastAccessed() {
    return copyWith(lastAccessedAt: DateTime.now());
  }

  /// 更新加载状态
  TabModel updateLoading(bool loading) {
    return copyWith(isLoading: loading);
  }

  /// 更新导航状态
  TabModel updateNavigation({bool? canGoBack, bool? canGoForward}) {
    return copyWith(
      canGoBack: canGoBack ?? this.canGoBack,
      canGoForward: canGoForward ?? this.canGoForward,
    );
  }

  /// 是否为空白页签
  bool get isBlank => url.isEmpty || url == 'about:blank';

  /// 获取域名
  String get domain {
    if (url.isEmpty) return '';
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (e) {
      return '';
    }
  }

  /// 获取显示标题（如果标题为空则使用域名）
  String get displayTitle {
    if (title.isNotEmpty && title != 'about:blank') {
      return title;
    }
    if (domain.isNotEmpty) {
      return domain;
    }
    return '新建页签';
  }

  /// 转换为Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'url': url,
      'isLoading': isLoading,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastAccessedAt': lastAccessedAt.millisecondsSinceEpoch,
      'canGoBack': canGoBack,
      'canGoForward': canGoForward,
      'faviconUrl': faviconUrl,
    };
  }

  /// 从Map创建
  factory TabModel.fromMap(Map<String, dynamic> map) {
    return TabModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      url: map['url'] ?? '',
      isLoading: map['isLoading'] ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      lastAccessedAt: DateTime.fromMillisecondsSinceEpoch(map['lastAccessedAt'] ?? 0),
      canGoBack: map['canGoBack'] ?? false,
      canGoForward: map['canGoForward'] ?? false,
      faviconUrl: map['faviconUrl'],
    );
  }

  @override
  String toString() {
    return 'TabModel(id: $id, title: $title, url: $url, isLoading: $isLoading)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TabModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
