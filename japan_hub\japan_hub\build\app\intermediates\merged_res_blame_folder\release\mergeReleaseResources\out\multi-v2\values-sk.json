{"logs": [{"outputFile": "com.example.japan_hub.app-mergeReleaseResources-44:/values-sk/values-sk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5789ed75dd4181f7db416e2788a9020b\\transformed\\appcompat-1.6.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,2817", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,2895"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,424,510,618,736,815,892,983,1076,1174,1268,1368,1461,1556,1654,1745,1836,1920,2025,2133,2232,2338,2450,2553,2719,4367", "endColumns": "106,100,110,85,107,117,78,76,90,92,97,93,99,92,94,97,90,90,83,104,107,98,105,111,102,165,97,82", "endOffsets": "207,308,419,505,613,731,810,887,978,1071,1169,1263,1363,1456,1551,1649,1740,1831,1915,2020,2128,2227,2333,2445,2548,2714,2812,4445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c48979df973cd2c9b59e4146f315bd8f\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "37,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3627,3822,3925,4040", "endColumns": "106,102,114,101", "endOffsets": "3729,3920,4035,4137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1eb6e469cc614b5f4ea976541f34c484\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "29,30,31,32,33,34,35,45", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2817,2913,3015,3116,3214,3324,3432,4450", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "2908,3010,3111,3209,3319,3427,3549,4546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\02b593d561501e9e2544adef61c8aa64\\transformed\\preference-1.2.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,266,344,491,660,744", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "173,261,339,486,655,739,820"}, "to": {"startLines": "36,38,42,43,46,47,48", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3554,3734,4142,4220,4551,4720,4804", "endColumns": "72,87,77,146,168,83,80", "endOffsets": "3622,3817,4215,4362,4715,4799,4880"}}]}]}