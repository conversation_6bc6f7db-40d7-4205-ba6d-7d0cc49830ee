{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Scoop/persist/android-clt/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Scoop/persist/android-clt/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Scoop/persist/android-clt/cmake/3.22.1/bin/ctest.exe", "root": "D:/Scoop/persist/android-clt/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-617d28252697c085c309.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c4b23143e3ceb4542dd4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f2fbeb01f1e29e7f608d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c4b23143e3ceb4542dd4.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-f2fbeb01f1e29e7f608d.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-617d28252697c085c309.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}