import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 生成主图标
  await generateIcon(512, 'assets/icon/app_icon.png');
  
  // 生成前景图标（用于adaptive icon）
  await generateForegroundIcon(512, 'assets/icon/app_icon_foreground.png');
  
  print('图标生成完成！');
  exit(0);
}

Future<void> generateIcon(int size, String path) async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // 绘制蓝色背景
  final backgroundPaint = Paint()
    ..color = const Color(0xFF2196F3)
    ..style = PaintingStyle.fill;
  
  final rect = RRect.fromRectAndRadius(
    Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
    Radius.circular(size * 0.2), // 20% 圆角
  );
  canvas.drawRRect(rect, backgroundPaint);
  
  // 绘制白色N字
  final textPainter = TextPainter(
    text: TextSpan(
      text: 'N',
      style: TextStyle(
        color: Colors.white,
        fontSize: size * 0.6, // 60% 大小
        fontWeight: FontWeight.bold,
        fontFamily: 'Arial',
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  
  textPainter.layout();
  
  final textOffset = Offset(
    (size - textPainter.width) / 2,
    (size - textPainter.height) / 2,
  );
  
  textPainter.paint(canvas, textOffset);
  
  // 转换为图片
  final picture = recorder.endRecording();
  final image = await picture.toImage(size, size);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  // 保存文件
  final file = File(path);
  await file.parent.create(recursive: true);
  await file.writeAsBytes(byteData!.buffer.asUint8List());
  
  print('生成图标: $path');
}

Future<void> generateForegroundIcon(int size, String path) async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // 透明背景
  canvas.drawRect(
    Rect.fromLTWH(0, 0, size.toDouble(), size.toDouble()),
    Paint()..color = Colors.transparent,
  );
  
  // 绘制白色N字（稍小一些，适配adaptive icon）
  final textPainter = TextPainter(
    text: TextSpan(
      text: 'N',
      style: TextStyle(
        color: Colors.white,
        fontSize: size * 0.5, // 50% 大小，比主图标小一些
        fontWeight: FontWeight.bold,
        fontFamily: 'Arial',
      ),
    ),
    textDirection: TextDirection.ltr,
  );
  
  textPainter.layout();
  
  final textOffset = Offset(
    (size - textPainter.width) / 2,
    (size - textPainter.height) / 2,
  );
  
  textPainter.paint(canvas, textOffset);
  
  // 转换为图片
  final picture = recorder.endRecording();
  final image = await picture.toImage(size, size);
  final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
  
  // 保存文件
  final file = File(path);
  await file.parent.create(recursive: true);
  await file.writeAsBytes(byteData!.buffer.asUint8List());
  
  print('生成前景图标: $path');
}
