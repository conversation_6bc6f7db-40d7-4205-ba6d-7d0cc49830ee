# C/C++ build system timings
generate_cxx_metadata
  [gap of 30ms]
  create-invalidation-state 70ms
  [gap of 25ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 136ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 62ms
  [gap of 23ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 117ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 60ms
  [gap of 24ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 120ms

