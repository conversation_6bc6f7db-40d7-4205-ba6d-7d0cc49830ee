import 'package:flutter/material.dart';
import 'tab_manager.dart';
import 'home_page.dart';
import 'webview_page.dart';

// 主浏览器应用容器
class BrowserApp extends StatefulWidget {
  @override
  State<BrowserApp> createState() => _BrowserAppState();
}

class _BrowserAppState extends State<BrowserApp> {
  final TabManager _tabManager = TabManager();

  @override
  void initState() {
    super.initState();
    _tabManager.initialize();
    _tabManager.addListener(_onTabsChanged);
  }

  @override
  void dispose() {
    _tabManager.removeListener(_onTabsChanged);
    super.dispose();
  }

  void _onTabsChanged() {
    setState(() {});
  }

  // 2. 使用IndexedStack保持所有标签页状态
  Widget _buildTabPages() {
    final tabs = _tabManager.tabs;
    if (tabs.isEmpty) {
      return const Center(child: Text('没有标签页'));
    }

    return IndexedStack(
      index: _tabManager.currentIndex,
      children: tabs.map((tab) {
        if (tab.isHomePage) {
          return HomePage(key: ValueKey('home_${tab.id}'));
        } else {
          return WebViewPage(
            key: ValueKey('webview_${tab.id}'),
            initialUrl: tab.url!,
            title: tab.title,
            tabId: tab.id,
          );
        }
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: _tabManager.currentTab?.isHomePage ?? false, // 在首页时允许系统自然退出
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final currentTab = _tabManager.currentTab;
          if (currentTab?.isHomePage != true) {
            // 只有在网页时才处理返回，切换到首页
            _tabManager.switchToTab(0);
          }
          // 在首页时不需要处理，让系统自然退出APP
        }
      },
      child: Scaffold(
        body: _buildTabPages(),
        bottomNavigationBar: _buildBottomBar(),
      ),
    );
  }

  // 构建底栏
  Widget _buildBottomBar() {
    final currentTab = _tabManager.currentTab;
    final isHomePage = currentTab?.isHomePage ?? true;

    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 返回按钮
          _buildBottomBarButton(
            icon: Icons.arrow_back,
            isEnabled: !isHomePage,
            onTap: _goBack,
          ),

          // 前进按钮
          _buildBottomBarButton(
            icon: Icons.arrow_forward,
            isEnabled: false, // 简化实现，暂不支持前进
            onTap: () {},
          ),

          // 搜索按钮
          _buildBottomBarButton(
            icon: Icons.search,
            isEnabled: true,
            onTap: _showSearch,
          ),

          // 新建标签页按钮
          _buildBottomBarButton(
            icon: Icons.add,
            isEnabled: true,
            onTap: _addNewTab,
          ),

          // 标签页按钮
          _buildTabButton(),

          // 更多按钮
          _buildBottomBarButton(
            icon: Icons.more_horiz,
            isEnabled: true,
            onTap: _showMoreMenu,
          ),
        ],
      ),
    );
  }

  // 构建底栏按钮
  Widget _buildBottomBarButton({
    required IconData icon,
    required bool isEnabled,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Icon(
            icon,
            size: 24,
            color: isEnabled ? Colors.black : Colors.grey[400],
          ),
        ),
      ),
    );
  }

  // 构建标签页按钮
  Widget _buildTabButton() {
    return GestureDetector(
      onTap: _showTabManager,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Container(
            width: 28,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black, width: 1.5),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '${_tabManager.tabCount}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 从首页打开网页的第一页，点返回回到首屏
  void _goBack() {
    final currentTab = _tabManager.currentTab;

    // 显示调试信息
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '🔙 返回点击 - 当前: ${currentTab?.title ?? "null"}, 首屏: ${currentTab?.isHomePage ?? "null"}',
        ),
        duration: const Duration(seconds: 3),
      ),
    );

    if (currentTab != null && !currentTab.isHomePage) {
      // 从首页打开的网页，点返回直接回首屏
      _tabManager.switchToTab(0);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔙 切换到首屏'),
          duration: Duration(seconds: 2),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('🔙 当前已经是首屏或标签页为空'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // 新建标签页功能：创建空白页面，让用户在地址栏输入网址
  void _addNewTab() {
    _tabManager.addTab(url: 'about:blank', title: '新标签页');
  }

  // 1. 搜索功能：聚焦地址栏
  void _showSearch() {
    final currentTab = _tabManager.currentTab;
    if (currentTab != null && !currentTab.isHomePage) {
      // 1. 如果是网页，调用WebViewPage的聚焦地址栏方法
      // 通过GlobalKey或者其他方式来实现
      // 暂时使用简单的实现：显示搜索弹窗
      _showSearchDialog();
    } else {
      // 1. 如果是首屏，也显示搜索弹窗
      _showSearchDialog();
    }
  }

  // 1. 显示搜索弹窗
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '搜索或输入网址',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1976D2),
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.white,
        elevation: 8,
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: '输入搜索内容或网址...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            Navigator.of(context).pop();
            if (value.isNotEmpty) {
              _navigateToUrl(value);
            }
          },
        ),
      ),
    );
  }

  // 导航到URL
  void _navigateToUrl(String url) {
    String finalUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = 'https://www.google.com/search?q=${Uri.encodeComponent(url)}';
    }

    _tabManager.addTab(url: finalUrl, title: _extractNameFromUrl(finalUrl));
  }

  // 从URL提取网站名称
  String _extractNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      String host = uri.host;
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      final Map<String, String> siteNames = {
        'books.fishhawk.top': '轻小说机翻网',
        'lightnovel.fun': '轻之国度',
        'google.com': '谷歌搜索',
        'baidu.com': '百度',
      };

      return siteNames[host] ?? host;
    } catch (e) {
      return '网页';
    }
  }

  // 显示标签页管理器
  void _showTabManager() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '标签页',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.blue[600],
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _addNewTab();
                      },
                      icon: const Icon(Icons.add, color: Colors.white),
                      iconSize: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: _tabManager.tabs.length,
                  itemBuilder: (context, index) {
                    final tab = _tabManager.tabs[index];
                    final isActive = index == _tabManager.currentIndex;

                    return ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: tab.isHomePage
                              ? Colors.blue[100]
                              : Colors.green[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          tab.isHomePage ? Icons.home : Icons.web,
                          color: tab.isHomePage
                              ? Colors.blue[700]
                              : Colors.green[700],
                        ),
                      ),
                      title: Text(
                        tab.title,
                        style: TextStyle(
                          fontWeight: isActive
                              ? FontWeight.bold
                              : FontWeight.w500,
                        ),
                      ),
                      subtitle: tab.url != null
                          ? Text(
                              tab.url!,
                              style: const TextStyle(fontSize: 12),
                              overflow: TextOverflow.ellipsis,
                            )
                          : null,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 当前活跃标签页显示绿色勾选图标
                          if (isActive)
                            const Icon(Icons.check_circle, color: Colors.green),
                          // 所有非首屏标签页都可以关闭（包括当前活跃的）
                          if (!tab.isHomePage) ...[
                            if (isActive) const SizedBox(width: 8),
                            IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: () {
                                // 直接关闭标签页，无需确认
                                _tabManager.closeTab(index);
                                setModalState(() {}); // 立即更新弹窗UI
                                setState(() {}); // 更新主界面UI
                                if (_tabManager.tabs.length <= 1) {
                                  Navigator.pop(context);
                                }
                              },
                            ),
                          ],
                        ],
                      ),
                      onTap: () {
                        _tabManager.switchToTab(index);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示更多菜单
  void _showMoreMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '更多功能',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.refresh),
              title: const Text('刷新页面'),
              onTap: () {
                Navigator.pop(context);
                // 实现刷新功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('返回首页'),
              onTap: () {
                Navigator.pop(context);
                _tabManager.switchToTab(0);
              },
            ),
          ],
        ),
      ),
    );
  }
}
