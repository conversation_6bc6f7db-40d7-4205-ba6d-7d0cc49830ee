import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'home_page.dart';
import 'cache_manager.dart';

// 开屏阶段图标失败状态管理
class _SplashIconCache {
  static Future<void> markIconFailed(String iconUrl, String websiteName) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${iconUrl}_$websiteName';
      await prefs.setBool('icon_failed_$cacheKey', true);
      debugPrint('开屏阶段记录图标失败: $websiteName');
    } catch (e) {
      // 保存失败，不影响正常流程
    }
  }
}

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _progressController;
  late Animation<double> _logoAnimation;
  late Animation<double> _progressAnimation;

  int _countdown = 5;
  Timer? _timer;
  bool _isPreloading = false;
  String _loadingText = '正在初始化...';

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startSplashSequence();
  }

  void _initAnimations() {
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 5000),
      vsync: this,
    );

    _logoAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeInOut),
    );
  }

  void _startSplashSequence() {
    // 启动logo动画
    _logoController.forward();

    // 启动进度条动画
    _progressController.forward();

    // 开始倒计时和预加载
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _countdown--;
        _updateLoadingText();
      });

      if (_countdown <= 0) {
        timer.cancel();
        _navigateToHome();
      }
    });

    // 开始预加载
    _startPreloading();
  }

  void _updateLoadingText() {
    switch (_countdown) {
      case 4:
        _loadingText = 'AI翻译和网络加速已启用！';
        break;
      case 3:
        _loadingText = '正在预加载网站内容...';
        break;
      case 2:
        _loadingText = '正在优化网络连接...';
        break;
      case 1:
        _loadingText = '即将完成...';
        break;
      default:
        _loadingText = '正在启用AI翻译和网络加速...';
    }
  }

  void _startPreloading() {
    setState(() {
      _isPreloading = true;
    });

    // 模拟预加载网站
    _preloadWebsites();
  }

  Future<void> _preloadWebsites() async {
    // 预加载推荐网站列表
    final websiteUrls = [
      'https://syosetu.com',
      'https://www.pixiv.net',
      'https://kakuyomu.jp',
      'https://www.nicovideo.jp',
      'https://www.yahoo.co.jp',
      'https://anime-newtype.net',
    ];

    // 导航页网站列表（与home_page.dart保持一致）
    final websiteObjects = [
      {
        'name': '小説家になろう',
        'url': 'https://syosetu.com/',
        'icon': 'https://syosetu.com/favicon.ico',
        'description': '日本最大的网络小说平台',
        'category': 'novel',
      },
      {
        'name': 'カクヨム',
        'url': 'https://kakuyomu.jp/',
        'icon': 'https://kakuyomu.jp/favicon.ico',
        'description': 'KADOKAWA旗下小说平台',
        'category': 'novel',
      },
      {
        'name': 'pixiv',
        'url': 'https://www.pixiv.net/',
        'icon': 'https://www.pixiv.net/favicon.ico',
        'description': '插画艺术社区',
        'category': 'art',
      },
      {
        'name': 'ニコニコ動画',
        'url': 'https://www.nicovideo.jp/',
        'icon': 'https://www.nicovideo.jp/favicon.ico',
        'description': '弹幕视频网站',
        'category': 'video',
      },
      {
        'name': 'Yahoo! JAPAN',
        'url': 'https://www.yahoo.co.jp/',
        'icon': 'https://s.yimg.jp/images/favicon.ico',
        'description': '日本雅虎门户',
        'category': 'portal',
      },
      {
        'name': '楽天',
        'url': 'https://www.rakuten.co.jp/',
        'icon': 'https://www.rakuten.co.jp/favicon.ico',
        'description': '日本乐天购物',
        'category': 'shopping',
      },
    ];

    try {
      final cacheManager = CacheManager();
      await cacheManager.cleanExpiredCache();

      // 阶段1: 开屏阶段预加载导航页前12个网站
      await _preloadStage1(cacheManager, websiteObjects);

      // 设置默认功能状态
      await _setDefaultSettings();

      // 预加载完成
      setState(() {
        _loadingText = '预加载完成！';
      });
    } catch (e) {
      // 预加载失败，继续启动
      setState(() {
        _loadingText = '预加载完成！';
      });
    }
  }

  // 阶段1: 开屏阶段预加载导航页前12个网站
  Future<void> _preloadStage1(
    CacheManager cacheManager,
    List<Map<String, dynamic>> websites,
  ) async {
    setState(() {
      _loadingText = '阶段1: 预加载前12个网站...';
    });

    final stage1Websites = websites.take(12).toList();

    // 逐个预加载，确保每个都成功
    for (int i = 0; i < stage1Websites.length; i++) {
      final website = stage1Websites[i];

      setState(() {
        _loadingText = '阶段1: 预加载第 ${i + 1}/${stage1Websites.length} 个网站...';
      });

      try {
        // 并发预加载图标和主页
        await Future.wait([
          _preloadSingleIcon(cacheManager, website),
          _preloadMainPage(cacheManager, website['url']),
        ]);

        setState(() {
          _loadingText = '阶段1: 已完成 ${i + 1}/${stage1Websites.length} 个网站';
        });
      } catch (e) {
        // 单个网站预加载失败，继续下一个
        setState(() {
          _loadingText = '阶段1: 第 ${i + 1} 个网站预加载失败，继续...';
        });
      }

      // 短暂延迟，避免过快的请求
      await Future.delayed(const Duration(milliseconds: 300));
    }
  }

  // 预加载主页内容
  Future<void> _preloadMainPage(CacheManager cacheManager, String url) async {
    try {
      // 使用CacheManager的现有方法获取并缓存页面内容
      await cacheManager.getCachedPageContent(url);
    } catch (e) {
      // 预加载失败，继续
    }
  }

  // 预加载网站图标
  Future<void> _preloadWebsiteIcons(
    CacheManager cacheManager,
    List<Map<String, dynamic>> websites,
  ) async {
    setState(() {
      _loadingText = '正在预加载网站图标...';
    });

    try {
      // 逐个预加载图标，避免并发过多
      for (final website in websites) {
        try {
          await _preloadSingleIcon(cacheManager, website);
          // 添加小延迟
          await Future.delayed(const Duration(milliseconds: 200));
        } catch (e) {
          // 单个图标失败，记录失败状态并继续其他
          final websiteName = website['name'] ?? '';
          final iconUrl = website['icon'] ?? '';
          if (websiteName.isNotEmpty && iconUrl.isNotEmpty) {
            await _SplashIconCache.markIconFailed(iconUrl, websiteName);
          }
          continue;
        }
      }
    } catch (e) {
      // 预加载失败，继续启动
    }
  }

  // 预加载单个网站图标
  Future<void> _preloadSingleIcon(
    CacheManager cacheManager,
    Map<String, dynamic> website,
  ) async {
    final websiteName = website['name'] ?? '';
    final websiteUrl = website['url'] ?? '';

    // 多个可能的图标路径
    final iconUrls = [
      website['icon'] ?? '',
      '$websiteUrl/favicon.ico',
      '$websiteUrl/apple-touch-icon.png',
      '$websiteUrl/icon.png',
      // Special cases for problematic websites
      if (websiteUrl.contains('yahoo.co.jp')) ...[
        'https://www.yahoo.co.jp/favicon.ico',
        'https://s.yimg.jp/images/favicon.ico',
        'https://s.yimg.jp/images/top/sp2/cmn/logo-ns-131205.png',
        'https://yahoo.co.jp/favicon.ico',
      ],
      if (websiteUrl.contains('syosetu.com')) ...[
        'https://syosetu.com/favicon.ico',
        'https://static.syosetu.com/favicon.ico',
        'https://syosetu.com/apple-touch-icon.png',
      ],
      if (websiteUrl.contains('pixiv.net')) ...[
        'https://www.pixiv.net/favicon.ico',
        'https://s.pximg.net/common/images/favicon.ico',
        'https://pixiv.net/favicon.ico',
      ],
      if (websiteUrl.contains('nicovideo.jp')) ...[
        'https://www.nicovideo.jp/favicon.ico',
        'https://secure-dcdn.cdn.nimg.jp/nicoaccount/usericon/s/252/2521.jpg',
        'https://nicovideo.jp/favicon.ico',
      ],
      if (websiteUrl.contains('rakuten.co.jp')) ...[
        'https://www.rakuten.co.jp/favicon.ico',
        'https://r.r10s.jp/com/img/thumb/favicon.ico',
        'https://rakuten.co.jp/favicon.ico',
      ],
    ].where((url) => url.isNotEmpty).toList();

    for (final iconUrl in iconUrls) {
      try {
        // 检查是否已缓存
        final cachedIcon = await cacheManager.getCachedFavicon(iconUrl);
        if (cachedIcon != null) {
          return; // 已有缓存，直接返回
        }

        // 尝试获取图标
        final response = await http
            .get(
              Uri.parse(iconUrl),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept':
                    'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache',
              },
            )
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
          // 验证是否为有效图片
          if (_isValidImage(response.bodyBytes)) {
            final base64Icon = base64Encode(response.bodyBytes);
            await cacheManager.cacheFavicon(iconUrl, base64Icon);

            setState(() {
              _loadingText = '已预加载 $websiteName 图标';
            });

            return; // 成功获取，退出循环
          }
        }
      } catch (e) {
        // 尝试下一个URL
        continue;
      }
    }

    // 所有URL都尝试失败，记录失败状态
    final originalIconUrl = website['icon'] ?? '';
    if (websiteName.isNotEmpty && originalIconUrl.isNotEmpty) {
      await _SplashIconCache.markIconFailed(originalIconUrl, websiteName);
    }
  }

  // 验证是否为有效图片
  bool _isValidImage(List<int> bytes) {
    if (bytes.length < 4) return false;

    // 检查常见图片格式的文件头
    final header = bytes.take(4).toList();

    // PNG: 89 50 4E 47
    if (header[0] == 0x89 &&
        header[1] == 0x50 &&
        header[2] == 0x4E &&
        header[3] == 0x47) {
      return true;
    }

    // JPEG: FF D8 FF
    if (header[0] == 0xFF && header[1] == 0xD8 && header[2] == 0xFF) {
      return true;
    }

    // GIF: 47 49 46
    if (header[0] == 0x47 && header[1] == 0x49 && header[2] == 0x46) {
      return true;
    }

    // ICO: 00 00 01 00
    if (header[0] == 0x00 &&
        header[1] == 0x00 &&
        header[2] == 0x01 &&
        header[3] == 0x00) {
      return true;
    }

    return false;
  }

  // 设置默认功能状态
  Future<void> _setDefaultSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 检查是否是首次启动
      final isFirstLaunch = prefs.getBool('first_launch') ?? true;

      if (isFirstLaunch) {
        // 首次启动，设置默认开启AI翻译和网络优化
        await prefs.setBool('ai_translate_enabled', true);
        await prefs.setBool('network_optimize_enabled', true);
        await prefs.setBool('first_launch', false);

        setState(() {
          _loadingText = 'AI翻译和网络加速已启用！';
        });

        await Future.delayed(const Duration(milliseconds: 1000));
      }
    } catch (e) {
      // 设置失败，继续启动
    }
  }

  void _navigateToHome() {
    Navigator.of(context).pushReplacement(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const HomePage(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _logoController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1E3A8A), // 深蓝色背景
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Logo动画
                    AnimatedBuilder(
                      animation: _logoAnimation,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _logoAnimation.value,
                          child: Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.language,
                              size: 60,
                              color: Color(0xFF1E3A8A),
                            ),
                          ),
                        );
                      },
                    ),
                    const SizedBox(height: 30),
                    // 应用名称
                    const Text(
                      'Japan Hub',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 2,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // 底部加载区域
            Padding(
              padding: const EdgeInsets.all(40),
              child: Column(
                children: [
                  // 加载文字
                  Text(
                    _loadingText,
                    style: const TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                  const SizedBox(height: 20),
                  // 进度条
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Container(
                        width: double.infinity,
                        height: 4,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _progressAnimation.value,
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
                              ),
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
