# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 55ms
  [gap of 34ms]
generate_cxx_metadata completed in 114ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 44ms
  [gap of 28ms]
generate_cxx_metadata completed in 86ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 51ms
  [gap of 19ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 101ms

