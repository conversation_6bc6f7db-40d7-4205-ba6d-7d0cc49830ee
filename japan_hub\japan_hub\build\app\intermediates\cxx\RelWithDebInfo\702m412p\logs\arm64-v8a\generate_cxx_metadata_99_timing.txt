# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 15ms
  execute-generate-process
    exec-configure 6472ms
    [gap of 63ms]
  execute-generate-process completed in 6535ms
  [gap of 78ms]
  write-metadata-json-to-file 16ms
generate_cxx_metadata completed in 6691ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 67ms
  [gap of 28ms]
generate_cxx_metadata completed in 116ms

