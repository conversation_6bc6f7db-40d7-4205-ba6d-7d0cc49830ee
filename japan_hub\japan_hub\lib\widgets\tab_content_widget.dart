import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import '../models/tab_model.dart';

/// 标签页内容组件
class TabContentWidget extends StatefulWidget {
  final TabModel tab;
  final Function(String) onUrlChanged;
  final Function(String) onTitleChanged;
  final Function(bool) onLoadingChanged;

  const TabContentWidget({
    super.key,
    required this.tab,
    required this.onUrlChanged,
    required this.onTitleChanged,
    required this.onLoadingChanged,
  });

  @override
  State<TabContentWidget> createState() => _TabContentWidgetState();
}

class _TabContentWidgetState extends State<TabContentWidget> {
  late TextEditingController _addressController;
  late FocusNode _addressFocusNode;
  InAppWebViewController? _webViewController;
  bool _showErrorPage = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _addressController = TextEditingController(text: widget.tab.url);
    _addressFocusNode = FocusNode();
  }

  @override
  void dispose() {
    _addressController.dispose();
    _addressFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 地址栏
        _buildAddressBar(),
        
        // WebView内容
        Expanded(
          child: _showErrorPage ? _buildErrorPage() : _buildWebView(),
        ),
      ],
    );
  }

  /// 构建地址栏
  Widget _buildAddressBar() {
    return Container(
      height: 48,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            onPressed: _webViewController?.canGoBack() == true 
              ? () => _webViewController?.goBack()
              : null,
            icon: Icon(
              Icons.arrow_back,
              color: _webViewController?.canGoBack() == true 
                ? Colors.grey[700] 
                : Colors.grey[400],
            ),
            iconSize: 20,
          ),
          
          // 前进按钮
          IconButton(
            onPressed: _webViewController?.canGoForward() == true
              ? () => _webViewController?.goForward()
              : null,
            icon: Icon(
              Icons.arrow_forward,
              color: _webViewController?.canGoForward() == true
                ? Colors.grey[700]
                : Colors.grey[400],
            ),
            iconSize: 20,
          ),
          
          // 刷新按钮
          IconButton(
            onPressed: () => _webViewController?.reload(),
            icon: Icon(Icons.refresh, color: Colors.grey[700]),
            iconSize: 20,
          ),
          
          // 地址栏
          Expanded(
            child: Container(
              height: 36,
              margin: const EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(18),
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              child: Row(
                children: [
                  const SizedBox(width: 12),
                  Icon(Icons.search, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _addressController,
                      focusNode: _addressFocusNode,
                      decoration: const InputDecoration(
                        hintText: '搜索或输入网址',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                      onSubmitted: _navigateToUrl,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建WebView
  Widget _buildWebView() {
    if (widget.tab.url.isEmpty) {
      return _buildBlankPage();
    }

    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(widget.tab.url)),
      initialSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        domStorageEnabled: true,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,
        supportZoom: true,
        builtInZoomControls: false,
        displayZoomControls: false,
        userAgent: 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
      ),
      onWebViewCreated: (controller) {
        _webViewController = controller;
        widget.tab.controller = controller;
      },
      onLoadStart: (controller, url) {
        if (url != null) {
          final urlString = url.toString();
          _addressController.text = urlString;
          widget.onUrlChanged(urlString);
          widget.onLoadingChanged(true);
        }
      },
      onLoadStop: (controller, url) {
        if (url != null) {
          widget.onLoadingChanged(false);
        }
      },
      onTitleChanged: (controller, title) {
        if (title != null) {
          widget.onTitleChanged(title);
        }
      },
      onReceivedError: (controller, request, error) {
        setState(() {
          _showErrorPage = true;
          _errorMessage = error.description;
        });
        widget.onLoadingChanged(false);
      },
    );
  }

  /// 构建空白页面
  Widget _buildBlankPage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[50],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(60),
            ),
            child: Icon(
              Icons.tab,
              size: 60,
              color: Colors.blue.shade300,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            '新建页签',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '在上方地址栏输入网址或搜索内容',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误页面
  Widget _buildErrorPage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[50],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 80,
            color: Colors.red[300],
          ),
          const SizedBox(height: 24),
          Text(
            '网页无法打开',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            _errorMessage,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _showErrorPage = false;
              });
              _webViewController?.reload();
            },
            child: const Text('重新加载'),
          ),
        ],
      ),
    );
  }

  /// 导航到URL
  void _navigateToUrl(String url) {
    String finalUrl = url.trim();

    // 智能URL处理
    if (!finalUrl.startsWith('http://') &&
        !finalUrl.startsWith('https://') &&
        !finalUrl.startsWith('ftp://') &&
        !finalUrl.startsWith('file://')) {
      if (finalUrl.contains('.') && !finalUrl.contains(' ')) {
        if (!finalUrl.startsWith('www.') && _shouldAddWww(finalUrl)) {
          finalUrl = 'https://www.$finalUrl';
        } else {
          finalUrl = 'https://$finalUrl';
        }
      } else {
        finalUrl = 'https://www.google.com/search?q=${Uri.encodeComponent(finalUrl)}';
      }
    }

    setState(() {
      _showErrorPage = false;
    });

    widget.onUrlChanged(finalUrl);
    widget.onLoadingChanged(true);
    _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(finalUrl)));
  }

  /// 判断域名是否需要添加www前缀
  bool _shouldAddWww(String domain) {
    final wwwDomains = [
      'baidu.com', 'google.com', 'facebook.com', 'twitter.com',
      'instagram.com', 'linkedin.com', 'github.com', 'stackoverflow.com',
      'amazon.com', 'taobao.com', 'tmall.com', 'jd.com', 'qq.com',
      'weibo.com', 'sina.com.cn', 'sohu.com', 'netease.com', '163.com',
      'yahoo.com', 'bing.com', 'youtube.com', 'netflix.com', 'spotify.com',
    ];
    return wwwDomains.contains(domain.toLowerCase());
  }
}
