<variant
    name="release"
    package="com.example.japan_hub"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="D:\lk\japan_hub\japan_hub\build\app\intermediates\merged_manifest\release\processReleaseMainManifest\AndroidManifest.xml"
    proguardFiles="D:\lk\japan_hub\japan_hub\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.7.3;D:\Scoop\persist\puro\data\envs\stable\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="D:\lk\japan_hub\japan_hub\build\app\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\c7515c6e5f6303611b1224848ff8ebc4\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <artifact
      classOutputs="D:\lk\japan_hub\japan_hub\build\app\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\lk\japan_hub\japan_hub\build\app\tmp\kotlin-classes\release;D:\lk\japan_hub\japan_hub\build\app\kotlinToolingMetadata;D:\lk\japan_hub\japan_hub\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\processReleaseResources\R.jar"
      type="MAIN"
      applicationId="com.example.japan_hub"
      generatedSourceFolders="D:\lk\japan_hub\japan_hub\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\lk\japan_hub\japan_hub\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\c7515c6e5f6303611b1224848ff8ebc4\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
