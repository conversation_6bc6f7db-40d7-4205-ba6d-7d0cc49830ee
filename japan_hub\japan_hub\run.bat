@echo off
echo Starting JapanHub Flutter App...
echo Choose running mode:
echo 1. Debug mode (slower, with hot reload)
echo 2. Build and serve (faster, production-like)
set /p choice="Enter your choice (1 or 2): "

if "%choice%"=="1" (
    echo Starting in debug mode...
    puro flutter run -d chrome --web-port=8080
) else if "%choice%"=="2" (
    echo Building for web...
    puro flutter build web
    echo Starting HTTP server...
    echo Open http://localhost:8080 in your browser
    python -m http.server 8080 --directory build/web
) else (
    echo Invalid choice. Starting in debug mode...
    puro flutter run -d chrome --web-port=8080
)
