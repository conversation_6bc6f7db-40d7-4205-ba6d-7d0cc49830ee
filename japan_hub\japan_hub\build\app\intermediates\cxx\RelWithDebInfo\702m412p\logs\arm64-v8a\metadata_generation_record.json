[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: arm64-v8a", "file_": "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'D:\\lk\\japan_hub\\japan_hub\\build\\.cxx\\RelWithDebInfo\\702m412p\\arm64-v8a\\android_gradle_build.json' was up-to-date", "file_": "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "release|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]