import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;

/// 翻译服务类 - 处理各种翻译API的调用
class TranslationService {
  // 腾讯云API配置 - 使用用户提供的新有效密钥
  static const String tencentSecretId = 'AKIDLxOdjdKrETYR7d0CGBvWw9D5kFgx3voh';
  static const String tencentSecretKey = 'qcTAHDwSlrkZJxc7VikdM6f0FKBqIdkd';
  static const String tencentEndpoint = 'https://tmt.tencentcloudapi.com/';
  static const String tencentService = 'tmt';
  static const String tencentVersion = '2018-03-21';
  static const String tencentAction = 'TextTranslate';
  static const String tencentRegion = 'ap-beijing';

  // 百度翻译API配置 - 使用用户提供的有效密钥
  static const String baiduAppId = '20250724002415106';
  static const String baiduSecretKey = 'vET_uli_l70K5BZ9BU5U';
  static const String baiduEndpoint =
      'https://fanyi-api.baidu.com/api/trans/vip/translate';

  // DeepSeek V3翻译API配置
  static const String deepseekApiKey = '***********************************';
  static const String deepseekEndpoint =
      'https://api.deepseek.com/v1/chat/completions';
  static const String deepseekModel = 'deepseek-chat';

  /// 调用腾讯翻译API
  static Future<String?> translateWithTencent(String text) async {
    try {
      print('🔄 开始调用腾讯翻译API，文本: $text');

      // 直接解决网络超时问题，不再逃避

      // 构建请求参数 - 腾讯API v3.0使用JSON格式
      final requestBody = {
        'SourceText': text,
        'Source': 'ja',
        'Target': 'zh',
        'ProjectId': 0,
      };

      // 获取时间戳
      final timestamp = _getCurrentTimestamp();

      // 生成签名
      final signature = _generateTencentSignature(requestBody, timestamp);

      // 构建请求头
      final headers = {
        'Authorization': signature,
        'Content-Type': 'application/json; charset=utf-8',
        'Host': 'tmt.tencentcloudapi.com',
        'X-TC-Action': tencentAction,
        'X-TC-Version': tencentVersion,
        'X-TC-Region': tencentRegion,
        'X-TC-Timestamp': timestamp.toString(),
      };

      print('🌐 发送腾讯API请求到: $tencentEndpoint');
      print('📤 请求体: ${jsonEncode(requestBody)}');
      print('📤 请求头: ${jsonEncode(headers)}');

      // 发送HTTP请求 - 修复网络问题
      print('🚀 开始发送HTTP请求...');

      // 创建HTTP客户端，配置更宽松的设置
      final client = http.Client();
      late http.Response response;

      try {
        response = await client
            .post(
              Uri.parse(tencentEndpoint),
              headers: {
                ...headers,
                'User-Agent': 'JapanHub/1.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive',
              },
              body: jsonEncode(requestBody),
            )
            .timeout(
              const Duration(seconds: 30), // 增加超时时间到30秒
              onTimeout: () {
                print('⏰ HTTP请求超时（30秒）');
                throw Exception('HTTP请求超时（30秒）');
              },
            );

        client.close();
      } catch (e) {
        client.close();
        rethrow;
      }

      print('📥 响应状态: ${response.statusCode}');
      print('📥 响应头: ${response.headers}');
      print('📥 响应体: ${response.body}');

      if (response.statusCode != 200) {
        print('❌ API请求失败，状态码: ${response.statusCode}');
        print('❌ 响应内容: ${response.body}');
        // 返回错误信息而不是null，方便调试
        return 'API请求失败，状态码: ${response.statusCode}，响应: ${response.body}';
      }

      final data = jsonDecode(response.body);
      print('📋 API响应数据: ${jsonEncode(data)}');

      if (data['Response'] != null && data['Response']['TargetText'] != null) {
        final translation = data['Response']['TargetText'] as String;
        print('✅ 腾讯API翻译成功: $text → $translation');
        return translation;
      }

      if (data['Response'] != null && data['Response']['Error'] != null) {
        print('❌ 腾讯API返回错误: ${data['Response']['Error']}');
        // 返回错误信息而不是null
        return 'API错误: ${jsonEncode(data['Response']['Error'])}';
      }

      print('❌ 腾讯API返回数据格式不正确');
      // 返回完整响应而不是null
      return 'API响应格式错误: ${jsonEncode(data)}';
    } catch (error) {
      print('❌ 腾讯API调用异常: $error');
      // 返回错误信息而不是null
      return 'API调用异常: $error';
    }
  }

  /// 生成腾讯云API签名
  static String _generateTencentSignature(
    Map<String, dynamic> requestBody,
    int timestamp,
  ) {
    final date = _formatDate(
      DateTime.fromMillisecondsSinceEpoch(timestamp * 1000),
    );

    // 第一步：拼接规范请求串
    final httpRequestMethod = 'POST';
    final canonicalUri = '/';
    final canonicalQueryString = '';
    final canonicalHeaders =
        'content-type:application/json; charset=utf-8\n'
        'host:tmt.tencentcloudapi.com\n';
    final signedHeaders = 'content-type;host';
    final hashedRequestPayload = _sha256Hash(jsonEncode(requestBody));

    final canonicalRequest =
        '$httpRequestMethod\n'
        '$canonicalUri\n'
        '$canonicalQueryString\n'
        '$canonicalHeaders\n'
        '$signedHeaders\n'
        '$hashedRequestPayload';

    // 第二步：拼接待签名字符串
    final algorithm = 'TC3-HMAC-SHA256';
    final requestTimestamp = timestamp.toString();
    final credentialScope = '$date/$tencentService/tc3_request';
    final hashedCanonicalRequest = _sha256Hash(canonicalRequest);

    final stringToSign =
        '$algorithm\n'
        '$requestTimestamp\n'
        '$credentialScope\n'
        '$hashedCanonicalRequest';

    // 第三步：计算签名
    final secretDate = _hmacSha256('TC3$tencentSecretKey', date);
    final secretService = _hmacSha256(secretDate, tencentService);
    final secretSigning = _hmacSha256(secretService, 'tc3_request');
    final signature = _hmacSha256Hex(secretSigning, stringToSign);

    // 第四步：拼接 Authorization
    final authorization =
        '$algorithm '
        'Credential=$tencentSecretId/$credentialScope, '
        'SignedHeaders=$signedHeaders, '
        'Signature=$signature';

    return authorization;
  }

  /// 获取当前时间戳
  static int _getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch ~/ 1000;
  }

  /// 格式化日期
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// SHA256哈希
  static String _sha256Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// MD5哈希（用于百度翻译API）
  static String _md5Hash(String input) {
    final bytes = utf8.encode(input);
    final digest = md5.convert(bytes);
    return digest.toString();
  }

  /// HMAC-SHA256
  static List<int> _hmacSha256(dynamic key, String data) {
    final keyBytes = key is String ? utf8.encode(key) : key as List<int>;
    final dataBytes = utf8.encode(data);
    final hmac = Hmac(sha256, keyBytes);
    final digest = hmac.convert(dataBytes);
    return digest.bytes;
  }

  /// HMAC-SHA256 十六进制
  static String _hmacSha256Hex(List<int> key, String data) {
    final dataBytes = utf8.encode(data);
    final hmac = Hmac(sha256, key);
    final digest = hmac.convert(dataBytes);
    return digest.toString();
  }

  /// 百度翻译API
  static Future<String?> translateWithBaidu(String text) async {
    try {
      print('🔄 开始调用百度翻译API，文本: $text');

      // 生成随机数和时间戳
      final salt = DateTime.now().millisecondsSinceEpoch.toString();

      // 生成MD5签名
      final signStr = '$baiduAppId$text$salt$baiduSecretKey';
      final sign = _md5Hash(signStr);

      print('🔐 百度API签名详情:');
      print('  - APPID: $baiduAppId');
      print('  - 文本: $text');
      print('  - 盐值: $salt');
      print('  - 密钥: $baiduSecretKey');
      print('  - 签名字符串: $signStr');
      print('  - MD5签名: $sign');

      // 构建请求参数
      final params = {
        'q': text,
        'from': 'jp',
        'to': 'zh',
        'appid': baiduAppId,
        'salt': salt,
        'sign': sign,
      };

      print('🌐 发送百度API请求到: $baiduEndpoint');
      print('📤 请求参数: ${jsonEncode(params)}');

      // 创建HTTP客户端
      final client = http.Client();
      late http.Response response;

      try {
        response = await client
            .post(
              Uri.parse(baiduEndpoint),
              headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'JapanHub/1.0',
                'Accept': 'application/json',
              },
              body: params.entries
                  .map(
                    (e) =>
                        '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}',
                  )
                  .join('&'),
            )
            .timeout(
              const Duration(seconds: 30),
              onTimeout: () {
                print('⏰ 百度API请求超时（30秒）');
                throw Exception('百度API请求超时（30秒）');
              },
            );

        client.close();
      } catch (e) {
        client.close();
        rethrow;
      }

      print('📥 百度API响应状态: ${response.statusCode}');
      print('📥 百度API响应头: ${response.headers}');
      print('📥 百度API响应体: ${response.body}');
      print('📥 百度API响应体长度: ${response.body.length}');
      print('📥 百度API响应体类型: ${response.body.runtimeType}');

      if (response.statusCode != 200) {
        final errorMsg =
            '百度API请求失败，状态码: ${response.statusCode}，响应: ${response.body}';
        print('❌ $errorMsg');
        return errorMsg;
      }

      if (response.body.isEmpty) {
        final errorMsg = '百度API返回空响应体';
        print('❌ $errorMsg');
        return errorMsg;
      }

      try {
        final data = jsonDecode(response.body);
        print('📋 百度API响应数据: ${jsonEncode(data)}');
        print('📋 百度API响应数据类型: ${data.runtimeType}');
        print('📋 百度API响应数据键: ${data.keys.toList()}');

        if (data['trans_result'] != null && data['trans_result'].isNotEmpty) {
          final translation = data['trans_result'][0]['dst'] as String;
          print('✅ 百度API翻译成功: $text → $translation');
          return translation;
        }

        if (data['error_code'] != null) {
          final errorMsg =
              '百度API错误: ${data['error_code']} - ${data['error_msg']}';
          print('❌ $errorMsg');
          return errorMsg;
        }

        final errorMsg = '百度API返回数据格式不正确: ${jsonEncode(data)}';
        print('❌ $errorMsg');
        return errorMsg;
      } catch (e) {
        final errorMsg = '百度API响应JSON解析失败: $e，原始响应: ${response.body}';
        print('❌ $errorMsg');
        return errorMsg;
      }
    } catch (error) {
      print('❌ 百度API调用异常: $error');
      return '百度API调用异常: $error';
    }
  }

  /// 调用DeepSeek V3翻译API
  static Future<String?> translateWithDeepSeek(String text) async {
    try {
      print('🔄 开始调用DeepSeek V3翻译API，文本: $text');

      // 构建请求体
      final requestBody = {
        'model': deepseekModel,
        'messages': [
          {
            'role': 'system',
            'content': '你是一个专业的日语翻译助手。请将用户输入的日语文本翻译成中文。只返回翻译结果，不要添加任何解释或额外内容。',
          },
          {'role': 'user', 'content': '请将以下日语文本翻译成中文：$text'},
        ],
        'temperature': 0.1,
        'max_tokens': 1000,
        'stream': false,
      };

      // 构建请求头
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $deepseekApiKey',
      };

      print('🌐 发送DeepSeek API请求到: $deepseekEndpoint');
      print('📤 请求体: ${jsonEncode(requestBody)}');

      // 发送HTTP请求
      final response = await http
          .post(
            Uri.parse(deepseekEndpoint),
            headers: headers,
            body: jsonEncode(requestBody),
          )
          .timeout(Duration(seconds: 30));

      print('📥 DeepSeek API响应状态: ${response.statusCode}');
      print('📥 DeepSeek API响应体: ${response.body}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        if (responseData['choices'] != null &&
            responseData['choices'].isNotEmpty &&
            responseData['choices'][0]['message'] != null) {
          final translatedText =
              responseData['choices'][0]['message']['content']
                  ?.toString()
                  .trim();

          if (translatedText != null && translatedText.isNotEmpty) {
            print('✅ DeepSeek翻译成功: $text → $translatedText');
            return translatedText;
          }
        }

        print('❌ DeepSeek API返回格式异常');
        return 'DeepSeek API返回格式异常';
      } else {
        final errorMsg =
            'DeepSeek API请求失败: ${response.statusCode} - ${response.body}';
        print('❌ $errorMsg');
        return errorMsg;
      }
    } catch (error) {
      print('❌ DeepSeek API调用异常: $error');
      return 'DeepSeek API调用异常: $error';
    }
  }

  /// 有道翻译API（预留）
  static Future<String?> translateWithYoudao(String text) async {
    // TODO: 实现有道翻译API
    print('🔄 有道翻译API调用（待实现）: $text');
    return null;
  }

  /// 根据选择的API进行翻译
  static Future<String?> translate(String text, String apiType) async {
    print('🔍 TranslationService.translate 被调用');
    print('📝 文本: $text');
    print('🎯 API类型: $apiType');
    print('🔄 开始API类型判断...');

    switch (apiType.toLowerCase()) {
      case 'tencent':
        print('✅ 匹配到腾讯API，调用 translateWithTencent');
        return await translateWithTencent(text);
      case 'baidu':
        print('✅ 匹配到百度API，调用 translateWithBaidu');
        return await translateWithBaidu(text);
      case 'deepseek':
        print('✅ 匹配到DeepSeek API，调用 translateWithDeepSeek');
        return await translateWithDeepSeek(text);
      case 'youdao':
        print('✅ 匹配到有道API，调用 translateWithYoudao');
        return await translateWithYoudao(text);
      default:
        print('❌ 未知的API类型: $apiType');
        print('❌ 可用的API类型: tencent, baidu, deepseek, youdao');
        return '未知的API类型: $apiType，可用类型: tencent, baidu, deepseek, youdao';
    }
  }

  /// 本地翻译功能（当网络问题时使用）
  static String _getLocalTranslation(String text) {
    // 简单的日语翻译映射
    final translations = {
      'こんにちは': '你好',
      'ありがとう': '谢谢',
      'すみません': '对不起',
      'はい': '是',
      'いいえ': '不是',
      'おはよう': '早上好',
      'こんばんは': '晚上好',
      'さようなら': '再见',
      'お疲れ様': '辛苦了',
      '頑張って': '加油',
      '大丈夫': '没关系',
      '分かりました': '明白了',
      '日本': '日本',
      '中国': '中国',
      '学校': '学校',
      '会社': '公司',
      '家': '家',
      '友達': '朋友',
      '先生': '老师',
      '学生': '学生',
      '今日': '今天',
      '明日': '明天',
      '昨日': '昨天',
      '時間': '时间',
      '場所': '地方',
      '人': '人',
      '物': '东西',
      '食べ物': '食物',
      '飲み物': '饮料',
      '本': '书',
      '映画': '电影',
      '音楽': '音乐',
      '写真': '照片',
    };

    // 直接翻译
    if (translations.containsKey(text)) {
      return translations[text]!;
    }

    // 部分匹配翻译
    for (final entry in translations.entries) {
      if (text.contains(entry.key)) {
        return text.replaceAll(entry.key, entry.value);
      }
    }

    // 如果没有匹配，返回带标记的翻译
    return '[测试翻译]$text';
  }
}
