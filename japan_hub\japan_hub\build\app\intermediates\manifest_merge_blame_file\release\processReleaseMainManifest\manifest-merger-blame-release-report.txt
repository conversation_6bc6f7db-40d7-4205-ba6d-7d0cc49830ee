1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.japan_hub"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- 网络权限 -->
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:3:5-67
11-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:4:5-79
12-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:4:22-76
13    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
13-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:5:5-76
13-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:5:22-73
14    <!--
15         Required to query activities that can process text, see:
16         https://developer.android.com/training/package-visibility and
17         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
18
19         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
20    -->
21    <queries>
21-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:46:5-51:15
22        <intent>
22-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:47:9-50:18
23            <action android:name="android.intent.action.PROCESS_TEXT" />
23-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:48:13-72
23-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:48:21-70
24
25            <data android:mimeType="text/plain" />
25-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:49:13-50
25-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:49:19-48
26        </intent>
27        <intent>
27-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-10:18
28            <action android:name="android.support.customtabs.action.CustomTabsService" />
28-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-90
28-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-87
29        </intent>
30    </queries>
31
32    <permission
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
33        android:name="com.example.japan_hub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.example.japan_hub.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
37
38    <application
39        android:name="android.app.Application"
39-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:9:9-42
40        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\1eb6e469cc614b5f4ea976541f34c484\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
41        android:extractNativeLibs="true"
42        android:icon="@mipmap/ic_launcher"
42-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:10:9-43
43        android:label="NACG翻译君"
43-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:8:9-32
44        android:networkSecurityConfig="@xml/network_security_config"
44-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:12:9-69
45        android:usesCleartextTraffic="true" >
45-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:11:9-44
46        <activity
46-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:13:9-34:20
47            android:name="com.example.japan_hub.MainActivity"
47-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:14:13-41
48            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
48-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:19:13-163
49            android:exported="true"
49-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:15:13-36
50            android:hardwareAccelerated="true"
50-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:20:13-47
51            android:launchMode="singleTop"
51-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:16:13-43
52            android:taskAffinity=""
52-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:17:13-36
53            android:theme="@style/LaunchTheme"
53-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:18:13-47
54            android:windowSoftInputMode="adjustResize" >
54-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:21:13-55
55
56            <!--
57                 Specifies an Android theme to apply to this Activity as soon as
58                 the Android process has started. This theme is visible to the user
59                 while the Flutter UI initializes. After that, this theme continues
60                 to determine the Window background behind the Flutter UI.
61            -->
62            <meta-data
62-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:26:13-29:17
63                android:name="io.flutter.embedding.android.NormalTheme"
63-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:27:15-70
64                android:resource="@style/NormalTheme" />
64-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:28:15-52
65
66            <intent-filter>
66-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:30:13-33:29
67                <action android:name="android.intent.action.MAIN" />
67-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:31:17-68
67-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:31:25-66
68
69                <category android:name="android.intent.category.LAUNCHER" />
69-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:32:17-76
69-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:32:27-74
70            </intent-filter>
71        </activity>
72        <!--
73             Don't delete the meta-data below.
74             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
75        -->
76        <meta-data
76-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:37:9-39:33
77            android:name="flutterEmbedding"
77-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:38:13-44
78            android:value="2" />
78-->D:\lk\japan_hub\japan_hub\android\app\src\main\AndroidManifest.xml:39:13-30
79
80        <activity
80-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:9-18:47
81            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
81-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-112
82            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
82-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-137
83            android:exported="false"
83-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-37
84            android:theme="@style/AppTheme" />
84-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-44
85        <activity
85-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-22:55
86            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
86-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-120
87            android:exported="false"
87-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-37
88            android:theme="@style/ThemeTransparent" />
88-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-52
89        <activity
89-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:9-26:55
90            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
90-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-114
91            android:exported="false"
91-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-37
92            android:theme="@style/ThemeTransparent" />
92-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-52
93        <activity
93-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:9-31:55
94            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
94-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-134
95            android:exported="false"
95-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-37
96            android:launchMode="singleInstance"
96-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:13-48
97            android:theme="@style/ThemeTransparent" />
97-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:31:13-52
98        <activity
98-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:32:9-36:55
99            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
99-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:33:13-128
100            android:exported="false"
100-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:34:13-37
101            android:launchMode="singleInstance"
101-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:35:13-48
102            android:theme="@style/ThemeTransparent" />
102-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:36:13-52
103
104        <receiver
104-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:38:9-41:40
105            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
105-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:39:13-119
106            android:enabled="true"
106-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:40:13-35
107            android:exported="false" />
107-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:41:13-37
108
109        <meta-data
109-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:43:9-45:36
110            android:name="io.flutter.embedded_views_preview"
110-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:44:13-61
111            android:value="true" />
111-->[:flutter_inappwebview_android] D:\lk\japan_hub\japan_hub\build\flutter_inappwebview_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:45:13-33
112
113        <activity
113-->[:url_launcher_android] D:\lk\japan_hub\japan_hub\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
114            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
114-->[:url_launcher_android] D:\lk\japan_hub\japan_hub\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
115            android:exported="false"
115-->[:url_launcher_android] D:\lk\japan_hub\japan_hub\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
116            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
116-->[:url_launcher_android] D:\lk\japan_hub\japan_hub\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
117
118        <provider
118-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
119            android:name="androidx.startup.InitializationProvider"
119-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
120            android:authorities="com.example.japan_hub.androidx-startup"
120-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
121            android:exported="false" >
121-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
122            <meta-data
122-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
123                android:name="androidx.emoji2.text.EmojiCompatInitializer"
123-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
124                android:value="androidx.startup" />
124-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a10027d9bd367529d888e25a1e79b162\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
125            <meta-data
125-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16630f24f63dbd17c9d46e0eefc3390e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
126                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
126-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16630f24f63dbd17c9d46e0eefc3390e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
127                android:value="androidx.startup" />
127-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\16630f24f63dbd17c9d46e0eefc3390e\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
128            <meta-data
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
129                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
130                android:value="androidx.startup" />
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
131        </provider>
132
133        <uses-library
133-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
134            android:name="androidx.window.extensions"
134-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
135            android:required="false" />
135-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
136        <uses-library
136-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
137            android:name="androidx.window.sidecar"
137-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
138            android:required="false" />
138-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\4ae030c953956f29b8dc170195675fcc\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
139
140        <receiver
140-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
141            android:name="androidx.profileinstaller.ProfileInstallReceiver"
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
142            android:directBootAware="false"
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
143            android:enabled="true"
143-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
144            android:exported="true"
144-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
145            android:permission="android.permission.DUMP" >
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
146            <intent-filter>
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
147                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
148            </intent-filter>
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
150                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
153                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
156                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\5e2a0fbdf233c4c0138b666ea4477f64\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
157            </intent-filter>
158        </receiver>
159    </application>
160
161</manifest>
