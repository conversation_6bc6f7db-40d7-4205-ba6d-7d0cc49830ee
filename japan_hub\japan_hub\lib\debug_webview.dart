import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class DebugWebViewPage extends StatefulWidget {
  final String url;
  final String title;

  const DebugWebViewPage({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<DebugWebViewPage> createState() => _DebugWebViewPageState();
}

class _DebugWebViewPageState extends State<DebugWebViewPage> {
  InAppWebViewController? webViewController;
  String currentUrl = "";
  String currentTitle = "";
  double progress = 0;
  bool isLoading = true;
  List<String> debugLogs = [];

  void _addLog(String message) {
    setState(() {
      debugLogs.add('${DateTime.now().toString().substring(11, 19)}: $message');
    });
    debugPrint('WebView Debug: $message');
  }

  @override
  void initState() {
    super.initState();
    currentUrl = widget.url;
    currentTitle = widget.title;
    _addLog('初始化WebView: ${widget.url}');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('调试: ${widget.title}'),
        backgroundColor: Colors.red[50],
        foregroundColor: Colors.red[800],
        actions: [
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: _showDebugLogs,
          ),
        ],
      ),
      body: Column(
        children: [
          // 状态栏
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            color: Colors.red[50],
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('当前URL: $currentUrl', style: const TextStyle(fontSize: 12)),
                Text('标题: $currentTitle', style: const TextStyle(fontSize: 12)),
                Text('加载中: $isLoading', style: const TextStyle(fontSize: 12)),
                Text('进度: ${(progress * 100).toInt()}%', style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
          // 进度条
          if (isLoading)
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[200],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
            ),
          // WebView
          Expanded(
            child: kIsWeb ? _buildWebWarning() : _buildWebView(),
          ),
        ],
      ),
    );
  }

  Widget _buildWebWarning() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.warning, size: 64, color: Colors.orange),
          const SizedBox(height: 16),
          const Text(
            'Web平台限制',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          const Text(
            '在Web平台上，InAppWebView使用iframe实现，受到浏览器安全策略限制。\n大多数网站会拒绝在iframe中显示。',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              _addLog('尝试强制加载WebView');
              setState(() {});
            },
            child: const Text('强制尝试'),
          ),
        ],
      ),
    );
  }

  Widget _buildWebView() {
    return InAppWebView(
      initialUrlRequest: URLRequest(url: WebUri(widget.url)),
      initialSettings: InAppWebViewSettings(
        isInspectable: true, // 强制开启调试
        javaScriptEnabled: true,
        domStorageEnabled: true,
        useWideViewPort: true,
        loadWithOverviewMode: true,
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        allowUniversalAccessFromFileURLs: true,
        allowFileAccessFromFileURLs: true,
      ),
      onWebViewCreated: (controller) {
        webViewController = controller;
        _addLog('WebView已创建');
      },
      onLoadStart: (controller, url) {
        _addLog('开始加载: ${url?.toString() ?? "null"}');
        setState(() {
          currentUrl = url?.toString() ?? '';
          isLoading = true;
        });
      },
      onProgressChanged: (controller, progress) {
        _addLog('加载进度: $progress%');
        setState(() {
          this.progress = progress / 100;
        });
      },
      onLoadStop: (controller, url) async {
        _addLog('加载完成: ${url?.toString() ?? "null"}');
        setState(() {
          currentUrl = url?.toString() ?? '';
          isLoading = false;
        });
        
        // 获取页面标题
        final title = await controller.getTitle();
        _addLog('页面标题: ${title ?? "null"}');
        if (title != null && title.isNotEmpty) {
          setState(() {
            currentTitle = title;
          });
        }
      },
      onReceivedError: (controller, request, error) {
        _addLog('错误: ${error.type} - ${error.description}');
        setState(() {
          isLoading = false;
        });
      },
      onReceivedHttpError: (controller, request, errorResponse) {
        _addLog('HTTP错误: ${errorResponse.statusCode} - ${errorResponse.reasonPhrase}');
      },
      onConsoleMessage: (controller, consoleMessage) {
        _addLog('控制台: ${consoleMessage.message}');
      },
    );
  }

  void _showDebugLogs() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('调试日志'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: debugLogs.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Text(
                  debugLogs[index],
                  style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                debugLogs.clear();
              });
              Navigator.of(context).pop();
            },
            child: const Text('清除日志'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
