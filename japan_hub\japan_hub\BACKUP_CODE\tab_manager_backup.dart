// 备份：多标签页管理器
// 保存时间：2025-07-28 02:30
// 来源：当前账号的完整实现

import 'package:flutter/foundation.dart';
import '../models/tab_model.dart';

/// 标签页管理器
/// 负责管理所有标签页的创建、切换、关闭等操作
class TabManager extends ChangeNotifier {
  final List<TabModel> _tabs = [];
  int _currentTabIndex = -1;

  /// 获取所有标签页
  List<TabModel> get tabs => List.unmodifiable(_tabs);

  /// 获取当前标签页索引
  int get currentTabIndex => _currentTabIndex;

  /// 获取当前标签页
  TabModel? get currentTab {
    if (_currentTabIndex >= 0 && _currentTabIndex < _tabs.length) {
      return _tabs[_currentTabIndex];
    }
    return null;
  }

  /// 是否有标签页
  bool get hasTabs => _tabs.isNotEmpty;

  /// 创建新标签页
  TabModel createNewTab({String? url, String? title}) {
    final tab = TabModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? '新建页签',
      url: url ?? '',
    );
    
    _tabs.add(tab);
    _currentTabIndex = _tabs.length - 1;
    
    debugPrint('📑 创建新标签页: ${tab.id}, 总数: ${_tabs.length}');
    notifyListeners();
    
    return tab;
  }

  /// 切换到指定标签页
  void switchToTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      _currentTabIndex = index;
      debugPrint('📑 切换到标签页: $index');
      notifyListeners();
    }
  }

  /// 关闭标签页
  void closeTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      final tab = _tabs[index];
      _tabs.removeAt(index);
      
      // 调整当前标签页索引
      if (_currentTabIndex >= _tabs.length) {
        _currentTabIndex = _tabs.length - 1;
      } else if (_currentTabIndex > index) {
        _currentTabIndex--;
      }
      
      debugPrint('📑 关闭标签页: ${tab.id}, 剩余: ${_tabs.length}');
      notifyListeners();
    }
  }

  /// 更新标签页信息
  void updateTab(String tabId, {String? url, String? title, bool? isLoading}) {
    final index = _tabs.indexWhere((tab) => tab.id == tabId);
    if (index != -1) {
      final tab = _tabs[index];
      _tabs[index] = tab.copyWith(
        url: url,
        title: title,
        isLoading: isLoading,
      );
      notifyListeners();
    }
  }

  /// 获取标签页
  TabModel? getTab(String tabId) {
    try {
      return _tabs.firstWhere((tab) => tab.id == tabId);
    } catch (e) {
      return null;
    }
  }

  /// 清空所有标签页
  void clearAllTabs() {
    _tabs.clear();
    _currentTabIndex = -1;
    debugPrint('📑 清空所有标签页');
    notifyListeners();
  }

  /// 重新排序标签页
  void reorderTabs(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    
    final tab = _tabs.removeAt(oldIndex);
    _tabs.insert(newIndex, tab);
    
    // 更新当前标签页索引
    if (_currentTabIndex == oldIndex) {
      _currentTabIndex = newIndex;
    } else if (_currentTabIndex > oldIndex && _currentTabIndex <= newIndex) {
      _currentTabIndex--;
    } else if (_currentTabIndex < oldIndex && _currentTabIndex >= newIndex) {
      _currentTabIndex++;
    }
    
    debugPrint('📑 重新排序标签页: $oldIndex -> $newIndex');
    notifyListeners();
  }

  /// 复制标签页
  TabModel duplicateTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      final originalTab = _tabs[index];
      final newTab = TabModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        title: '${originalTab.title} - 副本',
        url: originalTab.url,
      );
      
      _tabs.insert(index + 1, newTab);
      _currentTabIndex = index + 1;
      
      debugPrint('📑 复制标签页: ${originalTab.id} -> ${newTab.id}');
      notifyListeners();
      
      return newTab;
    }
    throw ArgumentError('Invalid tab index: $index');
  }

  /// 获取标签页数量
  int get tabCount => _tabs.length;

  /// 是否可以关闭标签页（至少保留一个）
  bool canCloseTab() => _tabs.length > 1;
}
