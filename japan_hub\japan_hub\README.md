# JapanHub - 日本内容聚合APP

一个专为日本内容爱好者设计的网站导航和内容聚合应用。

## 功能特性

### 🌐 网站导航
- **响应式网格布局** - 根据屏幕尺寸自动调整列数（手机4列，平板5列，桌面6列）
- **快速添加网站** - 支持URL输入和智能建议
- **长按删除** - 长按网站卡片可删除
- **手机版适配** - 自动转换为移动端友好的URL
- **状态指示器** - 实时显示网站在线状态

### 📱 用户界面
- **手机优先设计** - 针对1920*1080分辨率优化
- **现代化卡片设计** - 渐变背景、阴影效果、圆角边框
- **可展开侧边栏** - 右侧功能面板，支持展开/收起
- **用户信息区** - 显示用户头像、ID和VIP状态
- **Material Design 3** - 最新设计规范

### 🌐 WebView功能
- **浏览器风格界面** - 仿真浏览器设计，包含地址栏和导航按钮
- **智能地址栏** - 显示当前URL，HTTPS安全锁图标，一键复制
- **完整导航控制** - 前进、后退、刷新、回到首页
- **多种打开方式** - 内嵌浏览、代理模式、高级代理
- **错误处理优化** - 网站访问受限时显示友好错误页面
- **自动重试机制** - 支持重新尝试加载和外部浏览器打开
- **移动端优化** - 自动设置移动端用户代理
- **桌面模式切换** - 支持切换到桌面版网站
- **下拉刷新** - 支持下拉刷新页面
- **进度指示** - 实时显示页面加载进度

### 🔧 预设功能模块
- **导航模块** - 网站快速访问（已实现）
- **翻译模块** - AI翻译功能（待实现）
- **优化模块** - 网站加速和优化（待实现）

## 预设网站

应用预置了以下热门日本网站：
- **小説家になろう** - 日本最大Web小说平台
- **pixiv** - 最大插画分享平台
- **カクヨム** - KADOKAWA小说平台

## 技术栈

- **Flutter 3.32.7** - 跨平台UI框架
- **Dart 3.8.1** - 编程语言
- **flutter_inappwebview 6.1.5** - WebView组件
- **url_launcher 6.3.1** - URL启动器（Web平台备选）
- **Material Design 3** - UI设计规范
- **Puro** - Flutter版本管理工具

## 开发环境

### 环境要求
- Windows 11
- Puro (Flutter版本管理)
- Chrome浏览器（用于Web调试）
- Python（用于本地HTTP服务器）

### 快速开始

1. **代码分析**
   ```bash
   ./analyze.bat
   # 或
   puro flutter analyze
   ```

2. **运行应用**
   ```bash
   ./run.bat
   # 选择运行模式：
   # 1. Debug模式（支持热重载，较慢）
   # 2. 构建模式（生产环境，较快）
   ```

3. **仅构建Web版本**
   ```bash
   ./build.bat
   # 或
   puro flutter build web
   ```

### 手动运行

**Debug模式（开发）：**
```bash
puro flutter run -d chrome --web-port=8080
```

**生产模式（推荐）：**
```bash
# 1. 构建
puro flutter build web

# 2. 启动HTTP服务器
python -m http.server 8080 --directory build/web

# 3. 打开浏览器访问 http://localhost:8080
```

## 项目结构

```
lib/
├── main.dart          # 应用入口
├── home_page.dart     # 主页面（网站导航）
├── webview_page.dart  # WebView页面（网站浏览）
└── ...               # 其他功能模块（待添加）

scripts/
├── run.bat           # 运行脚本
├── build.bat         # 构建脚本
└── analyze.bat       # 代码分析脚本
```

## 开发说明

### 环境问题解决
- 如果遇到Flutter命令问题，请使用 `puro flutter` 而不是直接的 `flutter` 命令
- Chrome调试连接较慢是正常现象，建议使用构建模式进行测试

### Web平台限制说明
- **iframe限制**：由于浏览器安全策略，很多网站设置了X-Frame-Options头，禁止在iframe中显示
- **解决方案**：应用会自动检测Web平台，提供友好的替代界面，引导用户在新标签页打开网站
- **用户体验**：在Web版本中点击网站会显示专门的提示页面，而不是空白或错误页面

### 已完成功能
- ✅ **响应式网站导航网格** - 支持多种屏幕尺寸
- ✅ **WebView内嵌浏览** - 完整的网页浏览体验
- ✅ **现代化UI设计** - Material Design 3风格
- ✅ **移动端优化** - 针对1920*1080分辨率优化

### 下一步开发计划
1. ✅ ~~添加WebView功能实现网站内嵌浏览~~ (已完成)
2. 实现AI翻译功能模块
3. 添加网站数据持久化存储
4. 添加网站分类和搜索功能
5. 实现用户账户系统
6. 添加网站收藏和历史记录

## 许可证

本项目为私人开发项目。
