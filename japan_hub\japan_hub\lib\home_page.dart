import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'webview_page.dart';
import 'cache_manager.dart';
import 'proxy_manager.dart';
import 'google_translate_service.dart';

// 后台预加载函数，在独立线程中执行
Future<Map<String, String>> _backgroundPreloadIcons(
  List<Map<String, dynamic>> websites,
) async {
  final iconCache = <String, String>{};

  for (final website in websites) {
    try {
      final iconUrl = website['icon'] ?? '';
      final websiteName = website['name'];

      if (iconUrl.isNotEmpty) {
        // 在后台线程中下载图标
        final response = await http
            .get(
              Uri.parse(iconUrl),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
            )
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          final base64Data = base64Encode(response.bodyBytes);
          final cacheKey = '${iconUrl}_$websiteName';
          iconCache[cacheKey] = base64Data;
        }
      }
    } catch (e) {
      // 单个图标加载失败，继续下一个
      continue;
    }
  }

  return iconCache;
}

// 全局图标缓存，防止页面跳转时清理
class _GlobalIconCache {
  static final Map<String, String> _memoryCache = {};
  static final Map<String, String> _failureCache = {}; // 记录失败的图标
  static final Map<String, Future<String?>> _futureCache = {};

  static Map<String, String> get memoryCache => _memoryCache;
  static Map<String, String> get failureCache => _failureCache;
  static Map<String, Future<String?>> get futureCache => _futureCache;

  // 清理过期的Future缓存
  static void cleanupFutureCache() {
    // 移除已完成的Future，避免内存泄漏
    final keysToRemove = <String>[];
    for (final entry in _futureCache.entries) {
      // 检查Future是否已完成（通过try-catch方式）
      try {
        entry.value
            .then((_) {
              keysToRemove.add(entry.key);
            })
            .catchError((_) {
              keysToRemove.add(entry.key);
            });
      } catch (e) {
        // Future已完成
        keysToRemove.add(entry.key);
      }
    }
    for (final key in keysToRemove) {
      _futureCache.remove(key);
    }
  }

  // 记录图标加载失败
  static void markIconFailed(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    _failureCache[cacheKey] = 'failed';
  }

  // 检查图标是否已知失败（包括开屏阶段的失败记录）
  static Future<bool> isIconFailed(String iconUrl, String websiteName) async {
    final cacheKey = '${iconUrl}_$websiteName';

    // 首先检查内存中的失败记录
    if (_failureCache.containsKey(cacheKey)) {
      return true;
    }

    // 检查开屏阶段保存的失败记录
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFailedInSplash = prefs.getBool('icon_failed_$cacheKey') ?? false;
      if (isFailedInSplash) {
        // 将开屏阶段的失败记录同步到内存缓存
        _failureCache[cacheKey] = 'failed';
        return true;
      }
    } catch (e) {
      // 读取失败，不影响正常流程
    }

    return false;
  }

  // 同步版本的检查方法（用于同步调用）
  static bool isIconFailedSync(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    return _failureCache.containsKey(cacheKey);
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with WidgetsBindingObserver {
  int _currentPageIndex = 0;
  final CacheManager _cacheManager = CacheManager();
  final ProxyManager _proxyManager = ProxyManager();
  final GoogleTranslateService _translateService = GoogleTranslateService();

  // User info
  final String _userName = 'JapanHub用户';
  final String _userId = 'JH001';
  final bool _isVIP = true;

  // 翻译和网络优化状态
  bool isNetworkOptimized = false;
  bool isAITranslateEnabled = false;
  bool keepOriginalText = true;
  bool alwaysTranslateThisSite = false;
  bool alwaysTranslateJapanese = false;
  String selectedTranslationAPI = 'deepseek';
  bool isAIAPIEnabled = false;

  // 地址栏控制器
  late TextEditingController _addressController;
  late FocusNode _addressFocusNode;

  // NacgAI搜索控制器
  late TextEditingController _nacgAISearchController;

  // 删除了工具页相关的状态变量

  // Drag and drop states
  bool _isDragMode = false;
  int? _draggedIndex;

  // 图标内存缓存，避免重复加载
  final Map<String, String> _iconMemoryCache = {};
  final Map<String, Future<String?>> _iconFutureCache = {};

  // 预加载控制
  bool _isPreloadingInBackground = false;

  // Website data
  final List<Map<String, dynamic>> _websites = [
    {
      'name': '小説家になろう',
      'url': 'https://syosetu.com/',
      'description': '日本最大的网络小说平台',
      'category': 'novel',
      'icon': 'https://syosetu.com/favicon.ico',
    },
    {
      'name': 'カクヨム',
      'url': 'https://kakuyomu.jp/',
      'description': 'KADOKAWA旗下小说平台',
      'category': 'novel',
      'icon': 'https://kakuyomu.jp/favicon.ico',
    },
    {
      'name': 'pixiv',
      'url': 'https://www.pixiv.net/',
      'description': '插画艺术社区',
      'category': 'art',
      'icon': 'https://www.pixiv.net/favicon.ico',
    },
    {
      'name': 'ニコニコ動画',
      'url': 'https://www.nicovideo.jp/',
      'description': '弹幕视频网站',
      'category': 'video',
      'icon': 'https://www.nicovideo.jp/favicon.ico',
    },
    {
      'name': 'Yahoo! JAPAN',
      'url': 'https://www.yahoo.co.jp/',
      'description': '日本雅虎门户',
      'category': 'portal',
      'icon': 'https://s.yimg.jp/images/favicon.ico',
    },
    {
      'name': '楽天',
      'url': 'https://www.rakuten.co.jp/',
      'description': '日本乐天购物',
      'category': 'shopping',
      'icon': 'https://www.rakuten.co.jp/favicon.ico',
    },
    {
      'name': '朝日新闻',
      'url': 'https://www.asahi.com/',
      'description': '朝日新闻官方网站 - 测试翻译功能',
      'category': 'news',
      'icon': 'https://www.asahi.com/favicon.ico',
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // 初始化地址栏控制器
    _addressController = TextEditingController();
    _addressFocusNode = FocusNode();

    // 初始化NacgAI搜索控制器
    _nacgAISearchController = TextEditingController();

    // 添加应用生命周期观察者
    WidgetsBinding.instance.addObserver(this);

    // 加载设置
    _loadSettings();

    // 加载开屏阶段的图标失败记录
    _loadSplashFailureStates();

    // 延迟启动阶段2预加载
    Future.delayed(const Duration(seconds: 2), () {
      _startStage2Preload();
    });

    // 预加载图标到内存缓存
    _preloadIconsToMemory();
  }

  // 加载开屏阶段的图标失败记录
  Future<void> _loadSplashFailureStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith('icon_failed_')) {
          final isFailedInSplash = prefs.getBool(key) ?? false;
          if (isFailedInSplash) {
            // 提取cacheKey并同步到全局失败缓存
            final cacheKey = key.replaceFirst('icon_failed_', '');
            _GlobalIconCache.failureCache[cacheKey] = 'failed';
          }
        }
      }
      debugPrint('已加载开屏阶段的图标失败记录: ${_GlobalIconCache.failureCache.length} 个');
    } catch (e) {
      debugPrint('加载开屏失败记录失败: $e');
    }
  }

  @override
  void dispose() {
    // 释放地址栏控制器
    _addressController.dispose();
    _addressFocusNode.dispose();

    // 释放NacgAI搜索控制器
    _nacgAISearchController.dispose();

    // 移除应用生命周期观察者
    WidgetsBinding.instance.removeObserver(this);

    // 页面销毁时停止后台预加载
    _stopBackgroundPreloading();
    super.dispose();
  }

  // 应用生命周期状态变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // 当应用从后台回到前台时，重新加载设置以同步状态
    if (state == AppLifecycleState.resumed) {
      _loadSettings();
    }
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    // 模拟刷新延迟
    await Future.delayed(const Duration(milliseconds: 1000));

    // 重新加载设置
    await _loadSettings();

    // 可以在这里添加其他刷新逻辑，比如：
    // - 刷新网站列表
    // - 更新热门作品列表
    // - 检查应用更新等
  }

  Future<void> _initializeServices() async {
    await _proxyManager.loadSavedProxy();
    await _translateService.loadCacheFromStorage();
    _proxyManager.startHealthCheck();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(child: _buildBody()),
    );
  }

  Widget _buildBody() {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          return _buildCurrentPage();
        } else {
          return Row(
            children: [
              _buildSideNavigation(),
              Expanded(child: _buildCurrentPage()),
            ],
          );
        }
      },
    );
  }

  Widget _buildCurrentPage() {
    // 只保留首页
    return _buildMainContent();
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        // 浏览器地址栏区域
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: _buildAddressBar(),
        ),

        // 主要内容区域
        Expanded(
          child: RefreshIndicator(
            onRefresh: _onRefresh,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Container(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // 标题区域
                    _buildTitleSection(),

                    const SizedBox(height: 40),

                    // ACGN网站快捷跳转
                    _buildACGNWebsites(),

                    const SizedBox(height: 32),

                    // NacgAI搜索模组
                    _buildNacgAIModule(),
                  ],
                ),
              ),
            ),
          ),
        ),

        // 底部浏览器工具栏
        _buildBrowserToolbar(),
      ],
    );
  }

  // 构建地址栏
  Widget _buildAddressBar() {
    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.grey[300]!, width: 1),
      ),
      child: Row(
        children: [
          const SizedBox(width: 12),

          // 安全锁图标
          Icon(Icons.lock, size: 16, color: Colors.grey[600]),

          const SizedBox(width: 8),

          // 地址栏输入框
          Expanded(
            child: GestureDetector(
              onTap: () {
                // 确保每次点击都能正确聚焦
                if (!_addressFocusNode.hasFocus) {
                  Future.delayed(const Duration(milliseconds: 50), () {
                    _addressFocusNode.requestFocus();
                  });
                }
              },
              child: Container(
                height: 40, // 固定高度
                alignment: Alignment.center, // 容器内容居中
                child: TextField(
                  controller: _addressController,
                  focusNode: _addressFocusNode,
                  decoration: const InputDecoration(
                    hintText: '搜索或输入网址',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero, // 移除内边距
                    isDense: true, // 紧凑模式
                  ),
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.left,
                  textAlignVertical: TextAlignVertical.center, // 垂直居中
                  cursorColor: Colors.grey.shade400, // 浅色光标
                  cursorWidth: 1.5, // 更细的光标
                  cursorHeight: 16, // 调整光标高度
                  onSubmitted: (url) {
                    _navigateToWebView(url);
                  },
                ),
              ),
            ),
          ),

          const SizedBox(width: 8),

          // 网络优化按钮（与WebViewPage一致的设计）
          GestureDetector(
            onTap: _toggleNetworkOptimize,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.vpn_key,
                      size: 18,
                      color: isNetworkOptimized ? Colors.green : Colors.blue,
                    ),
                  ),
                  if (isNetworkOptimized)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 6,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 6),

          // AI翻译按钮（与WebViewPage一致的设计）
          GestureDetector(
            onTap: _toggleAITranslate,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.translate,
                      size: 18,
                      color: isAITranslateEnabled
                          ? Colors.blue
                          : Colors.grey[600],
                    ),
                  ),
                  if (isAITranslateEnabled)
                    Positioned(
                      right: 0,
                      top: 0,
                      child: Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          size: 6,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(width: 6),

          // 翻译设置按钮（与WebViewPage一致的设计）
          GestureDetector(
            onTap: _openTranslateSettings,
            child: SizedBox(
              width: 24,
              height: 24,
              child: Center(
                child: Icon(Icons.settings, size: 18, color: Colors.grey[600]),
              ),
            ),
          ),

          const SizedBox(width: 12),
        ],
      ),
    );
  }

  // 构建标题区域
  Widget _buildTitleSection() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // N字图标
            Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(right: 12),
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'N',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
            // 主标题
            const Text(
              'NACG翻译君',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.blue, // 蓝色突出主标题
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Padding(
          padding: const EdgeInsets.only(left: 80), // 向右移动更多距离
          child: Text(
            '- 世界的二（不起）次元',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ), // 更小的副标题，加"-"号
          ),
        ),
      ],
    );
  }

  // 构建ACGN网站快捷跳转
  Widget _buildACGNWebsites() {
    final acgnSites = [
      {
        'name': '成为小说家吧',
        'url': 'https://syosetu.com/',
        'icon': Icons.book,
        'color': Colors.orange,
      },
      {
        'name': 'Pixiv',
        'url': 'https://www.pixiv.net/',
        'icon': Icons.palette,
        'color': Colors.blue,
      },
      {
        'name': 'NicoNico',
        'url': 'https://www.nicovideo.jp/',
        'icon': Icons.play_circle,
        'color': Colors.red,
      },
      {
        'name': 'DMM',
        'url': 'https://www.dmm.com/',
        'icon': Icons.shopping_cart,
        'color': Colors.purple,
      },
      {
        'name': 'DLsite',
        'url': 'https://www.dlsite.com/',
        'icon': Icons.download,
        'color': Colors.green,
      },
      {
        'name': 'Booth',
        'url': 'https://booth.pm/',
        'icon': Icons.store,
        'color': Colors.teal,
      },
      {
        'name': 'MyAnimeList',
        'url': 'https://myanimelist.net/',
        'icon': Icons.list,
        'color': Colors.indigo,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.0,
      ),
      itemCount: acgnSites.length,
      itemBuilder: (context, index) {
        final site = acgnSites[index];
        return _buildACGNSiteCard(site);
      },
    );
  }

  // 构建ACGN网站卡片
  Widget _buildACGNSiteCard(Map<String, dynamic> site) {
    return GestureDetector(
      onTap: () {
        _navigateToWebView(site['url']);
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: (site['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(site['icon'], size: 24, color: site['color']),
          ),
          const SizedBox(height: 8),
          Text(
            site['name'],
            style: const TextStyle(fontSize: 10, fontWeight: FontWeight.w500),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // 构建底部浏览器工具栏
  Widget _buildBrowserToolbar() {
    return Container(
      height: 42,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 后退按钮
          _buildToolbarButton(
            icon: Icons.arrow_back,
            enabled: false, // 首页无法后退
            onTap: () {
              // 后退功能 - 首页无法后退
            },
          ),

          // 前进按钮
          _buildToolbarButton(
            icon: Icons.arrow_forward,
            enabled: false, // 首页无法前进
            onTap: () {
              // 前进功能 - 首页无法前进
            },
          ),

          // 搜索按钮
          _buildToolbarButton(
            icon: Icons.search,
            onTap: () {
              // 聚焦到地址栏，和选中地址栏一样的操作
              Future.delayed(const Duration(milliseconds: 100), () {
                _addressFocusNode.requestFocus();
              });
            },
          ),

          // 浏览器切页按钮
          _buildToolbarButton(
            icon: Icons.tab,
            onTap: () {
              // 显示页签切换器
              _showTabManager();
            },
          ),

          // 更多按钮
          _buildToolbarButton(
            icon: Icons.more_horiz,
            onTap: () {
              // 更多功能 - 暂无实现
            },
          ),
        ],
      ),
    );
  }

  // 构建工具栏按钮
  Widget _buildToolbarButton({
    required IconData icon,
    required VoidCallback onTap,
    bool enabled = true,
  }) {
    return GestureDetector(
      onTap: enabled ? onTap : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Icon(
          icon,
          size: 24,
          color: enabled ? Colors.grey[600] : Colors.grey[300],
        ),
      ),
    );
  }

  // 导航到WebView页面
  void _navigateToWebView(String url) async {
    String finalUrl = url.trim();

    // 改进URL处理逻辑，智能添加www前缀
    if (!finalUrl.startsWith('http://') &&
        !finalUrl.startsWith('https://') &&
        !finalUrl.startsWith('ftp://') &&
        !finalUrl.startsWith('file://')) {
      if (finalUrl.contains('.') && !finalUrl.contains(' ')) {
        // 看起来像域名，智能添加https前缀和www前缀
        if (!finalUrl.startsWith('www.') && _shouldAddWww(finalUrl)) {
          finalUrl = 'https://www.$finalUrl';
          debugPrint('🌐 智能添加www前缀: $finalUrl');
        } else {
          finalUrl = 'https://$finalUrl';
        }
      } else {
        // 看起来像搜索词，使用Google搜索
        finalUrl =
            'https://www.google.com/search?q=${Uri.encodeComponent(finalUrl)}';
      }
    }

    debugPrint('🌐 从首页导航到: $finalUrl');

    // 导航到WebView页面，并在返回时重新加载设置
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(initialUrl: finalUrl),
      ),
    );

    // 从WebView页面返回时，重新加载设置以同步状态
    _loadSettings();
  }

  // 判断域名是否需要添加www前缀
  bool _shouldAddWww(String domain) {
    // 常见的需要www前缀的域名列表
    final wwwDomains = [
      'baidu.com',
      'google.com',
      'facebook.com',
      'twitter.com',
      'instagram.com',
      'linkedin.com',
      'pinterest.com',
      'reddit.com',
      'tumblr.com',
      'wordpress.com',
      'blogger.com',
      'medium.com',
      'github.com',
      'stackoverflow.com',
      'wikipedia.org',
      'amazon.com',
      'ebay.com',
      'alibaba.com',
      'taobao.com',
      'tmall.com',
      'jd.com',
      'qq.com',
      'weibo.com',
      'sina.com.cn',
      'sohu.com',
      'netease.com',
      '163.com',
      'yahoo.com',
      'bing.com',
      'duckduckgo.com',
      'yandex.com',
      'apple.com',
      'microsoft.com',
      'adobe.com',
      'oracle.com',
      'ibm.com',
      'salesforce.com',
      'dropbox.com',
      'spotify.com',
      'netflix.com',
      'hulu.com',
      'youtube.com',
      'vimeo.com',
      'twitch.tv',
      'discord.com',
      'slack.com',
      'zoom.us',
      'skype.com',
      'whatsapp.com',
      'telegram.org',
      'wechat.com',
      'line.me',
      'viber.com',
    ];

    // 检查是否在需要www的域名列表中
    return wwwDomains.contains(domain.toLowerCase());
  }

  Widget _buildWebsiteCard(Map<String, dynamic> website, int index) {
    return LongPressDraggable<int>(
      data: index,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 120,
          height: 120,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: Column(
            children: [
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, width: 0.5),
        ),
        child: Column(
          children: [
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: Text(
                  website['name'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[400],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
      onDragStarted: () {
        setState(() {
          _isDragMode = true;
          _draggedIndex = index;
        });
      },
      onDragEnd: (details) {
        setState(() {
          _isDragMode = false;
          _draggedIndex = null;
        });
      },
      child: InkWell(
        onTap: _isDragMode ? null : () => _openWebsite(website),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!, width: 0.5),
          ),
          child: Column(
            children: [
              // 图标区域 - 靠上对齐
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              // 文字区域，支持2行
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(children: [_buildNavItem(0, Icons.home, '首页')]),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentPageIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () => setState(() => _currentPageIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.blue : Colors.grey,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(children: [_buildSideNavItem(0, Icons.home, '首页')]),
    );
  }

  Widget _buildSideNavItem(int index, IconData icon, String label) {
    final isSelected = _currentPageIndex == index;
    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.blue : Colors.grey),
      title: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.blue : Colors.black87,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onTap: () => setState(() => _currentPageIndex = index),
    );
  }

  void _openWebsite(Map<String, dynamic> website) {
    // 用户点击链接时，停止后台预加载以节省资源
    _stopBackgroundPreloading();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            WebViewPage(initialUrl: website['url'], title: website['name']),
      ),
    );
  }

  void _showUserMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue[100],
                child: Icon(Icons.person, color: Colors.blue[700]),
              ),
              title: Row(
                children: [
                  Text(_userName),
                  if (_isVIP) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'VIP',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              subtitle: Text('ID: $_userId'),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('设置'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('退出'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  // Build add website card
  Widget _buildAddWebsiteCard() {
    return InkWell(
      onTap: _showAddWebsiteDialog,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[200]!, width: 0.5),
        ),
        child: Column(
          children: [
            // 图标区域 - 靠上对齐
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.add, size: 28, color: Colors.green[700]),
              ),
            ),
            const SizedBox(height: 12),
            // 文字区域
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: const Text(
                  '添加网站',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build website icon with caching and failure handling
  Widget _buildWebsiteIcon(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';

    // 检查是否已知加载失败（开屏阶段记录的失败状态）
    if (_GlobalIconCache.isIconFailedSync(iconUrl, websiteName)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.language, color: Colors.grey[600], size: 24),
      );
    }

    // 首先检查全局内存缓存
    if (_GlobalIconCache.memoryCache.containsKey(cacheKey)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: MemoryImage(
              const Base64Decoder().convert(
                _GlobalIconCache.memoryCache[cacheKey]!,
              ),
            ),
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    // 如果全局缓存中没有，检查是否正在加载
    if (!_GlobalIconCache.futureCache.containsKey(cacheKey)) {
      _GlobalIconCache.futureCache[cacheKey] = _loadCachedIcon(
        iconUrl,
        websiteName,
      );
    }

    return FutureBuilder<String?>(
      key: ValueKey(cacheKey),
      future: _GlobalIconCache.futureCache[cacheKey],
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData &&
            snapshot.data != null) {
          // 保存到全局内存缓存（持久化，不会因页面跳转而清除）
          _GlobalIconCache.memoryCache[cacheKey] = snapshot.data!;

          // Show cached icon
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: MemoryImage(
                  const Base64Decoder().convert(snapshot.data!),
                ),
                fit: BoxFit.cover,
              ),
            ),
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          // Show loading placeholder
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                ),
              ),
            ),
          );
        } else {
          // 加载失败，记录到全局失败状态并显示默认图标
          _GlobalIconCache.markIconFailed(iconUrl, websiteName);

          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.language, color: Colors.grey[600], size: 24),
          );
        }
      },
    );
  }

  // 预加载图标到内存缓存（后台线程执行）
  Future<void> _preloadIconsToMemory() async {
    if (_isPreloadingInBackground) return; // 避免重复预加载

    _isPreloadingInBackground = true;

    try {
      // 使用compute在后台线程执行，完全不阻塞UI
      final iconCache = await compute(_backgroundPreloadIcons, _websites);

      // 检查页面是否还存在，避免在页面跳转后更新状态
      if (mounted && _isPreloadingInBackground) {
        setState(() {
          _iconMemoryCache.addAll(iconCache);
        });
        debugPrint('后台预加载完成，缓存了 ${iconCache.length} 个图标');
      }
    } catch (e) {
      // 后台预加载失败，不影响正常使用
      debugPrint('后台图标预加载失败: $e');
    } finally {
      _isPreloadingInBackground = false;
    }
  }

  // 停止后台预加载（当用户离开页面时调用）
  void _stopBackgroundPreloading() {
    _isPreloadingInBackground = false;
  }

  // Load cached icon or fetch from network
  Future<String?> _loadCachedIcon(String iconUrl, String websiteName) async {
    if (iconUrl.isEmpty) return null;

    try {
      // Check cache first
      final cachedIcon = await _cacheManager.getCachedFavicon(iconUrl);
      if (cachedIcon != null) {
        return cachedIcon;
      }

      // Try multiple icon URLs
      final iconUrls = [
        iconUrl,
        iconUrl.replaceAll('/favicon.ico', '/apple-touch-icon.png'),
        iconUrl.replaceAll('/favicon.ico', '/icon.png'),
        // Special cases for problematic websites
        if (iconUrl.contains('yahoo.co.jp') || iconUrl.contains('yimg.jp')) ...[
          'https://www.yahoo.co.jp/favicon.ico',
          'https://s.yimg.jp/images/favicon.ico',
          'https://s.yimg.jp/images/top/sp2/cmn/logo-ns-131205.png',
          'https://yahoo.co.jp/favicon.ico',
        ],
        if (iconUrl.contains('syosetu.com')) ...[
          'https://syosetu.com/favicon.ico',
          'https://static.syosetu.com/favicon.ico',
          'https://syosetu.com/apple-touch-icon.png',
        ],
        if (iconUrl.contains('pixiv.net')) ...[
          'https://www.pixiv.net/favicon.ico',
          'https://s.pximg.net/common/images/favicon.ico',
          'https://pixiv.net/favicon.ico',
        ],
        if (iconUrl.contains('nicovideo.jp')) ...[
          'https://www.nicovideo.jp/favicon.ico',
          'https://secure-dcdn.cdn.nimg.jp/nicoaccount/usericon/s/252/2521.jpg',
          'https://nicovideo.jp/favicon.ico',
        ],
        if (iconUrl.contains('rakuten.co.jp')) ...[
          'https://www.rakuten.co.jp/favicon.ico',
          'https://r.r10s.jp/com/img/thumb/favicon.ico',
          'https://rakuten.co.jp/favicon.ico',
        ],
      ];

      for (final url in iconUrls) {
        try {
          // Fetch from network and cache
          final response = await http
              .get(
                Uri.parse(url),
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                  'Accept':
                      'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                  'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
                  'Accept-Encoding': 'gzip, deflate, br',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache',
                },
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
            final base64Icon = base64Encode(response.bodyBytes);
            await _cacheManager.cacheFavicon(iconUrl, base64Icon);
            return base64Icon;
          }
        } catch (e) {
          // Try next URL
          continue;
        }
      }
    } catch (e) {
      // Icon loading failed
    }

    return null;
  }

  // Show add website dialog
  void _showAddWebsiteDialog() {
    final TextEditingController inputController = TextEditingController();
    List<Map<String, String>> suggestions = [];
    String detectedName = '';
    String detectedUrl = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('添加网站'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: inputController,
                decoration: const InputDecoration(
                  labelText: '网站名称或网址',
                  hintText: '输入网站名称（如：GitHub）或网址（如：https://github.com）',
                ),
                onChanged: (value) {
                  setState(() {
                    if (_isUrl(value)) {
                      // 输入的是网址，标准化URL
                      detectedUrl = _normalizeUrl(value);
                      detectedName = _extractNameFromUrl(detectedUrl);
                      suggestions = [];
                    } else {
                      // 输入的是网站名称
                      suggestions = _getWebsiteSuggestions(value);
                      detectedName = value;
                      detectedUrl = '';
                    }
                  });
                },
              ),
              if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '检测到:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text('名称: $detectedName'),
                      Text('网址: $detectedUrl'),
                    ],
                  ),
                ),
              ],
              if (suggestions.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  '建议网站:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    itemCount: suggestions.length,
                    itemBuilder: (context, index) {
                      final suggestion = suggestions[index];
                      return ListTile(
                        dense: true,
                        title: Text(suggestion['name']!),
                        subtitle: Text(suggestion['url']!),
                        onTap: () {
                          inputController.text = suggestion['name']!;
                          setState(() {
                            detectedName = suggestion['name']!;
                            detectedUrl = suggestion['url']!;
                            suggestions = [];
                          });
                        },
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) {
                  _addWebsite(detectedName, detectedUrl);
                  Navigator.of(context).pop();
                } else if (inputController.text.isNotEmpty) {
                  // 如果只输入了名称，尝试从建议中找到匹配的网址
                  final suggestion = _getWebsiteSuggestions(
                    inputController.text,
                  ).firstOrNull;
                  if (suggestion != null) {
                    _addWebsite(suggestion['name']!, suggestion['url']!);
                    Navigator.of(context).pop();
                  }
                }
              },
              child: const Text('添加'),
            ),
          ],
        ),
      ),
    );
  }

  // Get website suggestions based on input
  List<Map<String, String>> _getWebsiteSuggestions(String input) {
    if (input.length < 2) return [];

    final websiteDatabase = [
      {'name': 'GitHub', 'url': 'https://github.com'},
      {'name': 'Stack Overflow', 'url': 'https://stackoverflow.com'},
      {'name': 'Reddit', 'url': 'https://reddit.com'},
      {'name': 'Twitter', 'url': 'https://twitter.com'},
      {'name': 'YouTube', 'url': 'https://youtube.com'},
      {'name': 'Google', 'url': 'https://google.com'},
      {'name': 'Wikipedia', 'url': 'https://wikipedia.org'},
      {'name': 'Amazon', 'url': 'https://amazon.com'},
      {'name': 'Netflix', 'url': 'https://netflix.com'},
      {'name': 'Facebook', 'url': 'https://facebook.com'},
      {'name': 'Instagram', 'url': 'https://instagram.com'},
      {'name': 'LinkedIn', 'url': 'https://linkedin.com'},
      {'name': 'Discord', 'url': 'https://discord.com'},
      {'name': 'Twitch', 'url': 'https://twitch.tv'},
      {'name': 'TikTok', 'url': 'https://tiktok.com'},
      // Japanese websites
      {'name': 'Yahoo Japan', 'url': 'https://yahoo.co.jp'},
      {'name': 'Rakuten', 'url': 'https://rakuten.co.jp'},
      {'name': 'Amazon Japan', 'url': 'https://amazon.co.jp'},
      {'name': 'Mercari', 'url': 'https://mercari.com'},
      {'name': 'Cookpad', 'url': 'https://cookpad.com'},
      {'name': 'Hatena', 'url': 'https://hatena.ne.jp'},
      {'name': 'Ameba', 'url': 'https://ameblo.jp'},
      {'name': 'FC2', 'url': 'https://fc2.com'},
      {'name': 'Goo', 'url': 'https://goo.ne.jp'},
      {'name': 'Livedoor', 'url': 'https://livedoor.com'},
    ];

    return websiteDatabase
        .where(
          (site) => site['name']!.toLowerCase().contains(input.toLowerCase()),
        )
        .take(5)
        .toList();
  }

  // Add website to the list
  void _addWebsite(String name, String url) {
    setState(() {
      _websites.add({
        'name': name,
        'url': url,
        'description': '用户添加的网站',
        'category': 'custom',
        'icon': '$url/favicon.ico',
      });
    });

    // Save to local storage
    _saveWebsitesToStorage();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('已添加网站: $name')));
  }

  // Save websites to local storage
  Future<void> _saveWebsitesToStorage() async {
    // This would save to SharedPreferences in a real app
    // For now, just keep in memory
  }

  // Check if input is a URL
  bool _isUrl(String input) {
    // 明确的协议前缀
    if (input.startsWith('http://') || input.startsWith('https://')) {
      return true;
    }

    // 包含域名格式的字符串
    if (input.contains('.') && input.length > 4) {
      // 检查是否包含常见的顶级域名
      final commonTlds = [
        '.com',
        '.org',
        '.net',
        '.jp',
        '.cn',
        '.co.jp',
        '.ne.jp',
      ];
      for (final tld in commonTlds) {
        if (input.toLowerCase().contains(tld)) {
          return true;
        }
      }

      // 检查是否是IP地址格式
      final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}');
      if (ipRegex.hasMatch(input)) {
        return true;
      }
    }

    return false;
  }

  // Normalize URL to ensure it has proper protocol
  String _normalizeUrl(String url) {
    String normalized = url.trim();

    // 如果已经有协议，直接返回
    if (normalized.startsWith('http://') || normalized.startsWith('https://')) {
      return normalized;
    }

    // 默认添加https://协议
    return 'https://$normalized';
  }

  // 阶段2: 导航页后续网站预加载
  Future<void> _startStage2Preload() async {
    if (_websites.length <= 12) return; // 没有后续网站

    try {
      final stage2Websites = _websites.skip(12).take(12).toList();

      for (final website in stage2Websites) {
        // 后台预加载，不阻塞UI
        _preloadWebsiteInBackground(website);
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // 启动阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    } catch (e) {
      // 阶段2预加载失败，继续阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    }
  }

  // 阶段3: 首屏内容深度预加载
  Future<void> _startStage3Preload() async {
    try {
      final stage3Websites = _websites.take(12).toList();

      for (final website in stage3Websites) {
        // 预加载首屏内容（基于1080×1980分辨率）
        _preloadFirstScreenContent(website['url']);
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      // 阶段3预加载失败，不影响使用
    }
  }

  // 后台预加载网站
  Future<void> _preloadWebsiteInBackground(Map<String, dynamic> website) async {
    try {
      // 预加载图标
      await _loadCachedIcon(website['icon'] ?? '', website['name']);

      // 预加载主页内容
      await _cacheManager.getCachedPageContent(website['url']);
    } catch (e) {
      // 后台预加载失败，不影响用户体验
    }
  }

  // 预加载首屏内容
  Future<void> _preloadFirstScreenContent(String url) async {
    try {
      // 获取页面内容
      final content = await _cacheManager.getCachedPageContent(url);
      if (content != null) {
        // 分析页面，提取首屏关键资源
        final resources = _extractFirstScreenResources(content, url);

        // 预加载关键资源
        for (final resource in resources.take(5)) {
          // 限制5个资源
          _preloadResource(resource);
        }
      }
    } catch (e) {
      // 首屏预加载失败，不影响使用
    }
  }

  // 提取首屏关键资源
  List<String> _extractFirstScreenResources(String html, String baseUrl) {
    final resources = <String>[];
    final uri = Uri.parse(baseUrl);

    // 简化实现：使用字符串匹配
    try {
      // 查找CSS文件
      final lines = html.split('\n');
      for (final line in lines) {
        if (line.contains('.css') && line.contains('href=')) {
          final start = line.indexOf('href="');
          if (start != -1) {
            final end = line.indexOf('"', start + 6);
            if (end != -1) {
              final href = line.substring(start + 6, end);
              if (href.contains('.css')) {
                final resourceUrl = _resolveUrl(href, uri);
                if (resourceUrl != null) resources.add(resourceUrl);
              }
            }
          }
        }

        // 查找图片文件
        if ((line.contains('.jpg') ||
                line.contains('.png') ||
                line.contains('.gif')) &&
            line.contains('src=')) {
          final start = line.indexOf('src="');
          if (start != -1) {
            final end = line.indexOf('"', start + 5);
            if (end != -1) {
              final src = line.substring(start + 5, end);
              if (src.contains('.jpg') ||
                  src.contains('.png') ||
                  src.contains('.gif')) {
                final resourceUrl = _resolveUrl(src, uri);
                if (resourceUrl != null && resources.length < 5) {
                  resources.add(resourceUrl);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      // 资源提取失败，返回空列表
    }

    return resources;
  }

  // 解析相对URL为绝对URL
  String? _resolveUrl(String href, Uri baseUri) {
    try {
      if (href.startsWith('http')) {
        return href;
      } else if (href.startsWith('//')) {
        return '${baseUri.scheme}:$href';
      } else if (href.startsWith('/')) {
        return '${baseUri.scheme}://${baseUri.host}$href';
      } else {
        return '${baseUri.scheme}://${baseUri.host}${baseUri.path}/$href';
      }
    } catch (e) {
      return null;
    }
  }

  // 预加载资源
  Future<void> _preloadResource(String url) async {
    try {
      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
            },
          )
          .timeout(const Duration(seconds: 3));

      // 简单缓存资源（实际项目中可以使用更复杂的缓存策略）
      if (response.statusCode == 200) {
        // 资源预加载成功
      }
    } catch (e) {
      // 资源预加载失败，不影响使用
    }
  }

  // Reorder websites when dragging
  void _reorderWebsites(int oldIndex, int newIndex) {
    setState(() {
      final website = _websites.removeAt(oldIndex);
      _websites.insert(newIndex, website);
    });
  }

  // Build remove area at bottom
  Widget _buildRemoveArea() {
    return DragTarget<int>(
      onAcceptWithDetails: (details) {
        final draggedIndex = details.data;
        final website = _websites[draggedIndex];

        // 所有网站都可以删除
        _deleteWebsite(website);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 80,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isHovering ? Colors.red[400] : Colors.red[300],
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: isHovering ? 32 : 28,
              ),
              const SizedBox(width: 8),
              Text(
                '拖动到此处删除',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isHovering ? 18 : 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Check if website is a default website (cannot be deleted)
  bool _isDefaultWebsite(Map<String, dynamic> website) {
    final defaultWebsiteNames = [
      '小説家になろう',
      'カクヨム',
      'pixiv',
      'ニコニコ動画',
      'Yahoo! JAPAN',
      '楽天',
    ];

    return defaultWebsiteNames.contains(website['name']);
  }

  // Delete website from list
  void _deleteWebsite(Map<String, dynamic> website) {
    setState(() {
      _websites.removeWhere(
        (w) => w['name'] == website['name'] && w['url'] == website['url'],
      );
    });

    // Show confirmation message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已删除 "${website['name']}"'),
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Save to SharedPreferences in a real app
  }

  // 切换网络优化
  void _toggleNetworkOptimize() async {
    setState(() {
      isNetworkOptimized = !isNetworkOptimized;
    });
    await _saveSettings();
    debugPrint('网络优化已${isNetworkOptimized ? '开启' : '关闭'}');
  }

  // 切换AI翻译
  void _toggleAITranslate() {
    setState(() {
      isAITranslateEnabled = !isAITranslateEnabled;
    });
    _saveSettings();
    debugPrint('AI翻译已${isAITranslateEnabled ? '开启' : '关闭'}');
  }

  // 打开翻译设置（与WebViewPage完全一致）
  void _openTranslateSettings() {
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            width: 320,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 用户信息头部
                _buildUserHeader(),
                const SizedBox(height: 8),

                // 翻译设置标题
                const Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    '翻译设置',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ),
                const SizedBox(height: 8),

                // 翻译设置内容
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 翻译服务选择（点击弹出单独对话框）
                    GestureDetector(
                      onTap: () {
                        _showTranslationServiceDialog(setDialogState);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 16,
                        ),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _getApiIcon(selectedTranslationAPI),
                              color: _getApiIconColor(selectedTranslationAPI),
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getApiTitle(selectedTranslationAPI),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    _getApiSubtitle(selectedTranslationAPI),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 翻译选项组
                    Column(
                      children: [
                        // 保留原文开关
                        SwitchListTile(
                          title: const Text(
                            '保留原文',
                            style: TextStyle(fontSize: 14),
                          ),
                          value: keepOriginalText,
                          dense: true,
                          onChanged: (value) {
                            setDialogState(() {
                              keepOriginalText = value;
                            });
                            setState(() {
                              keepOriginalText = value;
                            });
                            _saveSettings();
                          },
                        ),

                        // 总是翻译该网站
                        SwitchListTile(
                          title: const Text(
                            '总是翻译该网站',
                            style: TextStyle(fontSize: 14),
                          ),
                          value: alwaysTranslateThisSite,
                          dense: true,
                          onChanged: (value) async {
                            setDialogState(() {
                              alwaysTranslateThisSite = value;
                            });
                            setState(() {
                              alwaysTranslateThisSite = value;
                            });
                            _saveSettings();
                          },
                        ),

                        // 总是翻译日语页面
                        SwitchListTile(
                          title: const Text(
                            '总是翻译日语页面',
                            style: TextStyle(fontSize: 14),
                          ),
                          value: alwaysTranslateJapanese,
                          dense: true,
                          onChanged: (value) {
                            setDialogState(() {
                              alwaysTranslateJapanese = value;
                            });
                            setState(() {
                              alwaysTranslateJapanese = value;
                            });
                            _saveSettings();
                          },
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 关闭按钮
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('关闭'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 保存设置（与WebViewPage一致）
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('network_optimized', isNetworkOptimized);
      await prefs.setBool('ai_translate_enabled', isAITranslateEnabled);
      await prefs.setString('selected_translation_api', selectedTranslationAPI);
      await prefs.setBool('keep_original_text', keepOriginalText);
      await prefs.setBool(
        'always_translate_this_site',
        alwaysTranslateThisSite,
      );
      await prefs.setBool('always_translate_japanese', alwaysTranslateJapanese);
    } catch (e) {
      debugPrint('保存设置失败: $e');
    }
  }

  // 加载设置（与WebViewPage一致）
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        isNetworkOptimized = prefs.getBool('network_optimized') ?? false;
        isAITranslateEnabled =
            prefs.getBool('ai_translate_enabled') ?? false; // 正确加载翻译开关状态
        selectedTranslationAPI =
            prefs.getString('selected_translation_api') ?? 'deepseek';
        isAIAPIEnabled = selectedTranslationAPI != 'none';
        keepOriginalText = prefs.getBool('keep_original_text') ?? true;
        alwaysTranslateThisSite =
            prefs.getBool('always_translate_this_site') ?? false;
        alwaysTranslateJapanese =
            prefs.getBool('always_translate_japanese') ?? false;
      });
    } catch (e) {
      debugPrint('加载设置失败: $e');
    }
  }

  // Extract website name from URL
  String _extractNameFromUrl(String url) {
    try {
      // Ensure URL has protocol
      String fullUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        fullUrl = 'https://$url';
      }

      final uri = Uri.parse(fullUrl);
      String host = uri.host;

      // Remove www. prefix
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      // Extract main domain name
      final parts = host.split('.');
      if (parts.isNotEmpty) {
        return parts[0].substring(0, 1).toUpperCase() + parts[0].substring(1);
      }

      return host;
    } catch (e) {
      return url;
    }
  }

  // 用户信息头部（与WebViewPage一致）
  Widget _buildUserHeader() {
    return Row(
      children: [
        // 用户头像
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: Colors.blue.shade100,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(Icons.person, size: 20, color: Colors.blue.shade600),
        ),
        const SizedBox(width: 8),

        // 用户信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _userName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '用户ID: $_userId',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),

        // VIP标识
        if (_isVIP)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.amber.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'VIP',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.amber.shade700,
              ),
            ),
          ),
      ],
    );
  }

  // 显示翻译服务选择对话框（与WebViewPage一致）
  void _showTranslationServiceDialog(StateSetter setDialogState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择翻译服务'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildApiCard(
              title: '仅词典翻译',
              subtitle: '本地词典，无需网络，基础翻译',
              value: 'none',
              icon: Icons.book,
              iconColor: Colors.grey.shade600,
              setDialogState: setDialogState,
            ),

            _buildApiCard(
              title: 'DeepSeek V3翻译',
              subtitle: '最新AI大模型，智能翻译，理解上下文',
              value: 'deepseek',
              icon: Icons.psychology,
              iconColor: Colors.purple.shade600,
              setDialogState: setDialogState,
            ),

            _buildApiCard(
              title: '腾讯翻译API',
              subtitle: '高质量机器翻译，专业可靠',
              value: 'tencent',
              icon: Icons.cloud,
              iconColor: Colors.blue.shade600,
              setDialogState: setDialogState,
            ),

            _buildApiCard(
              title: '百度翻译API',
              subtitle: '专业翻译服务，准确高效',
              value: 'baidu',
              icon: Icons.translate,
              iconColor: Colors.orange.shade600,
              setDialogState: setDialogState,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  // 构建卡片式API选择项（与WebViewPage一致）
  Widget _buildApiCard({
    required String title,
    required String subtitle,
    required String value,
    required IconData icon,
    required Color iconColor,
    required StateSetter setDialogState,
  }) {
    final isSelected = selectedTranslationAPI == value;

    return GestureDetector(
      onTap: () {
        setDialogState(() {
          selectedTranslationAPI = value;
          isAIAPIEnabled = value != 'none';
        });
        setState(() {
          selectedTranslationAPI = value;
          isAIAPIEnabled = value != 'none';
        });
        _saveSettings();

        // 关闭翻译服务选择对话框
        Navigator.pop(context);
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected ? Colors.blue.shade50 : null,
        ),
        child: Row(
          children: [
            Icon(icon, color: iconColor, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.blue.shade700 : null,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? Colors.blue.shade600
                          : Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(Icons.check_circle, color: Colors.blue.shade600, size: 20),
          ],
        ),
      ),
    );
  }

  // API图标获取方法
  IconData _getApiIcon(String api) {
    switch (api) {
      case 'none':
        return Icons.book;
      case 'deepseek':
        return Icons.psychology;
      case 'tencent':
        return Icons.cloud;
      case 'baidu':
        return Icons.translate;
      default:
        return Icons.translate;
    }
  }

  // API图标颜色获取方法
  Color _getApiIconColor(String api) {
    switch (api) {
      case 'none':
        return Colors.grey.shade600;
      case 'deepseek':
        return Colors.purple.shade600;
      case 'tencent':
        return Colors.blue.shade600;
      case 'baidu':
        return Colors.orange.shade600;
      default:
        return Colors.grey.shade600;
    }
  }

  // API标题获取方法
  String _getApiTitle(String api) {
    switch (api) {
      case 'none':
        return '仅词典翻译';
      case 'deepseek':
        return 'DeepSeek V3翻译';
      case 'tencent':
        return '腾讯翻译API';
      case 'baidu':
        return '百度翻译API';
      default:
        return '未知服务';
    }
  }

  // API副标题获取方法
  String _getApiSubtitle(String api) {
    switch (api) {
      case 'none':
        return '本地词典，无需网络，基础翻译';
      case 'deepseek':
        return '最新AI大模型，智能翻译，理解上下文';
      case 'tencent':
        return '高质量机器翻译，专业可靠';
      case 'baidu':
        return '专业翻译服务，准确高效';
      default:
        return '翻译服务';
    }
  }

  // 构建NacgAI搜索模组
  Widget _buildNacgAIModule() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // NacgAI标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'NacgAI',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '智能搜索',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // 搜索框和搜索按钮
        _buildNacgAISearchBar(),

        const SizedBox(height: 20),

        // 热门作品标签
        _buildPopularAnimeList(),
      ],
    );
  }

  // 构建NacgAI搜索栏
  Widget _buildNacgAISearchBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: Container(
              height: 44,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(22),
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  Icon(Icons.search, size: 20, color: Colors.grey[600]),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextField(
                      controller: _nacgAISearchController,
                      decoration: const InputDecoration(
                        hintText: '搜索轻小说、动漫作品...',
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        isDense: true,
                      ),
                      style: const TextStyle(fontSize: 14),
                      textAlignVertical: TextAlignVertical.center,
                      onSubmitted: (query) {
                        if (query.isNotEmpty) {
                          _searchAnimeInNacgAI(query);
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                ],
              ),
            ),
          ),

          const SizedBox(width: 12),

          // 搜索按钮
          Container(
            height: 44,
            width: 44,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(22),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  final query = _nacgAISearchController.text.trim();
                  if (query.isNotEmpty) {
                    _searchAnimeInNacgAI(query);
                  }
                },
                borderRadius: BorderRadius.circular(22),
                child: const Icon(Icons.search, color: Colors.white, size: 20),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建热门动漫作品列表
  Widget _buildPopularAnimeList() {
    final animeList = [
      '进击的巨人',
      '鬼灭之刃',
      '咒术回战',
      '间谍过家家',
      '链锯人',
      '我的英雄学院',
      '东京喰种',
      '死神',
      '火影忍者',
      '海贼王',
      '龙珠',
      '一拳超人',
      '约定的梦幻岛',
      '石纪元',
      '辉夜大小姐想让我告白',
      '五等分的新娘',
      '青春猪头少年不会梦到兔女郎学姐',
      'Re:从零开始的异世界生活',
      '关于我转生变成史莱姆这档事',
      '无职转生',
      '紫罗兰永恒花园',
      '你的名字',
      '天气之子',
      '千与千寻',
      '龙猫',
      '魔女宅急便',
      '幽灵公主',
      '风之谷',
      '攻壳机动队',
      '新世纪福音战士',
      '钢之炼金术师',
      '猎人',
      '银魂',
      '名侦探柯南',
      '多啦A梦',
      '樱桃小丸子',
      '蜡笔小新',
      '数码宝贝',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Text(
            '热门作品',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // 不规则排序的作品标签
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: animeList
              .map(
                (anime) => GestureDetector(
                  onTap: () => _searchAnimeInNacgAI(anime),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.blue.shade200, width: 1),
                    ),
                    child: Text(
                      anime,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),
              )
              .toList(),
        ),
      ],
    );
  }

  // 在NacgAI中搜索动漫作品
  void _searchAnimeInNacgAI(String animeName) {
    // 限定在轻小说机翻网和轻之国度搜索
    final searchQuery =
        '$animeName site:books.fishhawk.top OR site:lightnovel.cn';
    final searchUrl =
        'https://www.google.com/search?q=${Uri.encodeComponent(searchQuery)}';

    _navigateToWebView(searchUrl);
  }

  // 显示页签管理器
  void _showTabManager() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            // 标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '页签管理',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Row(
                    children: [
                      // 新建页签按钮
                      IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // 聚焦到地址栏来添加新页签
                          _addressFocusNode.requestFocus();
                        },
                        icon: const Icon(Icons.add),
                        tooltip: '新建页签',
                      ),
                      // 关闭按钮
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 页签列表
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  // 当前页签（首页）
                  ListTile(
                    leading: const Icon(Icons.home, color: Colors.blue),
                    title: const Text('首页'),
                    trailing: const Text(
                      '当前',
                      style: TextStyle(color: Colors.blue, fontSize: 12),
                    ),
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                  ),

                  // 新建页签
                  ListTile(
                    leading: const Icon(Icons.add, color: Colors.grey),
                    title: const Text('新建页签'),
                    onTap: () {
                      Navigator.of(context).pop();
                      _createNewBlankTab();
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 创建新的空白页签
  void _createNewBlankTab() {
    // 导航到新的空白WebView页面，地址栏为空并自动聚焦
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BlankWebViewPage()),
    ).then((_) {
      // 从新页签返回时，重新加载设置以同步状态
      _loadSettings();
    });
  }
}
