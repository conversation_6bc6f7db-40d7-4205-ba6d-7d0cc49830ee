import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'webview_page.dart';
import 'cache_manager.dart';
import 'proxy_manager.dart';
import 'google_translate_service.dart';
import 'tab_manager.dart'; // 多标签功能：导入标签页管理器

// 后台预加载函数，在独立线程中执行
Future<Map<String, String>> _backgroundPreloadIcons(
  List<Map<String, dynamic>> websites,
) async {
  final iconCache = <String, String>{};

  for (final website in websites) {
    try {
      final iconUrl = website['icon'] ?? '';
      final websiteName = website['name'];

      if (iconUrl.isNotEmpty) {
        // 在后台线程中下载图标
        final response = await http
            .get(
              Uri.parse(iconUrl),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
            )
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          final base64Data = base64Encode(response.bodyBytes);
          final cacheKey = '${iconUrl}_$websiteName';
          iconCache[cacheKey] = base64Data;
        }
      }
    } catch (e) {
      // 单个图标加载失败，继续下一个
      continue;
    }
  }

  return iconCache;
}

// 全局图标缓存，防止页面跳转时清理
class _GlobalIconCache {
  static final Map<String, String> _memoryCache = {};
  static final Map<String, String> _failureCache = {}; // 记录失败的图标
  static final Map<String, Future<String?>> _futureCache = {};

  static Map<String, String> get memoryCache => _memoryCache;
  static Map<String, String> get failureCache => _failureCache;
  static Map<String, Future<String?>> get futureCache => _futureCache;

  // 清理过期的Future缓存
  static void cleanupFutureCache() {
    // 移除已完成的Future，避免内存泄漏
    final keysToRemove = <String>[];
    for (final entry in _futureCache.entries) {
      // 检查Future是否已完成（通过try-catch方式）
      try {
        entry.value
            .then((_) {
              keysToRemove.add(entry.key);
            })
            .catchError((_) {
              keysToRemove.add(entry.key);
            });
      } catch (e) {
        // Future已完成
        keysToRemove.add(entry.key);
      }
    }
    for (final key in keysToRemove) {
      _futureCache.remove(key);
    }
  }

  // 记录图标加载失败
  static void markIconFailed(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    _failureCache[cacheKey] = 'failed';
  }

  // 检查图标是否已知失败（包括开屏阶段的失败记录）
  static Future<bool> isIconFailed(String iconUrl, String websiteName) async {
    final cacheKey = '${iconUrl}_$websiteName';

    // 首先检查内存中的失败记录
    if (_failureCache.containsKey(cacheKey)) {
      return true;
    }

    // 检查开屏阶段保存的失败记录
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFailedInSplash = prefs.getBool('icon_failed_$cacheKey') ?? false;
      if (isFailedInSplash) {
        // 将开屏阶段的失败记录同步到内存缓存
        _failureCache[cacheKey] = 'failed';
        return true;
      }
    } catch (e) {
      // 读取失败，不影响正常流程
    }

    return false;
  }

  // 同步版本的检查方法（用于同步调用）
  static bool isIconFailedSync(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    return _failureCache.containsKey(cacheKey);
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentPageIndex = 0;
  final CacheManager _cacheManager = CacheManager();
  final ProxyManager _proxyManager = ProxyManager();
  final GoogleTranslateService _translateService = GoogleTranslateService();

  // 页眉功能状态（与WebView完全相同）
  bool _isNetworkOptimized = false;
  bool _isAITranslateEnabled = false;
  String _selectedTranslationAPI = 'deepseek'; // 默认DeepSeekV3

  // 翻译设置的新状态变量
  bool _keepOriginalText = false;
  bool _alwaysTranslateSite = false;
  bool _preferJapanesePage = false;

  // 第3步：NacgAi搜索控制器
  final TextEditingController _nacgAiSearchController = TextEditingController();

  // 底栏功能状态
  bool _canGoBack = false;
  bool _canGoForward = false;
  int _tabCount = 1;

  // 1. 标签页信息列表
  List<Map<String, String>> _tabs = [
    {'title': 'Nacg翻译君', 'url': 'home', 'isHome': 'true'}, // 首屏标签页
  ];

  // 1. 地址栏控制器和状态
  final TextEditingController _addressController = TextEditingController();
  final FocusNode _addressFocusNode = FocusNode();
  bool _isAddressBarFocused = false;

  // User info
  final String _userName = 'JapanHub用户';
  final String _userId = 'JH001';
  final bool _isVIP = true;

  // 旧的状态变量已删除，使用新的页眉状态变量

  // Drag and drop states
  bool _isDragMode = false;
  int? _draggedIndex;

  // 图标内存缓存，避免重复加载
  final Map<String, String> _iconMemoryCache = {};
  final Map<String, Future<String?>> _iconFutureCache = {};

  // 预加载控制
  bool _isPreloadingInBackground = false;

  // Website data
  final List<Map<String, dynamic>> _websites = [
    {
      'name': '小説家になろう',
      'url': 'https://syosetu.com/',
      'description': '日本最大的网络小说平台',
      'category': 'novel',
      'icon': 'https://syosetu.com/favicon.ico',
    },
    {
      'name': 'カクヨム',
      'url': 'https://kakuyomu.jp/',
      'description': 'KADOKAWA旗下小说平台',
      'category': 'novel',
      'icon': 'https://kakuyomu.jp/favicon.ico',
    },
    {
      'name': 'pixiv',
      'url': 'https://www.pixiv.net/',
      'description': '插画艺术社区',
      'category': 'art',
      'icon': 'https://www.pixiv.net/favicon.ico',
    },
    {
      'name': 'ニコニコ動画',
      'url': 'https://www.nicovideo.jp/',
      'description': '弹幕视频网站',
      'category': 'video',
      'icon': 'https://www.nicovideo.jp/favicon.ico',
    },
    {
      'name': 'Yahoo! JAPAN',
      'url': 'https://www.yahoo.co.jp/',
      'description': '日本雅虎门户',
      'category': 'portal',
      'icon': 'https://s.yimg.jp/images/favicon.ico',
    },
    {
      'name': '楽天',
      'url': 'https://www.rakuten.co.jp/',
      'description': '日本乐天购物',
      'category': 'shopping',
      'icon': 'https://www.rakuten.co.jp/favicon.ico',
    },
    {
      'name': '朝日新闻',
      'url': 'https://www.asahi.com/',
      'description': '朝日新闻官方网站 - 测试翻译功能',
      'category': 'news',
      'icon': 'https://www.asahi.com/favicon.ico',
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // 加载页眉设置
    _loadSettings();

    // 加载开屏阶段的图标失败记录
    _loadSplashFailureStates();

    // 延迟启动阶段2预加载
    Future.delayed(const Duration(seconds: 2), () {
      _startStage2Preload();
    });

    // 预加载图标到内存缓存
    _preloadIconsToMemory();
  }

  // 加载开屏阶段的图标失败记录
  Future<void> _loadSplashFailureStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith('icon_failed_')) {
          final isFailedInSplash = prefs.getBool(key) ?? false;
          if (isFailedInSplash) {
            // 提取cacheKey并同步到全局失败缓存
            final cacheKey = key.replaceFirst('icon_failed_', '');
            _GlobalIconCache.failureCache[cacheKey] = 'failed';
          }
        }
      }
      // 已加载开屏阶段的图标失败记录
    } catch (e) {
      // 加载开屏失败记录失败
    }
  }

  @override
  void dispose() {
    // 页面销毁时停止后台预加载
    _stopBackgroundPreloading();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    await _proxyManager.loadSavedProxy();
    await _translateService.loadCacheFromStorage();
    _proxyManager.startHealthCheck();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      // 添加与WebView完全相同的页眉
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false, // 不显示返回按钮
        title: Container(
          height: 36,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Row(
            children: [
              const SizedBox(width: 12),
              Icon(Icons.search, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              // 1. 真正的地址栏输入框，和浏览器一样
              Expanded(
                child: TextField(
                  controller: _addressController,
                  focusNode: _addressFocusNode,
                  cursorColor: Colors.grey[500], // 1. 光标浅色一点
                  decoration: InputDecoration(
                    hintText: '搜索或输入网址',
                    hintStyle: TextStyle(fontSize: 14, color: Colors.grey[700]),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 0,
                    ), // 1. 完全去掉垂直padding
                    isDense: true,
                  ),
                  style: const TextStyle(
                    fontSize: 14,
                    height: 1.0,
                  ), // 1. 设置行高为1.0
                  textAlignVertical: TextAlignVertical.center,
                  onSubmitted: _navigateToUrl,
                  onTap: () {
                    setState(() {
                      _isAddressBarFocused = true;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),

              // 网络优化按钮（与WebView完全相同）
              GestureDetector(
                onTap: _toggleNetworkOptimize,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.vpn_key,
                          size: 18,
                          color: _isNetworkOptimized
                              ? Colors.green
                              : Colors.blue,
                        ),
                      ),
                      if (_isNetworkOptimized)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // AI翻译按钮（与WebView完全相同）
              GestureDetector(
                onTap: _toggleAITranslate,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Stack(
                    children: [
                      Center(
                        child: Icon(
                          Icons.translate,
                          size: 18,
                          color: _isAITranslateEnabled
                              ? Colors.blue
                              : Colors.purple,
                        ),
                      ),
                      if (_isAITranslateEnabled)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: const BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.check,
                              size: 6,
                              color: Colors.white,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // 翻译设置按钮（与WebView完全相同）
              GestureDetector(
                onTap: _openTranslateSettings,
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Icon(
                      Icons.settings,
                      size: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),
            ],
          ),
        ),
      ),
      body: SafeArea(child: _buildBody()),
      // 底栏部分：传统浏览器底栏
      // 多标签功能：去掉HomePage的底栏，使用BrowserApp统一的底栏
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Nacg翻译君标题
          Row(
            children: [
              // N字图标
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.blue[600],
                  borderRadius: BorderRadius.circular(6),
                ),
                child: const Center(
                  child: Text(
                    'N',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Text(
                'Nacg翻译君',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const Spacer(),
          // User info with VIP
          GestureDetector(
            onTap: _showUserMenu,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.blue[100],
                  child: Icon(Icons.person, size: 18, color: Colors.blue[700]),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _userId,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        if (_isVIP) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'VIP',
                              style: TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    // 完全清空，只显示空白页面
    return _buildMainContent();
  }

  Widget _buildCurrentPage() {
    switch (_currentPageIndex) {
      case 0:
        return _buildMainContent();
      case 1:
        return _buildToolsPage();
      case 2:
        return _buildProfilePage();
      default:
        return _buildMainContent();
    }
  }

  Widget _buildToolsPage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '工具箱',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildToolCard(
                  icon: Icons.speed,
                  title: '网络加速',
                  description: _isNetworkOptimized ? '已启用' : '点击启用',
                  color: Colors.blue,
                  isActive: _isNetworkOptimized,
                  onTap: _toggleNetworkOptimize,
                ),
                _buildToolCard(
                  icon: Icons.translate,
                  title: 'AI翻译',
                  description: _isAITranslateEnabled ? '已启用' : '点击启用',
                  color: Colors.green,
                  isActive: _isAITranslateEnabled,
                  onTap: () {
                    setState(() {
                      _isAITranslateEnabled = !_isAITranslateEnabled;
                    });
                  },
                ),
                _buildToolCard(
                  icon: Icons.bookmark,
                  title: '书签管理',
                  description: '管理收藏网站',
                  color: Colors.orange,
                  onTap: () {},
                ),
                _buildToolCard(
                  icon: Icons.history,
                  title: '浏览历史',
                  description: '查看访问记录',
                  color: Colors.purple,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfilePage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.blue[100],
                  child: Icon(Icons.person, size: 40, color: Colors.blue[700]),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _userName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    if (_isVIP) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'VIP',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: $_userId',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView(
              children: [
                _buildProfileItem(
                  icon: Icons.settings,
                  title: '设置',
                  onTap: () {},
                ),
                _buildProfileItem(
                  icon: Icons.help,
                  title: '帮助与反馈',
                  onTap: () {},
                ),
                _buildProfileItem(icon: Icons.info, title: '关于', onTap: () {}),
                _buildProfileItem(
                  icon: Icons.logout,
                  title: '退出账号',
                  onTap: () {},
                  isDestructive: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    // 0. 首屏支持下拉
    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 第2步：标题区域
              _buildTitleSection(),

              // 2. 在标题区下面增加7个网站入口
              _buildWebsiteEntries(),

              // 第3步：NacgAi智能搜索区
              _buildNacgAiSearchSection(),

              // 第3步：热门作品区
              _buildHotWorksSection(),
            ],
          ),
        ),
      ),
    );
  }

  // 第2步：构建标题区域
  Widget _buildTitleSection() {
    return Center(
      // 1. 区块居中显示
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12), // 稍微靠上移动，从20改为12
        child: Row(
          mainAxisSize: MainAxisSize.min, // 让Row内容居中
          crossAxisAlignment: CrossAxisAlignment.center, // N图标和标题平齐
          children: [
            // N字图标
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.blue[600],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Center(
                child: Text(
                  'N',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // 标题和副标题
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 主标题："Nacg翻译君" - 2. 用和N图标一样的蓝色
                Text(
                  'Nacg翻译君',
                  style: TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[600], // 使用和N图标一样的蓝色
                    letterSpacing: 1.2,
                  ),
                ),

                const SizedBox(height: 2),

                // 副标题："-世界的二（不起）次元" - 1. 副标题再小多一点点点
                Container(
                  width: 200, // 固定宽度确保对齐
                  child: Text(
                    '-世界的二（不起）次元',
                    style: TextStyle(
                      fontSize: 12, // 再小多一点点点，从14改为12
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.right, // 右对齐，让"元"字和"君"字对齐
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 2. 构建网站入口（按照您的图片重新实现）
  Widget _buildWebsiteEntries() {
    final websites = [
      {
        'name': '成为轻小说家吧',
        'url': 'https://syosetu.com/',
        'icon': Icons.book,
        'color': Colors.blue[600],
      },
      {
        'name': 'カクヨム',
        'url': 'https://kakuyomu.jp/',
        'icon': Icons.edit,
        'color': Colors.green[600],
      },
      {
        'name': 'アルファポリス',
        'url': 'https://www.alphapolis.co.jp/',
        'icon': Icons.star,
        'color': Colors.orange[600],
      },
      {
        'name': 'ハーメルン',
        'url': 'https://syosetu.org/',
        'icon': Icons.library_books,
        'color': Colors.purple[600],
      },
      {
        'name': 'ピクシブ小説',
        'url': 'https://www.pixiv.net/novel/',
        'icon': Icons.palette,
        'color': Colors.pink[600],
      },
      {
        'name': 'ノベルアップ+',
        'url': 'https://novelup.plus/',
        'icon': Icons.add_circle,
        'color': Colors.teal[600],
      },
      {
        'name': 'エブリスタ',
        'url': 'https://estar.jp/',
        'icon': Icons.favorite,
        'color': Colors.red[600],
      },
    ];

    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 20,
        horizontal: 0,
      ), // 0. 修复左右留白问题
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 2. 第一行：4个网站 - 使用Expanded确保对齐
          Row(
            children: websites
                .take(4)
                .map((website) => Expanded(child: _buildWebsiteEntry(website)))
                .toList(),
          ),

          const SizedBox(height: 20),

          // 2. 第二行：3个网站 - 与第一排对齐
          Row(
            children: [
              // 2. 第一个位置对齐第一排第一个
              Expanded(child: _buildWebsiteEntry(websites[4])),
              // 2. 第二个位置对齐第一排第二个
              Expanded(child: _buildWebsiteEntry(websites[5])),
              // 2. 第三个位置对齐第一排第三个
              Expanded(child: _buildWebsiteEntry(websites[6])),
              // 2. 第四个位置空白，对齐第一排第四个
              const Expanded(child: SizedBox()),
            ],
          ),
        ],
      ),
    );
  }

  // 构建单个网站入口（按照图片重新设计）
  Widget _buildWebsiteEntry(Map<String, dynamic> website) {
    return SizedBox(
      width: 70, // 2. 固定宽度确保对齐
      child: GestureDetector(
        onTap: () {
          // 修复：使用TabManager而不是Navigator.push
          _openWebsite(website);
        },
        child: Column(
          children: [
            // 圆形图标 - 修改颜色
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: (website['color'] as Color).withValues(
                  alpha: 0.1,
                ), // 底色更浅
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (website['color'] as Color).withValues(alpha: 0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  website['icon'] as IconData,
                  size: 24,
                  color: (website['color'] as Color).withValues(
                    alpha: 0.8,
                  ), // icon颜色更突出
                ),
              ),
            ),

            const SizedBox(height: 6),

            // 网站名称 - 只需要一行字
            Text(
              website['name'] as String,
              style: TextStyle(
                fontSize: 9,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 1, // 只显示一行字
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  // 第3步：构建NacgAi智能搜索区
  Widget _buildNacgAiSearchSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 2. 把"NacgAI 智能搜索"移到框外面作为标题
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: 'NacgAI',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[800],
                  ),
                ),
                TextSpan(
                  text: ' 智能搜索',
                  style: TextStyle(
                    fontSize: 10, // 1. 智能搜索这4个字做小多点
                    fontWeight: FontWeight.w400,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ),

        // 2. 搜索框区域（不包含标题）
        Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 搜索框 + 确定按键
              Row(
                children: [
                  // 搜索框
                  Expanded(
                    child: Container(
                      height: 44,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(22),
                        border: Border.all(color: Colors.blue[300]!, width: 1),
                      ),
                      child: TextField(
                        controller: _nacgAiSearchController,
                        cursorColor: Colors.grey[500], // 1. 光标浅色一点
                        decoration: InputDecoration(
                          hintText: '输入作品名称...', // 6. 不用在搜索框里面重复标题
                          hintStyle: TextStyle(color: Colors.grey[600]),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 0, // 0. 修复垂直居中
                          ),
                          prefixIcon: Icon(
                            Icons.search,
                            color: Colors.blue[600],
                          ),
                          isDense: true, // 0. 紧凑模式
                        ),
                        style: const TextStyle(height: 1.0), // 0. 设置行高
                        textAlignVertical: TextAlignVertical.center, // 0. 垂直居中
                        onSubmitted: (value) => _performNacgAiSearch(value),
                      ),
                    ),
                  ),

                  const SizedBox(width: 8),

                  // 确定按键
                  Container(
                    height: 44,
                    width: 44,
                    decoration: BoxDecoration(
                      color: Colors.blue[600],
                      borderRadius: BorderRadius.circular(22),
                    ),
                    child: IconButton(
                      onPressed: () =>
                          _performNacgAiSearch(_nacgAiSearchController.text),
                      icon: const Icon(
                        Icons.arrow_forward,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),

              // 1. 去掉搜索范围说明，让智能搜索专区更简洁
            ],
          ),
        ),
      ],
    );
  }

  // 第3步：构建热门作品区（纯文字+下划线+不同颜色）
  Widget _buildHotWorksSection() {
    // 2. 热门作品的排列尽量把一行占完！（但不能把作品名字切开）
    final hotWorks = [
      {'name': '无职转生', 'color': Colors.red[600]},
      {'name': '幼女战记', 'color': Colors.blue[600]},
      {'name': '刀剑神域', 'color': Colors.green[600]},
      {'name': '转生蜘蛛', 'color': Colors.cyan[600]},
      {'name': '五等分', 'color': Colors.amber[600]},
      {'name': '辉夜大小姐', 'color': Colors.red[400]},
      {'name': '盾之勇者成名录', 'color': Colors.orange[600]},
      {'name': '约会大作战', 'color': Colors.purple[600]},
      {'name': '魔法禁书目录', 'color': Colors.indigo[600]},
      {'name': '从零开始的异世界生活', 'color': Colors.teal[600]},
      {'name': '关于我转生变成史莱姆这档事', 'color': Colors.pink[600]},
      {'name': '我的青春恋爱物语果然有问题', 'color': Colors.blue[400]},
      {'name': '路人女主的养成方法', 'color': Colors.green[400]},
      {'name': '青春猪头少年不会梦到兔女郎学姐', 'color': Colors.orange[400]},
      {'name': '为美好世界', 'color': Colors.purple[400]},
      {'name': '工作细胞', 'color': Colors.pink[400]},
      {'name': '鬼灭之刃', 'color': Colors.teal[400]},
      {'name': '进击的巨人', 'color': Colors.indigo[400]},
      {'name': '一拳超人', 'color': Colors.cyan[400]},
      {'name': '海贼王', 'color': Colors.amber[400]},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 热门作品标题
          Text(
            '热门作品',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),

          const SizedBox(height: 12),

          // 热门作品 - 2. 尽量把一行占完！（但不能把作品名字切开）
          Wrap(
            spacing: 16, // 水平间距
            runSpacing: 12, // 垂直间距
            children: hotWorks
                .map(
                  (work) => GestureDetector(
                    onTap: () => _performNacgAiSearch(work['name'] as String),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ), // 4. 内边距
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: (work['color'] as Color).withOpacity(
                            0.5,
                          ), // 4. 浅色边框
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(
                          12,
                        ), // 4. 圆角外框（不是方框）
                      ),
                      child: Text(
                        work['name'] as String,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: work['color'] as Color,
                          // 4. 去掉下划线
                        ),
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  // 第3步：执行NacgAi搜索（3. 在谷歌搜索那两个网站的内容，site:搜索）
  void _performNacgAiSearch(String query) {
    if (query.trim().isEmpty) return;

    // 3. 使用Google的site:搜索，搜索轻小说机翻网和轻之国度
    final searchQuery =
        '${query.trim()} site:books.fishhawk.top OR site:www.lightnovel.fun';
    final searchUrl =
        'https://www.google.com/search?q=${Uri.encodeComponent(searchQuery)}';

    // 多标签功能：使用TabManager添加新标签页
    final tabManager = TabManager();
    tabManager.addTab(url: searchUrl, title: 'NacgAI搜索: ${query.trim()}');
  }

  Widget _buildWebsiteCard(Map<String, dynamic> website, int index) {
    return LongPressDraggable<int>(
      data: index,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 120,
          height: 120,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: Column(
            children: [
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, width: 0.5),
        ),
        child: Column(
          children: [
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: Text(
                  website['name'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[400],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
      onDragStarted: () {
        setState(() {
          _isDragMode = true;
          _draggedIndex = index;
        });
      },
      onDragEnd: (details) {
        setState(() {
          _isDragMode = false;
          _draggedIndex = null;
        });
      },
      child: InkWell(
        onTap: _isDragMode
            ? () {
                // 调试信息：拖拽模式下点击
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('🚫 拖拽模式下，点击被禁用'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            : () {
                // 调试信息：正常点击
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('✅ 正常点击: ${website['name']}'),
                    duration: const Duration(seconds: 2),
                  ),
                );
                _openWebsite(website);
              },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!, width: 0.5),
          ),
          child: Column(
            children: [
              // 图标区域 - 靠上对齐
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              // 文字区域，支持2行
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  Widget _buildToolCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: isActive ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isActive ? BorderSide(color: color, width: 2) : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isActive ? color.withValues(alpha: 0.1) : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: isActive ? color : Colors.grey[600]),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isActive ? color : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : Colors.grey[600]),
      title: Text(
        title,
        style: TextStyle(color: isDestructive ? Colors.red : Colors.black87),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  // ========== 页眉功能方法（与WebView完全相同） ==========

  // 显示地址栏输入对话框
  void _showAddressInput() {
    showDialog(
      context: context,
      builder: (context) {
        String inputUrl = '';
        return AlertDialog(
          title: const Text('输入网址'),
          content: TextField(
            decoration: const InputDecoration(
              hintText: '请输入网址或搜索内容',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) => inputUrl = value,
            onSubmitted: (value) {
              Navigator.of(context).pop();
              _navigateToWebView(value);
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _navigateToWebView(inputUrl);
              },
              child: const Text('前往'),
            ),
          ],
        );
      },
    );
  }

  // 1. 新的地址栏导航方法（支持http/https识别、www处理）
  void _navigateToUrl(String input) {
    if (input.trim().isEmpty) return;

    String url = input.trim();

    // 2. 支持http/https的识别
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // 3. 支持www的识别并跳转，不要先跳没有www的版本
      if (!url.startsWith('www.') && !url.contains('.')) {
        // 如果是搜索词，使用搜索引擎
        url = 'https://www.google.com/search?q=${Uri.encodeComponent(url)}';
      } else {
        // 如果是域名，优先添加www
        if (!url.startsWith('www.')) {
          url = 'www.$url';
        }
        url = 'https://$url';
      }
    }

    // 清空地址栏焦点
    _addressFocusNode.unfocus();
    setState(() {
      _isAddressBarFocused = false;
    });

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(initialUrl: url, title: '浏览器'),
      ),
    );
  }

  // 导航到WebView页面（保留原方法供其他地方使用）
  void _navigateToWebView(String url) {
    if (url.isEmpty) return;

    // 如果不是完整URL，添加https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(initialUrl: url, title: '浏览器'),
      ),
    );
  }

  // 切换网络优化（与WebView完全相同）
  void _toggleNetworkOptimize() {
    setState(() {
      _isNetworkOptimized = !_isNetworkOptimized;
    });

    // 保存设置
    _saveSettings();

    // 显示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isNetworkOptimized ? '网络优化已开启' : '网络优化已关闭'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // 切换AI翻译（与WebView完全相同）
  void _toggleAITranslate() {
    setState(() {
      _isAITranslateEnabled = !_isAITranslateEnabled;
    });

    // 保存设置
    _saveSettings();

    // 显示提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_isAITranslateEnabled ? 'AI翻译已开启' : 'AI翻译已关闭'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // 重新设计的翻译设置弹窗
  void _openTranslateSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '翻译设置',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1976D2),
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.white,
        elevation: 8,
        content: StatefulBuilder(
          builder: (context, setDialogState) {
            return SizedBox(
              width: 320, // 4. 稍微增加宽度适应新的翻译服务选择框
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 1. 用户信息条 - 美化设计
                  Container(
                    height: 36,
                    padding: const EdgeInsets.symmetric(horizontal: 14),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFFE3F2FD),
                          const Color(0xFFBBDEFB),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(18),
                      border: Border.all(
                        color: const Color(0xFF90CAF9),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF2196F3).withOpacity(0.15),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.person, size: 16, color: Colors.blue[600]),
                        const SizedBox(width: 6),
                        Text(
                          'ID: user123',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue[700],
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'VIP',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // 2. 翻译服务选择框 - 增加权重
                  GestureDetector(
                    onTap: () => _showTranslationServiceSelector(
                      context,
                      setDialogState,
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 10,
                      ), // 1. 减小内边距
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.white, const Color(0xFFF8F9FA)],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                        border: Border.all(
                          color: const Color(0xFF64B5F6),
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF2196F3).withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.translate,
                            color: Colors.blue[600],
                            size: 22,
                          ), // 2. 更大图标
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _getTranslationServiceName(
                                _selectedTranslationAPI,
                              ),
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w500,
                              ), // 1. 调小字体和权重
                            ),
                          ),
                          Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey[600],
                            size: 24,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 14), // 2. 调整翻译服务和开关之间的间距
                  // 3. 三个极度紧凑的开关按钮
                  _buildToggleOption('保留原文', _keepOriginalText, (value) {
                    setDialogState(() => _keepOriginalText = value);
                    setState(() {});
                    _saveSettings();
                  }),
                  const SizedBox(height: 2), // 2. 极小的开关间距
                  _buildToggleOption('总是翻译该网站', _alwaysTranslateSite, (value) {
                    setDialogState(() => _alwaysTranslateSite = value);
                    setState(() {});
                    _saveSettings();
                  }),
                  const SizedBox(height: 2), // 2. 极小的开关间距
                  _buildToggleOption('总是选择日语页面', _preferJapanesePage, (value) {
                    setDialogState(() => _preferJapanesePage = value);
                    setState(() {});
                    _saveSettings();
                  }),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // 保存设置
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('network_optimized', _isNetworkOptimized);
    await prefs.setBool('ai_translate_enabled', _isAITranslateEnabled);
    await prefs.setString('selected_translation_api', _selectedTranslationAPI);
    await prefs.setBool('keep_original_text', _keepOriginalText);
    await prefs.setBool('always_translate_site', _alwaysTranslateSite);
    await prefs.setBool('prefer_japanese_page', _preferJapanesePage);
  }

  // 加载设置
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isNetworkOptimized = prefs.getBool('network_optimized') ?? false;
      _isAITranslateEnabled = prefs.getBool('ai_translate_enabled') ?? false;
      _selectedTranslationAPI =
          prefs.getString('selected_translation_api') ?? 'deepseek';
      _keepOriginalText = prefs.getBool('keep_original_text') ?? false;
      _alwaysTranslateSite = prefs.getBool('always_translate_site') ?? false;
      _preferJapanesePage = prefs.getBool('prefer_japanese_page') ?? false;
    });
  }

  // 0. 下拉刷新方法
  Future<void> _onRefresh() async {
    // 模拟刷新延迟
    await Future.delayed(const Duration(seconds: 1));

    // 重新加载设置
    await _loadSettings();

    // 清空搜索框
    _nacgAiSearchController.clear();

    // 显示刷新完成提示
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('刷新完成'), duration: Duration(seconds: 1)),
      );
    }
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSideNavItem(0, Icons.home, '首页'),
          _buildSideNavItem(1, Icons.build, '工具'),
          _buildSideNavItem(2, Icons.person, '我的'),
        ],
      ),
    );
  }

  Widget _buildSideNavItem(int index, IconData icon, String label) {
    final isSelected = _currentPageIndex == index;
    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.blue : Colors.grey),
      title: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.blue : Colors.black87,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onTap: () => setState(() => _currentPageIndex = index),
    );
  }

  void _openWebsite(Map<String, dynamic> website) {
    // 用户点击链接时，停止后台预加载以节省资源
    _stopBackgroundPreloading();

    // 多标签功能：使用TabManager添加新标签页
    final tabManager = TabManager();

    tabManager.addTab(url: website['url'], title: website['name']);
  }

  void _showUserMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue[100],
                child: Icon(Icons.person, color: Colors.blue[700]),
              ),
              title: Row(
                children: [
                  Text(_userName),
                  if (_isVIP) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'VIP',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              subtitle: Text('ID: $_userId'),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('设置'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('退出'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  // Build add website card
  Widget _buildAddWebsiteCard() {
    return InkWell(
      onTap: _showAddWebsiteDialog,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[200]!, width: 0.5),
        ),
        child: Column(
          children: [
            // 图标区域 - 靠上对齐
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.add, size: 28, color: Colors.green[700]),
              ),
            ),
            const SizedBox(height: 12),
            // 文字区域
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: const Text(
                  '添加网站',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build website icon with caching and failure handling
  Widget _buildWebsiteIcon(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';

    // 检查是否已知加载失败（开屏阶段记录的失败状态）
    if (_GlobalIconCache.isIconFailedSync(iconUrl, websiteName)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.language, color: Colors.grey[600], size: 24),
      );
    }

    // 首先检查全局内存缓存
    if (_GlobalIconCache.memoryCache.containsKey(cacheKey)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: MemoryImage(
              const Base64Decoder().convert(
                _GlobalIconCache.memoryCache[cacheKey]!,
              ),
            ),
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    // 如果全局缓存中没有，检查是否正在加载
    if (!_GlobalIconCache.futureCache.containsKey(cacheKey)) {
      _GlobalIconCache.futureCache[cacheKey] = _loadCachedIcon(
        iconUrl,
        websiteName,
      );
    }

    return FutureBuilder<String?>(
      key: ValueKey(cacheKey),
      future: _GlobalIconCache.futureCache[cacheKey],
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData &&
            snapshot.data != null) {
          // 保存到全局内存缓存（持久化，不会因页面跳转而清除）
          _GlobalIconCache.memoryCache[cacheKey] = snapshot.data!;

          // Show cached icon
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: MemoryImage(
                  const Base64Decoder().convert(snapshot.data!),
                ),
                fit: BoxFit.cover,
              ),
            ),
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          // Show loading placeholder
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                ),
              ),
            ),
          );
        } else {
          // 加载失败，记录到全局失败状态并显示默认图标
          _GlobalIconCache.markIconFailed(iconUrl, websiteName);

          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.language, color: Colors.grey[600], size: 24),
          );
        }
      },
    );
  }

  // 预加载图标到内存缓存（后台线程执行）
  Future<void> _preloadIconsToMemory() async {
    if (_isPreloadingInBackground) return; // 避免重复预加载

    _isPreloadingInBackground = true;

    try {
      // 使用compute在后台线程执行，完全不阻塞UI
      final iconCache = await compute(_backgroundPreloadIcons, _websites);

      // 检查页面是否还存在，避免在页面跳转后更新状态
      if (mounted && _isPreloadingInBackground) {
        setState(() {
          _iconMemoryCache.addAll(iconCache);
        });
        // 后台预加载完成
      }
    } catch (e) {
      // 后台预加载失败，不影响正常使用
      // 后台图标预加载失败
    } finally {
      _isPreloadingInBackground = false;
    }
  }

  // 停止后台预加载（当用户离开页面时调用）
  void _stopBackgroundPreloading() {
    _isPreloadingInBackground = false;
  }

  // Load cached icon or fetch from network
  Future<String?> _loadCachedIcon(String iconUrl, String websiteName) async {
    if (iconUrl.isEmpty) return null;

    try {
      // Check cache first
      final cachedIcon = await _cacheManager.getCachedFavicon(iconUrl);
      if (cachedIcon != null) {
        return cachedIcon;
      }

      // Try multiple icon URLs
      final iconUrls = [
        iconUrl,
        iconUrl.replaceAll('/favicon.ico', '/apple-touch-icon.png'),
        iconUrl.replaceAll('/favicon.ico', '/icon.png'),
        // Special cases for problematic websites
        if (iconUrl.contains('yahoo.co.jp') || iconUrl.contains('yimg.jp')) ...[
          'https://www.yahoo.co.jp/favicon.ico',
          'https://s.yimg.jp/images/favicon.ico',
          'https://s.yimg.jp/images/top/sp2/cmn/logo-ns-131205.png',
          'https://yahoo.co.jp/favicon.ico',
        ],
        if (iconUrl.contains('syosetu.com')) ...[
          'https://syosetu.com/favicon.ico',
          'https://static.syosetu.com/favicon.ico',
          'https://syosetu.com/apple-touch-icon.png',
        ],
        if (iconUrl.contains('pixiv.net')) ...[
          'https://www.pixiv.net/favicon.ico',
          'https://s.pximg.net/common/images/favicon.ico',
          'https://pixiv.net/favicon.ico',
        ],
        if (iconUrl.contains('nicovideo.jp')) ...[
          'https://www.nicovideo.jp/favicon.ico',
          'https://secure-dcdn.cdn.nimg.jp/nicoaccount/usericon/s/252/2521.jpg',
          'https://nicovideo.jp/favicon.ico',
        ],
        if (iconUrl.contains('rakuten.co.jp')) ...[
          'https://www.rakuten.co.jp/favicon.ico',
          'https://r.r10s.jp/com/img/thumb/favicon.ico',
          'https://rakuten.co.jp/favicon.ico',
        ],
      ];

      for (final url in iconUrls) {
        try {
          // Fetch from network and cache
          final response = await http
              .get(
                Uri.parse(url),
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                  'Accept':
                      'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                  'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
                  'Accept-Encoding': 'gzip, deflate, br',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache',
                },
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
            final base64Icon = base64Encode(response.bodyBytes);
            await _cacheManager.cacheFavicon(iconUrl, base64Icon);
            return base64Icon;
          }
        } catch (e) {
          // Try next URL
          continue;
        }
      }
    } catch (e) {
      // Icon loading failed
    }

    return null;
  }

  // Show add website dialog
  void _showAddWebsiteDialog() {
    final TextEditingController inputController = TextEditingController();
    List<Map<String, String>> suggestions = [];
    String detectedName = '';
    String detectedUrl = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('添加网站'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: inputController,
                decoration: const InputDecoration(
                  labelText: '网站名称或网址',
                  hintText: '输入网站名称（如：GitHub）或网址（如：https://github.com）',
                ),
                onChanged: (value) {
                  setState(() {
                    if (_isUrl(value)) {
                      // 输入的是网址，标准化URL
                      detectedUrl = _normalizeUrl(value);
                      detectedName = _extractNameFromUrl(detectedUrl);
                      suggestions = [];
                    } else {
                      // 输入的是网站名称
                      suggestions = _getWebsiteSuggestions(value);
                      detectedName = value;
                      detectedUrl = '';
                    }
                  });
                },
              ),
              if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '检测到:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text('名称: $detectedName'),
                      Text('网址: $detectedUrl'),
                    ],
                  ),
                ),
              ],
              if (suggestions.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  '建议网站:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    itemCount: suggestions.length,
                    itemBuilder: (context, index) {
                      final suggestion = suggestions[index];
                      return ListTile(
                        dense: true,
                        title: Text(suggestion['name']!),
                        subtitle: Text(suggestion['url']!),
                        onTap: () {
                          inputController.text = suggestion['name']!;
                          setState(() {
                            detectedName = suggestion['name']!;
                            detectedUrl = suggestion['url']!;
                            suggestions = [];
                          });
                        },
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) {
                  _addWebsite(detectedName, detectedUrl);
                  Navigator.of(context).pop();
                } else if (inputController.text.isNotEmpty) {
                  // 如果只输入了名称，尝试从建议中找到匹配的网址
                  final suggestion = _getWebsiteSuggestions(
                    inputController.text,
                  ).firstOrNull;
                  if (suggestion != null) {
                    _addWebsite(suggestion['name']!, suggestion['url']!);
                    Navigator.of(context).pop();
                  }
                }
              },
              child: const Text('添加'),
            ),
          ],
        ),
      ),
    );
  }

  // Get website suggestions based on input
  List<Map<String, String>> _getWebsiteSuggestions(String input) {
    if (input.length < 2) return [];

    final websiteDatabase = [
      {'name': 'GitHub', 'url': 'https://github.com'},
      {'name': 'Stack Overflow', 'url': 'https://stackoverflow.com'},
      {'name': 'Reddit', 'url': 'https://reddit.com'},
      {'name': 'Twitter', 'url': 'https://twitter.com'},
      {'name': 'YouTube', 'url': 'https://youtube.com'},
      {'name': 'Google', 'url': 'https://google.com'},
      {'name': 'Wikipedia', 'url': 'https://wikipedia.org'},
      {'name': 'Amazon', 'url': 'https://amazon.com'},
      {'name': 'Netflix', 'url': 'https://netflix.com'},
      {'name': 'Facebook', 'url': 'https://facebook.com'},
      {'name': 'Instagram', 'url': 'https://instagram.com'},
      {'name': 'LinkedIn', 'url': 'https://linkedin.com'},
      {'name': 'Discord', 'url': 'https://discord.com'},
      {'name': 'Twitch', 'url': 'https://twitch.tv'},
      {'name': 'TikTok', 'url': 'https://tiktok.com'},
      // Japanese websites
      {'name': 'Yahoo Japan', 'url': 'https://yahoo.co.jp'},
      {'name': 'Rakuten', 'url': 'https://rakuten.co.jp'},
      {'name': 'Amazon Japan', 'url': 'https://amazon.co.jp'},
      {'name': 'Mercari', 'url': 'https://mercari.com'},
      {'name': 'Cookpad', 'url': 'https://cookpad.com'},
      {'name': 'Hatena', 'url': 'https://hatena.ne.jp'},
      {'name': 'Ameba', 'url': 'https://ameblo.jp'},
      {'name': 'FC2', 'url': 'https://fc2.com'},
      {'name': 'Goo', 'url': 'https://goo.ne.jp'},
      {'name': 'Livedoor', 'url': 'https://livedoor.com'},
    ];

    return websiteDatabase
        .where(
          (site) => site['name']!.toLowerCase().contains(input.toLowerCase()),
        )
        .take(5)
        .toList();
  }

  // Add website to the list
  void _addWebsite(String name, String url) {
    setState(() {
      _websites.add({
        'name': name,
        'url': url,
        'description': '用户添加的网站',
        'category': 'custom',
        'icon': '$url/favicon.ico',
      });
    });

    // Save to local storage
    _saveWebsitesToStorage();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('已添加网站: $name')));
  }

  // Save websites to local storage
  Future<void> _saveWebsitesToStorage() async {
    // This would save to SharedPreferences in a real app
    // For now, just keep in memory
  }

  // Check if input is a URL
  bool _isUrl(String input) {
    // 明确的协议前缀
    if (input.startsWith('http://') || input.startsWith('https://')) {
      return true;
    }

    // 包含域名格式的字符串
    if (input.contains('.') && input.length > 4) {
      // 检查是否包含常见的顶级域名
      final commonTlds = [
        '.com',
        '.org',
        '.net',
        '.jp',
        '.cn',
        '.co.jp',
        '.ne.jp',
      ];
      for (final tld in commonTlds) {
        if (input.toLowerCase().contains(tld)) {
          return true;
        }
      }

      // 检查是否是IP地址格式
      final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}');
      if (ipRegex.hasMatch(input)) {
        return true;
      }
    }

    return false;
  }

  // Normalize URL to ensure it has proper protocol
  String _normalizeUrl(String url) {
    String normalized = url.trim();

    // 如果已经有协议，直接返回
    if (normalized.startsWith('http://') || normalized.startsWith('https://')) {
      return normalized;
    }

    // 默认添加https://协议
    return 'https://$normalized';
  }

  // 阶段2: 导航页后续网站预加载
  Future<void> _startStage2Preload() async {
    if (_websites.length <= 12) return; // 没有后续网站

    try {
      final stage2Websites = _websites.skip(12).take(12).toList();

      for (final website in stage2Websites) {
        // 后台预加载，不阻塞UI
        _preloadWebsiteInBackground(website);
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // 启动阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    } catch (e) {
      // 阶段2预加载失败，继续阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    }
  }

  // 阶段3: 首屏内容深度预加载
  Future<void> _startStage3Preload() async {
    try {
      final stage3Websites = _websites.take(12).toList();

      for (final website in stage3Websites) {
        // 预加载首屏内容（基于1080×1980分辨率）
        _preloadFirstScreenContent(website['url']);
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      // 阶段3预加载失败，不影响使用
    }
  }

  // 后台预加载网站
  Future<void> _preloadWebsiteInBackground(Map<String, dynamic> website) async {
    try {
      // 预加载图标
      await _loadCachedIcon(website['icon'] ?? '', website['name']);

      // 预加载主页内容
      await _cacheManager.getCachedPageContent(website['url']);
    } catch (e) {
      // 后台预加载失败，不影响用户体验
    }
  }

  // 预加载首屏内容
  Future<void> _preloadFirstScreenContent(String url) async {
    try {
      // 获取页面内容
      final content = await _cacheManager.getCachedPageContent(url);
      if (content != null) {
        // 分析页面，提取首屏关键资源
        final resources = _extractFirstScreenResources(content, url);

        // 预加载关键资源
        for (final resource in resources.take(5)) {
          // 限制5个资源
          _preloadResource(resource);
        }
      }
    } catch (e) {
      // 首屏预加载失败，不影响使用
    }
  }

  // 提取首屏关键资源
  List<String> _extractFirstScreenResources(String html, String baseUrl) {
    final resources = <String>[];
    final uri = Uri.parse(baseUrl);

    // 简化实现：使用字符串匹配
    try {
      // 查找CSS文件
      final lines = html.split('\n');
      for (final line in lines) {
        if (line.contains('.css') && line.contains('href=')) {
          final start = line.indexOf('href="');
          if (start != -1) {
            final end = line.indexOf('"', start + 6);
            if (end != -1) {
              final href = line.substring(start + 6, end);
              if (href.contains('.css')) {
                final resourceUrl = _resolveUrl(href, uri);
                if (resourceUrl != null) resources.add(resourceUrl);
              }
            }
          }
        }

        // 查找图片文件
        if ((line.contains('.jpg') ||
                line.contains('.png') ||
                line.contains('.gif')) &&
            line.contains('src=')) {
          final start = line.indexOf('src="');
          if (start != -1) {
            final end = line.indexOf('"', start + 5);
            if (end != -1) {
              final src = line.substring(start + 5, end);
              if (src.contains('.jpg') ||
                  src.contains('.png') ||
                  src.contains('.gif')) {
                final resourceUrl = _resolveUrl(src, uri);
                if (resourceUrl != null && resources.length < 5) {
                  resources.add(resourceUrl);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      // 资源提取失败，返回空列表
    }

    return resources;
  }

  // 解析相对URL为绝对URL
  String? _resolveUrl(String href, Uri baseUri) {
    try {
      if (href.startsWith('http')) {
        return href;
      } else if (href.startsWith('//')) {
        return '${baseUri.scheme}:$href';
      } else if (href.startsWith('/')) {
        return '${baseUri.scheme}://${baseUri.host}$href';
      } else {
        return '${baseUri.scheme}://${baseUri.host}${baseUri.path}/$href';
      }
    } catch (e) {
      return null;
    }
  }

  // 预加载资源
  Future<void> _preloadResource(String url) async {
    try {
      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
            },
          )
          .timeout(const Duration(seconds: 3));

      // 简单缓存资源（实际项目中可以使用更复杂的缓存策略）
      if (response.statusCode == 200) {
        // 资源预加载成功
      }
    } catch (e) {
      // 资源预加载失败，不影响使用
    }
  }

  // Reorder websites when dragging
  void _reorderWebsites(int oldIndex, int newIndex) {
    setState(() {
      final website = _websites.removeAt(oldIndex);
      _websites.insert(newIndex, website);
    });
  }

  // Build remove area at bottom
  Widget _buildRemoveArea() {
    return DragTarget<int>(
      onAcceptWithDetails: (details) {
        final draggedIndex = details.data;
        final website = _websites[draggedIndex];

        // 所有网站都可以删除
        _deleteWebsite(website);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 80,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isHovering ? Colors.red[400] : Colors.red[300],
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: isHovering ? 32 : 28,
              ),
              const SizedBox(width: 8),
              Text(
                '拖动到此处删除',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isHovering ? 18 : 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Check if website is a default website (cannot be deleted)
  bool _isDefaultWebsite(Map<String, dynamic> website) {
    final defaultWebsiteNames = [
      '小説家になろう',
      'カクヨム',
      'pixiv',
      'ニコニコ動画',
      'Yahoo! JAPAN',
      '楽天',
    ];

    return defaultWebsiteNames.contains(website['name']);
  }

  // Delete website from list
  void _deleteWebsite(Map<String, dynamic> website) {
    setState(() {
      _websites.removeWhere(
        (w) => w['name'] == website['name'] && w['url'] == website['url'],
      );
    });

    // Show confirmation message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已删除 "${website['name']}"'),
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Save to SharedPreferences in a real app
  }

  // Extract website name from URL (1. 显示中文名字)
  String _extractNameFromUrl(String url) {
    try {
      // Ensure URL has protocol
      String fullUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        fullUrl = 'https://$url';
      }

      final uri = Uri.parse(fullUrl);
      String host = uri.host;

      // Remove www. prefix
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      // 1. 返回中文网站名字
      final Map<String, String> siteNames = {
        'books.fishhawk.top': '轻小说机翻网',
        'lightnovel.fun': '轻之国度',
        'google.com': '谷歌搜索',
        'baidu.com': '百度',
        'bilibili.com': '哔哩哔哩',
        'youtube.com': 'YouTube',
        'github.com': 'GitHub',
        'stackoverflow.com': 'Stack Overflow',
        'zhihu.com': '知乎',
        'weibo.com': '微博',
        'taobao.com': '淘宝',
        'jd.com': '京东',
        'tmall.com': '天猫',
        'amazon.com': '亚马逊',
        'twitter.com': 'Twitter',
        'facebook.com': 'Facebook',
        'instagram.com': 'Instagram',
        'reddit.com': 'Reddit',
        'wikipedia.org': '维基百科',
      };

      // 检查是否有对应的中文名
      for (String domain in siteNames.keys) {
        if (host.contains(domain)) {
          return siteNames[domain]!;
        }
      }

      // 如果没有中文名，返回首字母大写的域名
      final parts = host.split('.');
      if (parts.isNotEmpty) {
        return parts[0].substring(0, 1).toUpperCase() + parts[0].substring(1);
      }

      return host;
    } catch (e) {
      return url;
    }
  }

  // ========== 底栏功能实现 ==========

  // 构建底栏
  Widget _buildBottomBar() {
    return Container(
      height: 50, // 不高的底栏，类似传统浏览器
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 1. 返回按钮（可返回黑色，不可返回浅色）
          _buildBottomBarButton(
            icon: Icons.arrow_back,
            isEnabled: _canGoBack,
            onTap: _goBack,
          ),

          // 2. 前进按钮（可前进黑色，不可前进浅色）
          _buildBottomBarButton(
            icon: Icons.arrow_forward,
            isEnabled: _canGoForward,
            onTap: _goForward,
          ),

          // 3. 搜索按钮（等于选中地址栏并跳出输入法）
          _buildBottomBarButton(
            icon: Icons.search,
            isEnabled: true,
            onTap: _focusAddressBar,
          ),

          // 4. 页签页按钮（多标签功能）
          _buildTabButton(),

          // 5. 更多按钮（还没做）
          _buildBottomBarButton(
            icon: Icons.more_horiz,
            isEnabled: true,
            onTap: _showMoreMenu,
          ),
        ],
      ),
    );
  }

  // 构建底栏按钮
  Widget _buildBottomBarButton({
    required IconData icon,
    required bool isEnabled,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Icon(
            icon,
            size: 24,
            color: isEnabled ? Colors.black : Colors.grey[400],
          ),
        ),
      ),
    );
  }

  // 构建页签按钮（3. 优化页签页，简洁美观）
  Widget _buildTabButton() {
    return GestureDetector(
      onTap: _showTabManager,
      child: Container(
        width: 40,
        height: 40,
        child: Center(
          child: Container(
            width: 28,
            height: 20,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black, width: 1.5),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                '$_tabCount',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // ========== 底栏功能方法 ==========

  // 返回功能
  void _goBack() {
    // TODO: 实现返回功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('返回功能'), duration: Duration(seconds: 1)),
    );
  }

  // 前进功能
  void _goForward() {
    // TODO: 实现前进功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('前进功能'), duration: Duration(seconds: 1)),
    );
  }

  // 聚焦地址栏（等于选中地址栏并跳出输入法）
  void _focusAddressBar() {
    _addressFocusNode.requestFocus(); // 1. 直接聚焦地址栏
    setState(() {
      _isAddressBarFocused = true;
    });
  }

  // 显示标签管理器（2. 优化标签页界面，简洁美观）
  void _showTabManager() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: 400,
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // 顶部拖拽指示器
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),

            // 标题和新建按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '标签页', // 0. 标签页3个字调整大小
                  style: TextStyle(
                    fontSize: 16, // 0. 标签页字体调大，与加号对称
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: IconButton(
                    onPressed: _addNewTab,
                    icon: const Icon(Icons.add, color: Colors.white),
                    iconSize: 18, // 0. 加号调大，与标签页字体对称
                    padding: EdgeInsets.zero, // 0. 减少内边距，优化留白
                    constraints: const BoxConstraints(
                      minWidth: 32,
                      minHeight: 32,
                    ), // 0. 优化按钮尺寸
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 标签页列表
            Expanded(
              child: _tabCount == 0
                  ? const Center(
                      child: Text(
                        '暂无标签页',
                        style: TextStyle(color: Colors.grey, fontSize: 16),
                      ),
                    )
                  : ListView.builder(
                      itemCount: _tabs.length,
                      itemBuilder: (context, index) {
                        final tab = _tabs[index];
                        final isHome = tab['isHome'] == 'true';

                        return Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: ListTile(
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 4, // 2. 减少垂直padding，让框不要那么高
                            ),
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: isHome
                                    ? Colors.green[100]
                                    : Colors.blue[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                isHome ? Icons.home : Icons.web,
                                color: isHome
                                    ? Colors.green[600]
                                    : Colors.blue[600],
                              ),
                            ),
                            title: Text(
                              tab['title']!, // 1. 显示真实标题
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            subtitle: Text(
                              tab['url']!, // 2. 显示真实URL
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            // 1. 首屏标签页不显示关闭按钮
                            trailing: isHome
                                ? null
                                : Container(
                                    width: 32,
                                    height: 32,
                                    decoration: BoxDecoration(
                                      color: Colors.red[50],
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                    child: IconButton(
                                      onPressed: () => _closeTab(index),
                                      icon: Icon(
                                        Icons.close,
                                        color: Colors.red[600],
                                        size: 16,
                                      ),
                                      padding: EdgeInsets.zero,
                                    ),
                                  ),
                            onTap: () {
                              Navigator.pop(context);
                              _switchToTab(index);
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示更多菜单
  void _showMoreMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '更多功能',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.bookmark),
              title: const Text('书签'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现书签功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('历史记录'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现历史记录功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('设置'),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现设置功能
              },
            ),
          ],
        ),
      ),
    );
  }

  // ========== 标签页管理方法 ==========

  // 添加新标签页（4. 打开新页面会弹出一页新的网页！让你输入网址后跳转！）
  void _addNewTab() {
    setState(() {
      _tabCount++;
    });
    Navigator.pop(context); // 先关闭标签管理器

    // 2. 打开全屏的新标签页地址栏页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => _NewTabPage(
          onNavigate: (url) {
            Navigator.pop(context); // 关闭新标签页
            // 2. 添加新标签页到列表
            setState(() {
              _tabs.add({
                'title': _extractNameFromUrl(url), // 2. 读取网站名字
                'url': url,
                'isHome': 'false',
              });
            });
            _navigateToUrl(url); // 导航到输入的网址
          },
          onCancel: () {
            setState(() {
              _tabCount--; // 取消时减少标签数
            });
            Navigator.pop(context);
          },
        ),
      ),
    );
  }

  // 关闭标签页
  void _closeTab(int index) {
    final tab = _tabs[index];
    final isHome = tab['isHome'] == 'true';

    // 1. 首屏标签页不能关闭
    if (isHome) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('首屏标签页不能关闭'),
          duration: Duration(seconds: 1),
        ),
      );
      return;
    }

    // 2. 其他标签页可以正常关闭
    if (_tabs.length > 1) {
      setState(() {
        _tabs.removeAt(index);
        _tabCount--;
      });
      Navigator.pop(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('关闭标签页，当前共 $_tabCount 个标签'),
          duration: const Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('至少需要保留一个标签页'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  // 切换到指定标签页
  void _switchToTab(int index) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('切换到标签页 ${index + 1}'),
        duration: const Duration(seconds: 1),
      ),
    );
    // TODO: 实现标签页切换逻辑
  }

  // 获取翻译服务名称
  String _getTranslationServiceName(String apiKey) {
    switch (apiKey) {
      case 'deepseek':
        return 'DeepSeek V3 翻译';
      case 'tencent':
        return '腾讯翻译 API';
      case 'baidu':
        return '百度翻译 API';
      case 'none':
        return '词典翻译';
      default:
        return 'DeepSeek V3 翻译';
    }
  }

  // 显示翻译服务选择器
  void _showTranslationServiceSelector(
    BuildContext context,
    StateSetter setDialogState,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          '选择翻译服务',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF1976D2),
          ),
        ),
        contentPadding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        backgroundColor: Colors.white,
        elevation: 8,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildServiceOption(
              'DeepSeek V3 翻译',
              'deepseek',
              context,
              setDialogState,
            ),
            _buildServiceOption('腾讯翻译 API', 'tencent', context, setDialogState),
            _buildServiceOption('百度翻译 API', 'baidu', context, setDialogState),
            _buildServiceOption('词典翻译', 'none', context, setDialogState),
          ],
        ),
      ),
    );
  }

  // 构建服务选项 - 美化设计
  Widget _buildServiceOption(
    String title,
    String value,
    BuildContext context,
    StateSetter setDialogState,
  ) {
    final isSelected = _selectedTranslationAPI == value;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isSelected
              ? [const Color(0xFFE3F2FD), const Color(0xFFBBDEFB)]
              : [Colors.white, const Color(0xFFF8F9FA)],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? const Color(0xFF2196F3) : const Color(0xFFE0E0E0),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isSelected
                ? const Color(0xFF2196F3).withOpacity(0.15)
                : const Color(0xFF000000).withOpacity(0.05),
            blurRadius: isSelected ? 6 : 3,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        title: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected
                ? const Color(0xFF1976D2)
                : const Color(0xFF333333),
          ),
        ),
        leading: Radio<String>(
          value: value,
          groupValue: _selectedTranslationAPI,
          activeColor: const Color(0xFF2196F3),
          onChanged: (newValue) {
            setDialogState(() {
              _selectedTranslationAPI = newValue!;
            });
            setState(() {});
            _saveSettings();
            Navigator.pop(context);
          },
        ),
        onTap: () {
          setDialogState(() {
            _selectedTranslationAPI = value;
          });
          setState(() {});
          _saveSettings();
          Navigator.pop(context);
        },
      ),
    );
  }

  // 构建开关选项 - 3. 减小权重
  Widget _buildToggleOption(
    String title,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: 10,
        vertical: 4,
      ), // 1. 调整内边距，和字体更协调
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(6), // 1. 适中的圆角
        border: Border.all(
          color: const Color(0xFFDEE2E6),
          width: 0.8,
        ), // 1. 稍粗的边框
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 12, // 1. 调大字体，更易读
              fontWeight: FontWeight.w400, // 1. 适中的字重
              color: Color(0xFF555555),
            ),
          ),
          Transform.scale(
            scale: 0.5, // 2. 继续缩小开关
            child: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: const Color(0xFF42A5F5),
            ),
          ),
        ],
      ),
    );
  }
}

// 2. 新标签页全屏地址栏页面
class _NewTabPage extends StatefulWidget {
  final Function(String) onNavigate;
  final VoidCallback onCancel;

  const _NewTabPage({required this.onNavigate, required this.onCancel});

  @override
  State<_NewTabPage> createState() => _NewTabPageState();
}

class _NewTabPageState extends State<_NewTabPage> {
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _urlFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 自动聚焦地址栏
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _urlFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _urlController.dispose();
    _urlFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      // 2. 网页的页眉部分是网址输入+前往的箭头icon！！！
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        automaticallyImplyLeading: false, // 不显示返回按钮
        title: Container(
          height: 36,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(18),
            border: Border.all(color: Colors.grey[300]!, width: 1),
          ),
          child: Row(
            children: [
              const SizedBox(width: 12),
              Icon(Icons.search, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              // 2. 网址输入框
              Expanded(
                child: TextField(
                  controller: _urlController,
                  focusNode: _urlFocusNode,
                  cursorColor: Colors.grey[500], // 1. 光标浅色一点
                  decoration: InputDecoration(
                    hintText: '搜索或输入网址',
                    hintStyle: TextStyle(fontSize: 14, color: Colors.grey[700]),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 0),
                    isDense: true,
                  ),
                  style: const TextStyle(fontSize: 14, height: 1.0),
                  textAlignVertical: TextAlignVertical.center,
                  onSubmitted: (value) {
                    if (value.trim().isNotEmpty) {
                      widget.onNavigate(value.trim());
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),

              // 2. 前往的箭头icon
              GestureDetector(
                onTap: () {
                  if (_urlController.text.trim().isNotEmpty) {
                    widget.onNavigate(_urlController.text.trim());
                  }
                },
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: Colors.blue[600],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.arrow_forward,
                    size: 14,
                    color: Colors.white,
                  ),
                ),
              ),

              const SizedBox(width: 6),

              // 关闭按钮
              GestureDetector(
                onTap: widget.onCancel,
                child: Container(
                  width: 24,
                  height: 24,
                  child: Center(
                    child: Icon(Icons.close, size: 18, color: Colors.grey[600]),
                  ),
                ),
              ),

              const SizedBox(width: 12),
            ],
          ),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 大标题
              const Text(
                '新建标签页',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),

              const SizedBox(height: 16),

              // 副标题
              Text(
                '在上方地址栏输入网址或搜索内容',
                style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              ),

              const SizedBox(height: 40),

              // 快捷操作按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildQuickAction(
                    icon: Icons.home,
                    label: '返回首页',
                    onTap: () => widget.onCancel(),
                  ),
                  const SizedBox(width: 40),
                  _buildQuickAction(
                    icon: Icons.bookmark,
                    label: '书签',
                    onTap: () {
                      // TODO: 打开书签
                    },
                  ),
                  const SizedBox(width: 40),
                  _buildQuickAction(
                    icon: Icons.history,
                    label: '历史',
                    onTap: () {
                      // TODO: 打开历史记录
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建快捷操作按钮
  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(30),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: Icon(icon, size: 28, color: Colors.grey[700]),
          ),
          const SizedBox(height: 8),
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }
}
