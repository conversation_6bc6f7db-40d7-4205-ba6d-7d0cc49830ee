import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'webview_page.dart';
import 'cache_manager.dart';
import 'proxy_manager.dart';
import 'google_translate_service.dart';

// 后台预加载函数，在独立线程中执行
Future<Map<String, String>> _backgroundPreloadIcons(
  List<Map<String, dynamic>> websites,
) async {
  final iconCache = <String, String>{};

  for (final website in websites) {
    try {
      final iconUrl = website['icon'] ?? '';
      final websiteName = website['name'];

      if (iconUrl.isNotEmpty) {
        // 在后台线程中下载图标
        final response = await http
            .get(
              Uri.parse(iconUrl),
              headers: {
                'User-Agent':
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
              },
            )
            .timeout(const Duration(seconds: 5));

        if (response.statusCode == 200) {
          final base64Data = base64Encode(response.bodyBytes);
          final cacheKey = '${iconUrl}_$websiteName';
          iconCache[cacheKey] = base64Data;
        }
      }
    } catch (e) {
      // 单个图标加载失败，继续下一个
      continue;
    }
  }

  return iconCache;
}

// 全局图标缓存，防止页面跳转时清理
class _GlobalIconCache {
  static final Map<String, String> _memoryCache = {};
  static final Map<String, String> _failureCache = {}; // 记录失败的图标
  static final Map<String, Future<String?>> _futureCache = {};

  static Map<String, String> get memoryCache => _memoryCache;
  static Map<String, String> get failureCache => _failureCache;
  static Map<String, Future<String?>> get futureCache => _futureCache;

  // 清理过期的Future缓存
  static void cleanupFutureCache() {
    // 移除已完成的Future，避免内存泄漏
    final keysToRemove = <String>[];
    for (final entry in _futureCache.entries) {
      // 检查Future是否已完成（通过try-catch方式）
      try {
        entry.value
            .then((_) {
              keysToRemove.add(entry.key);
            })
            .catchError((_) {
              keysToRemove.add(entry.key);
            });
      } catch (e) {
        // Future已完成
        keysToRemove.add(entry.key);
      }
    }
    for (final key in keysToRemove) {
      _futureCache.remove(key);
    }
  }

  // 记录图标加载失败
  static void markIconFailed(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    _failureCache[cacheKey] = 'failed';
  }

  // 检查图标是否已知失败（包括开屏阶段的失败记录）
  static Future<bool> isIconFailed(String iconUrl, String websiteName) async {
    final cacheKey = '${iconUrl}_$websiteName';

    // 首先检查内存中的失败记录
    if (_failureCache.containsKey(cacheKey)) {
      return true;
    }

    // 检查开屏阶段保存的失败记录
    try {
      final prefs = await SharedPreferences.getInstance();
      final isFailedInSplash = prefs.getBool('icon_failed_$cacheKey') ?? false;
      if (isFailedInSplash) {
        // 将开屏阶段的失败记录同步到内存缓存
        _failureCache[cacheKey] = 'failed';
        return true;
      }
    } catch (e) {
      // 读取失败，不影响正常流程
    }

    return false;
  }

  // 同步版本的检查方法（用于同步调用）
  static bool isIconFailedSync(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';
    return _failureCache.containsKey(cacheKey);
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _currentPageIndex = 0;
  final CacheManager _cacheManager = CacheManager();
  final ProxyManager _proxyManager = ProxyManager();
  final GoogleTranslateService _translateService = GoogleTranslateService();

  // User info
  final String _userName = 'JapanHub用户';
  final String _userId = 'JH001';
  final bool _isVIP = true;

  // Tool states
  bool _isNetworkAccelerated = true;
  bool _isAITranslateEnabled = true;

  // Drag and drop states
  bool _isDragMode = false;
  int? _draggedIndex;

  // 图标内存缓存，避免重复加载
  final Map<String, String> _iconMemoryCache = {};
  final Map<String, Future<String?>> _iconFutureCache = {};

  // 预加载控制
  bool _isPreloadingInBackground = false;

  // 轻小说作品数据
  final List<Map<String, dynamic>> _lightNovels = [
    {'title': '转生成为史莱姆', 'author': '伏瀬', 'color': Colors.blue[600]},
    {'title': '关于我转生变成史莱姆这档事', 'author': '伏瀬', 'color': Colors.green[600]},
    {'title': '无职转生', 'author': '理不尽な孫の手', 'color': Colors.orange[600]},
    {'title': '盾之勇者成名录', 'author': 'アネコユサギ', 'color': Colors.purple[600]},
    {'title': 'Re:从零开始的异世界生活', 'author': '長月達平', 'color': Colors.red[600]},
    {'title': '幼女战记', 'author': 'カルロ・ゼン', 'color': Colors.indigo[600]},
    {'title': '魔法科高校的劣等生', 'author': '佐島勤', 'color': Colors.teal[600]},
    {'title': '刀剑神域', 'author': '川原礫', 'color': Colors.cyan[600]},
  ];

  // Website data
  final List<Map<String, dynamic>> _websites = [
    {
      'name': '小説家になろう',
      'url': 'https://syosetu.com/',
      'description': '日本最大的网络小说平台',
      'category': 'novel',
      'icon': 'https://syosetu.com/favicon.ico',
    },
    {
      'name': 'カクヨム',
      'url': 'https://kakuyomu.jp/',
      'description': 'KADOKAWA旗下小说平台',
      'category': 'novel',
      'icon': 'https://kakuyomu.jp/favicon.ico',
    },
    {
      'name': 'pixiv',
      'url': 'https://www.pixiv.net/',
      'description': '插画艺术社区',
      'category': 'art',
      'icon': 'https://www.pixiv.net/favicon.ico',
    },
    {
      'name': 'ニコニコ動画',
      'url': 'https://www.nicovideo.jp/',
      'description': '弹幕视频网站',
      'category': 'video',
      'icon': 'https://www.nicovideo.jp/favicon.ico',
    },
    {
      'name': 'Yahoo! JAPAN',
      'url': 'https://www.yahoo.co.jp/',
      'description': '日本雅虎门户',
      'category': 'portal',
      'icon': 'https://s.yimg.jp/images/favicon.ico',
    },
    {
      'name': '楽天',
      'url': 'https://www.rakuten.co.jp/',
      'description': '日本乐天购物',
      'category': 'shopping',
      'icon': 'https://www.rakuten.co.jp/favicon.ico',
    },
    {
      'name': '朝日新闻',
      'url': 'https://www.asahi.com/',
      'description': '朝日新闻官方网站 - 测试翻译功能',
      'category': 'news',
      'icon': 'https://www.asahi.com/favicon.ico',
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();

    // 加载开屏阶段的图标失败记录
    _loadSplashFailureStates();

    // 延迟启动阶段2预加载
    Future.delayed(const Duration(seconds: 2), () {
      _startStage2Preload();
    });

    // 预加载图标到内存缓存
    _preloadIconsToMemory();
  }

  // 加载开屏阶段的图标失败记录
  Future<void> _loadSplashFailureStates() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith('icon_failed_')) {
          final isFailedInSplash = prefs.getBool(key) ?? false;
          if (isFailedInSplash) {
            // 提取cacheKey并同步到全局失败缓存
            final cacheKey = key.replaceFirst('icon_failed_', '');
            _GlobalIconCache.failureCache[cacheKey] = 'failed';
          }
        }
      }
      debugPrint('已加载开屏阶段的图标失败记录: ${_GlobalIconCache.failureCache.length} 个');
    } catch (e) {
      debugPrint('加载开屏失败记录失败: $e');
    }
  }

  @override
  void dispose() {
    // 页面销毁时停止后台预加载
    _stopBackgroundPreloading();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    await _proxyManager.loadSavedProxy();
    await _translateService.loadCacheFromStorage();
    _proxyManager.startHealthCheck();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // Header - only show on home page
          if (_currentPageIndex == 0) _buildHeader(),
          // Main content
          Expanded(
            child: SafeArea(top: _currentPageIndex != 0, child: _buildBody()),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 8),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // App title
          const Text(
            'Japan Hub',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          // User info with VIP
          GestureDetector(
            onTap: _showUserMenu,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.blue[100],
                  child: Icon(Icons.person, size: 18, color: Colors.blue[700]),
                ),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          _userId,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                        if (_isVIP) ...[
                          const SizedBox(width: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 1,
                            ),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'VIP',
                              style: TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth < 600) {
          return Column(
            children: [
              Expanded(child: _buildCurrentPage()),
              _buildBottomNavigation(),
            ],
          );
        } else {
          return Row(
            children: [
              _buildSideNavigation(),
              Expanded(child: _buildCurrentPage()),
            ],
          );
        }
      },
    );
  }

  Widget _buildCurrentPage() {
    switch (_currentPageIndex) {
      case 0:
        return _buildMainContent();
      case 1:
        return _buildToolsPage();
      case 2:
        return _buildProfilePage();
      default:
        return _buildMainContent();
    }
  }

  Widget _buildToolsPage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '工具箱',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildToolCard(
                  icon: Icons.speed,
                  title: '网络加速',
                  description: _isNetworkAccelerated ? '已启用' : '点击启用',
                  color: Colors.blue,
                  isActive: _isNetworkAccelerated,
                  onTap: () {
                    setState(() {
                      _isNetworkAccelerated = !_isNetworkAccelerated;
                    });
                  },
                ),
                _buildToolCard(
                  icon: Icons.translate,
                  title: 'AI翻译',
                  description: _isAITranslateEnabled ? '已启用' : '点击启用',
                  color: Colors.green,
                  isActive: _isAITranslateEnabled,
                  onTap: () {
                    setState(() {
                      _isAITranslateEnabled = !_isAITranslateEnabled;
                    });
                  },
                ),
                _buildToolCard(
                  icon: Icons.bookmark,
                  title: '书签管理',
                  description: '管理收藏网站',
                  color: Colors.orange,
                  onTap: () {},
                ),
                _buildToolCard(
                  icon: Icons.history,
                  title: '浏览历史',
                  description: '查看访问记录',
                  color: Colors.purple,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfilePage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: Colors.blue[100],
                  child: Icon(Icons.person, size: 40, color: Colors.blue[700]),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      _userName,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    if (_isVIP) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'VIP',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'ID: $_userId',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: ListView(
              children: [
                _buildProfileItem(
                  icon: Icons.settings,
                  title: '设置',
                  onTap: () {},
                ),
                _buildProfileItem(
                  icon: Icons.help,
                  title: '帮助与反馈',
                  onTap: () {},
                ),
                _buildProfileItem(icon: Icons.info, title: '关于', onTap: () {}),
                _buildProfileItem(
                  icon: Icons.logout,
                  title: '退出账号',
                  onTap: () {},
                  isDestructive: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 网站导航区域
          Expanded(
            flex: 2,
            child: Stack(
              children: [
                // 主要的网格视图
                GridView.builder(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    childAspectRatio: 0.9,
                  ),
                  itemCount: _websites.length + 1, // +1 for add button
                  itemBuilder: (context, index) {
                    if (index == _websites.length) {
                      return _buildAddWebsiteCard();
                    }
                    final website = _websites[index];
                    return DragTarget<int>(
                      onAcceptWithDetails: (details) {
                        final draggedIndex = details.data;
                        if (draggedIndex != index) {
                          _reorderWebsites(draggedIndex, index);
                        }
                      },
                      builder: (context, candidateData, rejectedData) {
                        return _buildWebsiteCard(website, index);
                      },
                    );
                  },
                ),
                // 底部移除区域
                if (_isDragMode)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: _buildRemoveArea(),
                  ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 不规则排序轻小说作品区
          Expanded(flex: 1, child: _buildLightNovelArea()),
        ],
      ),
    );
  }

  // 构建不规则排序轻小说作品区
  Widget _buildLightNovelArea() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Stack(
            children: [
              // 不规则排序的轻小说作品
              ..._buildIrregularLightNovels(constraints),
              // "世界的二（不起）次元"标题 - 靠右
              Positioned(
                top: 16,
                right: 24, // 更靠右一些
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Text(
                    '世界的二（不起）次元',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 生成不规则排序的轻小说作品
  List<Widget> _buildIrregularLightNovels(BoxConstraints constraints) {
    final List<Widget> widgets = [];

    // 预定义的不规则位置（相对于容器的百分比）
    final List<Map<String, double>> positions = [
      {'left': 0.05, 'top': 0.15},
      {'left': 0.25, 'top': 0.05},
      {'left': 0.45, 'top': 0.25},
      {'left': 0.65, 'top': 0.1},
      {'left': 0.15, 'top': 0.45},
    ];

    // 只显示前5个轻小说作品，颜色不超过5种
    final displayNovels = _lightNovels.take(5).toList();

    for (int i = 0; i < displayNovels.length && i < positions.length; i++) {
      final novel = displayNovels[i];
      final position = positions[i];

      widgets.add(
        Positioned(
          left: position['left']! * constraints.maxWidth,
          top: position['top']! * constraints.maxHeight,
          child: _buildLightNovelChip(novel),
        ),
      );
    }

    return widgets;
  }

  // 构建单个轻小说作品标签
  Widget _buildLightNovelChip(Map<String, dynamic> novel) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: novel['color'] ?? Colors.blue[600],
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        novel['title'] ?? '',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildWebsiteCard(Map<String, dynamic> website, int index) {
    return LongPressDraggable<int>(
      data: index,
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 120,
          height: 120,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue, width: 2),
          ),
          child: Column(
            children: [
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!, width: 0.5),
        ),
        child: Column(
          children: [
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: Text(
                  website['name'],
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[400],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
      onDragStarted: () {
        setState(() {
          _isDragMode = true;
          _draggedIndex = index;
        });
      },
      onDragEnd: (details) {
        setState(() {
          _isDragMode = false;
          _draggedIndex = null;
        });
      },
      child: InkWell(
        onTap: _isDragMode ? null : () => _openWebsite(website),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!, width: 0.5),
          ),
          child: Column(
            children: [
              // 图标区域 - 靠上对齐
              Container(
                height: 60,
                alignment: Alignment.topCenter,
                padding: const EdgeInsets.only(top: 6),
                child: _buildWebsiteIcon(
                  website['icon'] ?? '',
                  website['name'],
                ),
              ),
              const SizedBox(height: 12),
              // 文字区域，支持2行
              Expanded(
                child: Container(
                  alignment: Alignment.topCenter,
                  child: Text(
                    website['name'],
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  Widget _buildToolCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    bool isActive = false,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: isActive ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isActive ? BorderSide(color: color, width: 2) : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isActive ? color.withValues(alpha: 0.1) : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: isActive ? color : Colors.grey[600]),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isActive ? color : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : Colors.grey[600]),
      title: Text(
        title,
        style: TextStyle(color: isDestructive ? Colors.red : Colors.black87),
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          _buildNavItem(0, Icons.home, '首页'),
          _buildNavItem(1, Icons.build, '工具'),
          _buildNavItem(2, Icons.person, '我的'),
        ],
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label) {
    final isSelected = _currentPageIndex == index;
    return Expanded(
      child: InkWell(
        onTap: () => setState(() => _currentPageIndex = index),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.blue : Colors.grey,
                size: 24,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSideNavigation() {
    return Container(
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSideNavItem(0, Icons.home, '首页'),
          _buildSideNavItem(1, Icons.build, '工具'),
          _buildSideNavItem(2, Icons.person, '我的'),
        ],
      ),
    );
  }

  Widget _buildSideNavItem(int index, IconData icon, String label) {
    final isSelected = _currentPageIndex == index;
    return ListTile(
      leading: Icon(icon, color: isSelected ? Colors.blue : Colors.grey),
      title: Text(
        label,
        style: TextStyle(
          color: isSelected ? Colors.blue : Colors.black87,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onTap: () => setState(() => _currentPageIndex = index),
    );
  }

  void _openWebsite(Map<String, dynamic> website) {
    // 用户点击链接时，停止后台预加载以节省资源
    _stopBackgroundPreloading();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            WebViewPage(initialUrl: website['url'], title: website['name']),
      ),
    );
  }

  void _showUserMenu() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue[100],
                child: Icon(Icons.person, color: Colors.blue[700]),
              ),
              title: Row(
                children: [
                  Text(_userName),
                  if (_isVIP) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        'VIP',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              subtitle: Text('ID: $_userId'),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('设置'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('退出'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  // Build add website card
  Widget _buildAddWebsiteCard() {
    return InkWell(
      onTap: _showAddWebsiteDialog,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green[200]!, width: 0.5),
        ),
        child: Column(
          children: [
            // 图标区域 - 靠上对齐
            Container(
              height: 60,
              alignment: Alignment.topCenter,
              padding: const EdgeInsets.only(top: 6),
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.green[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.add, size: 28, color: Colors.green[700]),
              ),
            ),
            const SizedBox(height: 12),
            // 文字区域
            Expanded(
              child: Container(
                alignment: Alignment.topCenter,
                child: const Text(
                  '添加网站',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build website icon with caching and failure handling
  Widget _buildWebsiteIcon(String iconUrl, String websiteName) {
    final cacheKey = '${iconUrl}_$websiteName';

    // 检查是否已知加载失败（开屏阶段记录的失败状态）
    if (_GlobalIconCache.isIconFailedSync(iconUrl, websiteName)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(Icons.language, color: Colors.grey[600], size: 24),
      );
    }

    // 首先检查全局内存缓存
    if (_GlobalIconCache.memoryCache.containsKey(cacheKey)) {
      return Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: MemoryImage(
              const Base64Decoder().convert(
                _GlobalIconCache.memoryCache[cacheKey]!,
              ),
            ),
            fit: BoxFit.cover,
          ),
        ),
      );
    }

    // 如果全局缓存中没有，检查是否正在加载
    if (!_GlobalIconCache.futureCache.containsKey(cacheKey)) {
      _GlobalIconCache.futureCache[cacheKey] = _loadCachedIcon(
        iconUrl,
        websiteName,
      );
    }

    return FutureBuilder<String?>(
      key: ValueKey(cacheKey),
      future: _GlobalIconCache.futureCache[cacheKey],
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData &&
            snapshot.data != null) {
          // 保存到全局内存缓存（持久化，不会因页面跳转而清除）
          _GlobalIconCache.memoryCache[cacheKey] = snapshot.data!;

          // Show cached icon
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: MemoryImage(
                  const Base64Decoder().convert(snapshot.data!),
                ),
                fit: BoxFit.cover,
              ),
            ),
          );
        } else if (snapshot.connectionState == ConnectionState.waiting) {
          // Show loading placeholder
          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                ),
              ),
            ),
          );
        } else {
          // 加载失败，记录到全局失败状态并显示默认图标
          _GlobalIconCache.markIconFailed(iconUrl, websiteName);

          return Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.language, color: Colors.grey[600], size: 24),
          );
        }
      },
    );
  }

  // 预加载图标到内存缓存（后台线程执行）
  Future<void> _preloadIconsToMemory() async {
    if (_isPreloadingInBackground) return; // 避免重复预加载

    _isPreloadingInBackground = true;

    try {
      // 使用compute在后台线程执行，完全不阻塞UI
      final iconCache = await compute(_backgroundPreloadIcons, _websites);

      // 检查页面是否还存在，避免在页面跳转后更新状态
      if (mounted && _isPreloadingInBackground) {
        setState(() {
          _iconMemoryCache.addAll(iconCache);
        });
        debugPrint('后台预加载完成，缓存了 ${iconCache.length} 个图标');
      }
    } catch (e) {
      // 后台预加载失败，不影响正常使用
      debugPrint('后台图标预加载失败: $e');
    } finally {
      _isPreloadingInBackground = false;
    }
  }

  // 停止后台预加载（当用户离开页面时调用）
  void _stopBackgroundPreloading() {
    _isPreloadingInBackground = false;
  }

  // Load cached icon or fetch from network
  Future<String?> _loadCachedIcon(String iconUrl, String websiteName) async {
    if (iconUrl.isEmpty) return null;

    try {
      // Check cache first
      final cachedIcon = await _cacheManager.getCachedFavicon(iconUrl);
      if (cachedIcon != null) {
        return cachedIcon;
      }

      // Try multiple icon URLs
      final iconUrls = [
        iconUrl,
        iconUrl.replaceAll('/favicon.ico', '/apple-touch-icon.png'),
        iconUrl.replaceAll('/favicon.ico', '/icon.png'),
        // Special cases for problematic websites
        if (iconUrl.contains('yahoo.co.jp') || iconUrl.contains('yimg.jp')) ...[
          'https://www.yahoo.co.jp/favicon.ico',
          'https://s.yimg.jp/images/favicon.ico',
          'https://s.yimg.jp/images/top/sp2/cmn/logo-ns-131205.png',
          'https://yahoo.co.jp/favicon.ico',
        ],
        if (iconUrl.contains('syosetu.com')) ...[
          'https://syosetu.com/favicon.ico',
          'https://static.syosetu.com/favicon.ico',
          'https://syosetu.com/apple-touch-icon.png',
        ],
        if (iconUrl.contains('pixiv.net')) ...[
          'https://www.pixiv.net/favicon.ico',
          'https://s.pximg.net/common/images/favicon.ico',
          'https://pixiv.net/favicon.ico',
        ],
        if (iconUrl.contains('nicovideo.jp')) ...[
          'https://www.nicovideo.jp/favicon.ico',
          'https://secure-dcdn.cdn.nimg.jp/nicoaccount/usericon/s/252/2521.jpg',
          'https://nicovideo.jp/favicon.ico',
        ],
        if (iconUrl.contains('rakuten.co.jp')) ...[
          'https://www.rakuten.co.jp/favicon.ico',
          'https://r.r10s.jp/com/img/thumb/favicon.ico',
          'https://rakuten.co.jp/favicon.ico',
        ],
      ];

      for (final url in iconUrls) {
        try {
          // Fetch from network and cache
          final response = await http
              .get(
                Uri.parse(url),
                headers: {
                  'User-Agent':
                      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                  'Accept':
                      'image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                  'Accept-Language': 'ja,en-US;q=0.9,en;q=0.8',
                  'Accept-Encoding': 'gzip, deflate, br',
                  'Cache-Control': 'no-cache',
                  'Pragma': 'no-cache',
                },
              )
              .timeout(const Duration(seconds: 5));

          if (response.statusCode == 200 && response.bodyBytes.isNotEmpty) {
            final base64Icon = base64Encode(response.bodyBytes);
            await _cacheManager.cacheFavicon(iconUrl, base64Icon);
            return base64Icon;
          }
        } catch (e) {
          // Try next URL
          continue;
        }
      }
    } catch (e) {
      // Icon loading failed
    }

    return null;
  }

  // Show add website dialog
  void _showAddWebsiteDialog() {
    final TextEditingController inputController = TextEditingController();
    List<Map<String, String>> suggestions = [];
    String detectedName = '';
    String detectedUrl = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('添加网站'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: inputController,
                decoration: const InputDecoration(
                  labelText: '网站名称或网址',
                  hintText: '输入网站名称（如：GitHub）或网址（如：https://github.com）',
                ),
                onChanged: (value) {
                  setState(() {
                    if (_isUrl(value)) {
                      // 输入的是网址，标准化URL
                      detectedUrl = _normalizeUrl(value);
                      detectedName = _extractNameFromUrl(detectedUrl);
                      suggestions = [];
                    } else {
                      // 输入的是网站名称
                      suggestions = _getWebsiteSuggestions(value);
                      detectedName = value;
                      detectedUrl = '';
                    }
                  });
                },
              ),
              if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.green[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '检测到:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text('名称: $detectedName'),
                      Text('网址: $detectedUrl'),
                    ],
                  ),
                ),
              ],
              if (suggestions.isNotEmpty) ...[
                const SizedBox(height: 16),
                const Text(
                  '建议网站:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    itemCount: suggestions.length,
                    itemBuilder: (context, index) {
                      final suggestion = suggestions[index];
                      return ListTile(
                        dense: true,
                        title: Text(suggestion['name']!),
                        subtitle: Text(suggestion['url']!),
                        onTap: () {
                          inputController.text = suggestion['name']!;
                          setState(() {
                            detectedName = suggestion['name']!;
                            detectedUrl = suggestion['url']!;
                            suggestions = [];
                          });
                        },
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (detectedName.isNotEmpty && detectedUrl.isNotEmpty) {
                  _addWebsite(detectedName, detectedUrl);
                  Navigator.of(context).pop();
                } else if (inputController.text.isNotEmpty) {
                  // 如果只输入了名称，尝试从建议中找到匹配的网址
                  final suggestion = _getWebsiteSuggestions(
                    inputController.text,
                  ).firstOrNull;
                  if (suggestion != null) {
                    _addWebsite(suggestion['name']!, suggestion['url']!);
                    Navigator.of(context).pop();
                  }
                }
              },
              child: const Text('添加'),
            ),
          ],
        ),
      ),
    );
  }

  // Get website suggestions based on input
  List<Map<String, String>> _getWebsiteSuggestions(String input) {
    if (input.length < 2) return [];

    final websiteDatabase = [
      {'name': 'GitHub', 'url': 'https://github.com'},
      {'name': 'Stack Overflow', 'url': 'https://stackoverflow.com'},
      {'name': 'Reddit', 'url': 'https://reddit.com'},
      {'name': 'Twitter', 'url': 'https://twitter.com'},
      {'name': 'YouTube', 'url': 'https://youtube.com'},
      {'name': 'Google', 'url': 'https://google.com'},
      {'name': 'Wikipedia', 'url': 'https://wikipedia.org'},
      {'name': 'Amazon', 'url': 'https://amazon.com'},
      {'name': 'Netflix', 'url': 'https://netflix.com'},
      {'name': 'Facebook', 'url': 'https://facebook.com'},
      {'name': 'Instagram', 'url': 'https://instagram.com'},
      {'name': 'LinkedIn', 'url': 'https://linkedin.com'},
      {'name': 'Discord', 'url': 'https://discord.com'},
      {'name': 'Twitch', 'url': 'https://twitch.tv'},
      {'name': 'TikTok', 'url': 'https://tiktok.com'},
      // Japanese websites
      {'name': 'Yahoo Japan', 'url': 'https://yahoo.co.jp'},
      {'name': 'Rakuten', 'url': 'https://rakuten.co.jp'},
      {'name': 'Amazon Japan', 'url': 'https://amazon.co.jp'},
      {'name': 'Mercari', 'url': 'https://mercari.com'},
      {'name': 'Cookpad', 'url': 'https://cookpad.com'},
      {'name': 'Hatena', 'url': 'https://hatena.ne.jp'},
      {'name': 'Ameba', 'url': 'https://ameblo.jp'},
      {'name': 'FC2', 'url': 'https://fc2.com'},
      {'name': 'Goo', 'url': 'https://goo.ne.jp'},
      {'name': 'Livedoor', 'url': 'https://livedoor.com'},
    ];

    return websiteDatabase
        .where(
          (site) => site['name']!.toLowerCase().contains(input.toLowerCase()),
        )
        .take(5)
        .toList();
  }

  // Add website to the list
  void _addWebsite(String name, String url) {
    setState(() {
      _websites.add({
        'name': name,
        'url': url,
        'description': '用户添加的网站',
        'category': 'custom',
        'icon': '$url/favicon.ico',
      });
    });

    // Save to local storage
    _saveWebsitesToStorage();

    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('已添加网站: $name')));
  }

  // Save websites to local storage
  Future<void> _saveWebsitesToStorage() async {
    // This would save to SharedPreferences in a real app
    // For now, just keep in memory
  }

  // Check if input is a URL
  bool _isUrl(String input) {
    // 明确的协议前缀
    if (input.startsWith('http://') || input.startsWith('https://')) {
      return true;
    }

    // 包含域名格式的字符串
    if (input.contains('.') && input.length > 4) {
      // 检查是否包含常见的顶级域名
      final commonTlds = [
        '.com',
        '.org',
        '.net',
        '.jp',
        '.cn',
        '.co.jp',
        '.ne.jp',
      ];
      for (final tld in commonTlds) {
        if (input.toLowerCase().contains(tld)) {
          return true;
        }
      }

      // 检查是否是IP地址格式
      final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}');
      if (ipRegex.hasMatch(input)) {
        return true;
      }
    }

    return false;
  }

  // Normalize URL to ensure it has proper protocol
  String _normalizeUrl(String url) {
    String normalized = url.trim();

    // 如果已经有协议，直接返回
    if (normalized.startsWith('http://') || normalized.startsWith('https://')) {
      return normalized;
    }

    // 默认添加https://协议
    return 'https://$normalized';
  }

  // 阶段2: 导航页后续网站预加载
  Future<void> _startStage2Preload() async {
    if (_websites.length <= 12) return; // 没有后续网站

    try {
      final stage2Websites = _websites.skip(12).take(12).toList();

      for (final website in stage2Websites) {
        // 后台预加载，不阻塞UI
        _preloadWebsiteInBackground(website);
        await Future.delayed(const Duration(milliseconds: 300));
      }

      // 启动阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    } catch (e) {
      // 阶段2预加载失败，继续阶段3
      Future.delayed(const Duration(seconds: 3), () {
        _startStage3Preload();
      });
    }
  }

  // 阶段3: 首屏内容深度预加载
  Future<void> _startStage3Preload() async {
    try {
      final stage3Websites = _websites.take(12).toList();

      for (final website in stage3Websites) {
        // 预加载首屏内容（基于1080×1980分辨率）
        _preloadFirstScreenContent(website['url']);
        await Future.delayed(const Duration(milliseconds: 500));
      }
    } catch (e) {
      // 阶段3预加载失败，不影响使用
    }
  }

  // 后台预加载网站
  Future<void> _preloadWebsiteInBackground(Map<String, dynamic> website) async {
    try {
      // 预加载图标
      await _loadCachedIcon(website['icon'] ?? '', website['name']);

      // 预加载主页内容
      await _cacheManager.getCachedPageContent(website['url']);
    } catch (e) {
      // 后台预加载失败，不影响用户体验
    }
  }

  // 预加载首屏内容
  Future<void> _preloadFirstScreenContent(String url) async {
    try {
      // 获取页面内容
      final content = await _cacheManager.getCachedPageContent(url);
      if (content != null) {
        // 分析页面，提取首屏关键资源
        final resources = _extractFirstScreenResources(content, url);

        // 预加载关键资源
        for (final resource in resources.take(5)) {
          // 限制5个资源
          _preloadResource(resource);
        }
      }
    } catch (e) {
      // 首屏预加载失败，不影响使用
    }
  }

  // 提取首屏关键资源
  List<String> _extractFirstScreenResources(String html, String baseUrl) {
    final resources = <String>[];
    final uri = Uri.parse(baseUrl);

    // 简化实现：使用字符串匹配
    try {
      // 查找CSS文件
      final lines = html.split('\n');
      for (final line in lines) {
        if (line.contains('.css') && line.contains('href=')) {
          final start = line.indexOf('href="');
          if (start != -1) {
            final end = line.indexOf('"', start + 6);
            if (end != -1) {
              final href = line.substring(start + 6, end);
              if (href.contains('.css')) {
                final resourceUrl = _resolveUrl(href, uri);
                if (resourceUrl != null) resources.add(resourceUrl);
              }
            }
          }
        }

        // 查找图片文件
        if ((line.contains('.jpg') ||
                line.contains('.png') ||
                line.contains('.gif')) &&
            line.contains('src=')) {
          final start = line.indexOf('src="');
          if (start != -1) {
            final end = line.indexOf('"', start + 5);
            if (end != -1) {
              final src = line.substring(start + 5, end);
              if (src.contains('.jpg') ||
                  src.contains('.png') ||
                  src.contains('.gif')) {
                final resourceUrl = _resolveUrl(src, uri);
                if (resourceUrl != null && resources.length < 5) {
                  resources.add(resourceUrl);
                }
              }
            }
          }
        }
      }
    } catch (e) {
      // 资源提取失败，返回空列表
    }

    return resources;
  }

  // 解析相对URL为绝对URL
  String? _resolveUrl(String href, Uri baseUri) {
    try {
      if (href.startsWith('http')) {
        return href;
      } else if (href.startsWith('//')) {
        return '${baseUri.scheme}:$href';
      } else if (href.startsWith('/')) {
        return '${baseUri.scheme}://${baseUri.host}$href';
      } else {
        return '${baseUri.scheme}://${baseUri.host}${baseUri.path}/$href';
      }
    } catch (e) {
      return null;
    }
  }

  // 预加载资源
  Future<void> _preloadResource(String url) async {
    try {
      final response = await http
          .get(
            Uri.parse(url),
            headers: {
              'User-Agent':
                  'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36',
            },
          )
          .timeout(const Duration(seconds: 3));

      // 简单缓存资源（实际项目中可以使用更复杂的缓存策略）
      if (response.statusCode == 200) {
        // 资源预加载成功
      }
    } catch (e) {
      // 资源预加载失败，不影响使用
    }
  }

  // Reorder websites when dragging
  void _reorderWebsites(int oldIndex, int newIndex) {
    setState(() {
      final website = _websites.removeAt(oldIndex);
      _websites.insert(newIndex, website);
    });
  }

  // Build remove area at bottom
  Widget _buildRemoveArea() {
    return DragTarget<int>(
      onAcceptWithDetails: (details) {
        final draggedIndex = details.data;
        final website = _websites[draggedIndex];

        // 所有网站都可以删除
        _deleteWebsite(website);
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          height: 80,
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isHovering ? Colors.red[400] : Colors.red[300],
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.delete_outline,
                color: Colors.white,
                size: isHovering ? 32 : 28,
              ),
              const SizedBox(width: 8),
              Text(
                '拖动到此处删除',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: isHovering ? 18 : 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Check if website is a default website (cannot be deleted)
  bool _isDefaultWebsite(Map<String, dynamic> website) {
    final defaultWebsiteNames = [
      '小説家になろう',
      'カクヨム',
      'pixiv',
      'ニコニコ動画',
      'Yahoo! JAPAN',
      '楽天',
    ];

    return defaultWebsiteNames.contains(website['name']);
  }

  // Delete website from list
  void _deleteWebsite(Map<String, dynamic> website) {
    setState(() {
      _websites.removeWhere(
        (w) => w['name'] == website['name'] && w['url'] == website['url'],
      );
    });

    // Show confirmation message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已删除 "${website['name']}"'),
        duration: const Duration(seconds: 2),
      ),
    );

    // TODO: Save to SharedPreferences in a real app
  }

  // Extract website name from URL
  String _extractNameFromUrl(String url) {
    try {
      // Ensure URL has protocol
      String fullUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        fullUrl = 'https://$url';
      }

      final uri = Uri.parse(fullUrl);
      String host = uri.host;

      // Remove www. prefix
      if (host.startsWith('www.')) {
        host = host.substring(4);
      }

      // Extract main domain name
      final parts = host.split('.');
      if (parts.isNotEmpty) {
        return parts[0].substring(0, 1).toUpperCase() + parts[0].substring(1);
      }

      return host;
    } catch (e) {
      return url;
    }
  }
}
