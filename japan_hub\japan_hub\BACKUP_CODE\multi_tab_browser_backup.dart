import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'managers/tab_manager.dart';
import 'models/tab_model.dart';
import 'widgets/tab_bar_widget.dart';
import 'widgets/tab_content_widget.dart';

/// 多标签页浏览器
class MultiTabBrowser extends StatefulWidget {
  final String? initialUrl;
  
  const MultiTabBrowser({super.key, this.initialUrl});

  @override
  State<MultiTabBrowser> createState() => _MultiTabBrowserState();
}

class _MultiTabBrowserState extends State<MultiTabBrowser> {
  late TabManager _tabManager;

  @override
  void initState() {
    super.initState();
    _tabManager = TabManager();
    
    // 创建初始标签页
    if (widget.initialUrl != null && widget.initialUrl!.isNotEmpty) {
      _tabManager.createNewTab(url: widget.initialUrl);
    } else {
      _tabManager.createNewTab();
    }
    
    // 监听标签页变化
    _tabManager.addListener(_onTabsChanged);
  }

  @override
  void dispose() {
    _tabManager.removeListener(_onTabsChanged);
    super.dispose();
  }

  void _onTabsChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 标签页栏
            TabBarWidget(
              tabs: _tabManager.tabs,
              currentTabIndex: _tabManager.currentTabIndex,
              onTabSelected: (index) => _tabManager.switchToTab(index),
              onTabClosed: (index) => _closeTab(index),
              onNewTab: () => _createNewTab(),
            ),
            
            // 标签页内容
            Expanded(
              child: _buildTabContent(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签页内容
  Widget _buildTabContent() {
    if (!_tabManager.hasTabs) {
      return _buildEmptyState();
    }

    final currentTab = _tabManager.currentTab;
    if (currentTab == null) {
      return _buildEmptyState();
    }

    return TabContentWidget(
      tab: currentTab,
      onUrlChanged: (url) => _updateTabUrl(currentTab.id, url),
      onTitleChanged: (title) => _updateTabTitle(currentTab.id, title),
      onLoadingChanged: (isLoading) => _updateTabLoading(currentTab.id, isLoading),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[50],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.tab,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '没有打开的标签页',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _createNewTab,
            icon: const Icon(Icons.add),
            label: const Text('新建标签页'),
          ),
        ],
      ),
    );
  }

  /// 创建新标签页
  void _createNewTab() {
    _tabManager.createNewTab();
  }

  /// 关闭标签页
  void _closeTab(int index) {
    _tabManager.closeTab(index);
    
    // 如果没有标签页了，返回上一页
    if (!_tabManager.hasTabs) {
      Navigator.of(context).pop();
    }
  }

  /// 更新标签页URL
  void _updateTabUrl(String tabId, String url) {
    _tabManager.updateTab(tabId, url: url);
  }

  /// 更新标签页标题
  void _updateTabTitle(String tabId, String title) {
    _tabManager.updateTab(tabId, title: title);
  }

  /// 更新标签页加载状态
  void _updateTabLoading(String tabId, bool isLoading) {
    _tabManager.updateTab(tabId, isLoading: isLoading);
  }
}
