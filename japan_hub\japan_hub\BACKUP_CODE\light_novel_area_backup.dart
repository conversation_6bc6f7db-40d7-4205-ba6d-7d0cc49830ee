// 备份：轻小说作品区代码
// 保存时间：2025-07-28 02:30
// 来源：当前账号的home_page.dart优化代码

// 轻小说作品数据
final List<Map<String, dynamic>> _lightNovels = [
  {'title': '转生成为史莱姆', 'author': '伏瀬', 'color': Colors.blue[600]},
  {'title': '关于我转生变成史莱姆这档事', 'author': '伏瀬', 'color': Colors.green[600]},
  {'title': '无职转生', 'author': '理不尽な孫の手', 'color': Colors.orange[600]},
  {'title': '盾之勇者成名录', 'author': 'アネコユサギ', 'color': Colors.purple[600]},
  {'title': 'Re:从零开始的异世界生活', 'author': '長月達平', 'color': Colors.red[600]},
  {'title': '幼女战记', 'author': 'カルロ・ゼン', 'color': Colors.indigo[600]},
  {'title': '魔法科高校的劣等生', 'author': '佐島勤', 'color': Colors.teal[600]},
  {'title': '刀剑神域', 'author': '川原礫', 'color': Colors.cyan[600]},
];

// 构建不规则排序轻小说作品区
Widget _buildLightNovelArea() {
  return LayoutBuilder(
    builder: (context, constraints) {
      return Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
        ),
        child: Stack(
          children: [
            // 不规则排序的轻小说作品
            ..._buildIrregularLightNovels(constraints),
            // "世界的二（不起）次元"标题 - 靠右
            Positioned(
              top: 16,
              right: 24, // 更靠右一些
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Text(
                  '世界的二（不起）次元',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}

// 生成不规则排序的轻小说作品
List<Widget> _buildIrregularLightNovels(BoxConstraints constraints) {
  final List<Widget> widgets = [];
  
  // 预定义的不规则位置（相对于容器的百分比）
  final List<Map<String, double>> positions = [
    {'left': 0.05, 'top': 0.15},
    {'left': 0.25, 'top': 0.05},
    {'left': 0.45, 'top': 0.25},
    {'left': 0.65, 'top': 0.1},
    {'left': 0.15, 'top': 0.45},
  ];

  // 只显示前5个轻小说作品，颜色不超过5种
  final displayNovels = _lightNovels.take(5).toList();

  for (int i = 0; i < displayNovels.length && i < positions.length; i++) {
    final novel = displayNovels[i];
    final position = positions[i];
    
    widgets.add(
      Positioned(
        left: position['left']! * constraints.maxWidth,
        top: position['top']! * constraints.maxHeight,
        child: _buildLightNovelChip(novel),
      ),
    );
  }
  
  return widgets;
}

// 构建单个轻小说作品标签
Widget _buildLightNovelChip(Map<String, dynamic> novel) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: novel['color'] ?? Colors.blue[600],
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 2,
          offset: const Offset(0, 1),
        ),
      ],
    ),
    child: Text(
      novel['title'] ?? '',
      style: const TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.w500,
      ),
    ),
  );
}

// 在主内容布局中的使用方式：
/*
Widget _buildMainContent() {
  return Container(
    padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 网站导航区域
        Expanded(
          flex: 2,
          child: Stack(
            children: [
              // 主要的网格视图
              GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                  childAspectRatio: 0.9,
                ),
                itemCount: _websites.length + 1, // +1 for add button
                itemBuilder: (context, index) {
                  if (index == _websites.length) {
                    return _buildAddWebsiteCard();
                  }
                  final website = _websites[index];
                  return DragTarget<int>(
                    onAcceptWithDetails: (details) {
                      final draggedIndex = details.data;
                      if (draggedIndex != index) {
                        _reorderWebsites(draggedIndex, index);
                      }
                    },
                    builder: (context, candidateData, rejectedData) {
                      return _buildWebsiteCard(website, index);
                    },
                  );
                },
              ),
              // 底部移除区域
              if (_isDragMode)
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: _buildRemoveArea(),
                ),
            ],
          ),
        ),
        
        const SizedBox(height: 16),
        
        // 不规则排序轻小说作品区
        Expanded(flex: 1, child: _buildLightNovelArea()),
      ],
    ),
  );
}
*/
