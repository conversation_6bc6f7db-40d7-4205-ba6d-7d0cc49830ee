<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:focusableInTouchMode="true"
    tools:context="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
    android:focusable="true">

    <com.pichillilorenzo.flutter_inappwebview_android.pull_to_refresh.PullToRefreshLayout
        android:id="@+id/pullToRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.pichillilorenzo.flutter_inappwebview_android.webview.in_app_webview.InAppWebView
            android:id="@+id/webView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </com.pichillilorenzo.flutter_inappwebview_android.pull_to_refresh.PullToRefreshLayout>

    <ProgressBar
        android:id="@+id/progressBar"
        style="@android:style/Widget.Holo.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:max="100"
        android:progress="0" />

</RelativeLayout>

