import 'package:flutter/foundation.dart';
import '../models/tab_model.dart';

/// 标签页管理器
class TabManager extends ChangeNotifier {
  static final TabManager _instance = TabManager._internal();
  factory TabManager() => _instance;
  TabManager._internal();

  final List<TabModel> _tabs = [];
  int _currentTabIndex = -1;

  /// 获取所有标签页
  List<TabModel> get tabs => List.unmodifiable(_tabs);

  /// 获取当前标签页索引
  int get currentTabIndex => _currentTabIndex;

  /// 获取当前标签页
  TabModel? get currentTab {
    if (_currentTabIndex >= 0 && _currentTabIndex < _tabs.length) {
      return _tabs[_currentTabIndex];
    }
    return null;
  }

  /// 获取标签页数量
  int get tabCount => _tabs.length;

  /// 是否有标签页
  bool get hasTabs => _tabs.isNotEmpty;

  /// 创建新标签页
  TabModel createNewTab({String? url, String? title}) {
    final tab = TabModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? '新建页签',
      url: url ?? '',
    );
    
    _tabs.add(tab);
    _currentTabIndex = _tabs.length - 1;
    
    debugPrint('📑 创建新标签页: ${tab.id}, 总数: ${_tabs.length}');
    notifyListeners();
    
    return tab;
  }

  /// 切换到指定标签页
  void switchToTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      _currentTabIndex = index;
      debugPrint('📑 切换到标签页: $index');
      notifyListeners();
    }
  }

  /// 切换到指定ID的标签页
  void switchToTabById(String id) {
    final index = _tabs.indexWhere((tab) => tab.id == id);
    if (index != -1) {
      switchToTab(index);
    }
  }

  /// 关闭标签页
  void closeTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      final tab = _tabs.removeAt(index);
      debugPrint('📑 关闭标签页: ${tab.id}, 剩余: ${_tabs.length}');
      
      // 调整当前标签页索引
      if (_tabs.isEmpty) {
        _currentTabIndex = -1;
      } else if (index <= _currentTabIndex) {
        _currentTabIndex = (_currentTabIndex - 1).clamp(0, _tabs.length - 1);
      }
      
      notifyListeners();
    }
  }

  /// 根据ID关闭标签页
  void closeTabById(String id) {
    final index = _tabs.indexWhere((tab) => tab.id == id);
    if (index != -1) {
      closeTab(index);
    }
  }

  /// 关闭所有标签页
  void closeAllTabs() {
    _tabs.clear();
    _currentTabIndex = -1;
    debugPrint('📑 关闭所有标签页');
    notifyListeners();
  }

  /// 更新标签页信息
  void updateTab(String id, {
    String? title,
    String? url,
    String? favicon,
    bool? isLoading,
  }) {
    final tab = _tabs.firstWhere((tab) => tab.id == id, orElse: () => TabModel(id: ''));
    if (tab.id.isNotEmpty) {
      tab.updateInfo(
        title: title,
        url: url,
        favicon: favicon,
        isLoading: isLoading,
      );
      notifyListeners();
    }
  }

  /// 根据ID获取标签页
  TabModel? getTabById(String id) {
    try {
      return _tabs.firstWhere((tab) => tab.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取标签页索引
  int getTabIndex(String id) {
    return _tabs.indexWhere((tab) => tab.id == id);
  }
}
