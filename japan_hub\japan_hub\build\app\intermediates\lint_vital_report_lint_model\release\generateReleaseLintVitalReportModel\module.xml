<lint-module
    format="1"
    dir="D:\lk\japan_hub\japan_hub\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="D:\lk\japan_hub\japan_hub\build\app"
    bootClassPath="D:\Scoop\apps\android-clt\current\platforms\android-35\android.jar;D:\Scoop\apps\android-clt\current\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
