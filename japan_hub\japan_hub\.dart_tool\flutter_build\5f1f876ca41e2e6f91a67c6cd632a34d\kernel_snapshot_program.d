D:\\lk\\japan_hub\\japan_hub\\.dart_tool\\flutter_build\\5f1f876ca41e2e6f91a67c6cd632a34d\\app.dill: D:\\lk\\japan_hub\\japan_hub\\lib\\main.dart D:\\lk\\japan_hub\\japan_hub\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\material.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\services.dart D:\\lk\\japan_hub\\japan_hub\\lib\\splash_page.dart D:\\lk\\japan_hub\\japan_hub\\lib\\browser_app.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\flutter_inappwebview_android.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\shared_preferences_android.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\lib\\url_launcher_android.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\flutter_inappwebview_ios.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\shared_preferences_foundation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\lib\\url_launcher_ios.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\lib\\shared_preferences_linux.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\url_launcher_linux.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\flutter_inappwebview_macos.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\url_launcher_macos.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\flutter_inappwebview_windows.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\lib\\shared_preferences_windows.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\lib\\url_launcher_windows.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\carousel.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\predictive_back_page_transitions_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\slider_value_indicator_shape.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\widgets.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\flutter_version.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\predictive_back_event.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\scribe.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\http.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\shared_preferences.dart D:\\lk\\japan_hub\\japan_hub\\lib\\cache_manager.dart D:\\lk\\japan_hub\\japan_hub\\lib\\tab_manager.dart D:\\lk\\japan_hub\\japan_hub\\lib\\home_page.dart D:\\lk\\japan_hub\\japan_hub\\lib\\webview_page.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_android.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\shared_preferences_async_android.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\foundation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\link.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\url_launcher_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_async_foundation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\shared_preferences_foundation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\local.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\path.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_async_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\shared_preferences_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\types.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\cupertino.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\scheduler.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\rendering.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\animation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\gestures.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\collection.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\material_color_utilities.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\painting.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\characters.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_boundary.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\expansible.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\feedback.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\flutter_logo.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pinned_header_sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_menu_anchor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_floating_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_resizing_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_tree.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\standard_component_type.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\system_context_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\toggleable.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_preview.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_state.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\client.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\exception.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_client.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\base_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\byte_stream.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\streamed_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_async.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_legacy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\crypto.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\flutter_inappwebview.dart D:\\lk\\japan_hub\\japan_hub\\lib\\proxy_manager.dart D:\\lk\\japan_hub\\japan_hub\\lib\\google_translate_service.dart D:\\lk\\japan_hub\\japan_hub\\lib\\shared_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\inappwebview_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\chrome_safari_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\pull_to_refresh\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\service_worker_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\webview_feature.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\proxy_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\webview_asset_loader.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\tracing_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\process_global_config.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\strings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\lib\\src\\messages_async.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\types.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\url_launcher_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\inappwebview_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\chrome_safari_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\pull_to_refresh\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_authentication_session\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\lib\\src\\messages.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\context.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_exception.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\path_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\lib\\method_channel_shared_preferences.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\inappwebview_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_authentication_session\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\inappwebview_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\webview_environment\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\ffi.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sheet.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\semantics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_tree.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\algorithms.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\boollist.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\canonicalized_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterable.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_list.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\comparators.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\equality_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\functions.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_extensions.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\iterable_zip.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\list_extensions.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\priority_queue.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\queue_list.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\union_set_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\unmodifiable_wrappers.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\wrappers.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\blend\\blend.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\contrast\\contrast.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dislike\\dislike_analyzer.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_color.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\dynamic_scheme.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\material_dynamic_colors.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\variant.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\cam16.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\hct.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\viewing_conditions.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\core_palette.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\palettes\\tonal_palette.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_celebi.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wsmeans.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\quantizer_wu.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_content.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_expressive.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fidelity.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_fruit_salad.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_monochrome.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_neutral.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_rainbow.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_tonal_spot.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\scheme\\scheme_vibrant.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\score\\score.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\temperature\\temperature_cache.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\color_utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\math_utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\utils\\string_utils.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\extensions.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\_web_image_info_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\_web_image_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\physics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\meta-1.16.0\\lib\\meta_meta.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_client.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\http_parser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\multipart_file_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\boundary_characters.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\lib\\src\\shared_preferences_devtools_extension_data.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hmac.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\md5.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha1.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha256.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\flutter_inappwebview_platform_interface.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\chrome_safari_browser\\chrome_safari_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_browser\\in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_webview\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_webview\\in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_webview\\headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_storage\\web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_storage\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\pull_to_refresh\\pull_to_refresh_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_message\\web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_message\\web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\web_message\\web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\print_job\\print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\find_interaction\\find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\method_channel_url_launcher.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\chrome_safari_browser\\chrome_safari_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_browser\\in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_webview\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_webview\\in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_webview\\headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_storage\\web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_storage\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\platform_util.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\pull_to_refresh\\pull_to_refresh_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_message\\web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_message\\web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_message\\web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\print_job\\print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\find_interaction\\find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\web_authentication_session\\web_authenticate_session.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_directory.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_file_system_entity.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_link.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\forwarding\\forwarding_random_access_file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\directory.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\file_system_entity.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\link.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\characters.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\internal_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\parsed_path.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\posix.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\url.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\style\\windows.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_browser\\in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_webview\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_webview\\in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_webview\\headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_storage\\web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_storage\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\platform_util.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_message\\web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_message\\web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_message\\web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\print_job\\print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\find_interaction\\find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\web_authentication_session\\web_authenticate_session.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_browser\\in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\webview_environment\\webview_environment.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_storage\\web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_storage\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_message\\web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_message\\web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\web_message\\web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\print_job\\print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\find_interaction\\find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\allocation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\arena.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf16.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\lib\\src\\utf8.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\combined_wrappers\\combined_iterator.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\lib\\src\\empty_unmodifiable_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\contrast_curve.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\dynamiccolor\\src\\tone_delta_pair.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\hct\\src\\hct_solver.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider_lab.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\lib\\quantize\\src\\point_provider.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\characters_impl.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\lib\\src\\io_streamed_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\authentication_challenge.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\case_insensitive_map.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\http_date.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\media_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\digest_sink.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\hash_sink.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\lib\\src\\sha512_fastsinks.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\chrome_safari_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\pull_to_refresh\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_authentication_session\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\service_worker_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\proxy_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\webview_asset_loader.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\tracing_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\process_global_config.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_localhost_server.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\webview_environment\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\lib\\src\\in_app_webview\\_static_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\lib\\src\\in_app_webview\\_static_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\interface\\error_codes_dart_io.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_directory.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_link.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\lib\\src\\in_app_webview\\_static_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\custom_platform_view.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\lib\\src\\in_app_webview\\_static_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\breaks.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\constants.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\lib\\src\\grapheme_clusters\\table.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\string_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\scan.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\decoder.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\encoder.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_data.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\inappwebview_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\webview_environment\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_storage\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_cookie_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\content_blocker.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_http_auth_credentials_database.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\pull_to_refresh\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_authentication_session\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\print_job\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\find_interaction\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_uri.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\debug_logging_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\util.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_service_worker_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_webview_feature.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_proxy_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_webview_asset_loader.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_tracing_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_process_global_config.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_in_app_localhost_server.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_localhost_server.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\android\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\apple\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\find_interaction\\find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_browser\\in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\chrome_safari_browser\\chrome_safari_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\android\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\ios\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\webview_environment\\webview_environment.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\pull_to_refresh\\pull_to_refresh_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_message\\web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_message\\web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_message\\web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_authentication_session\\web_authenticate_session.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\print_job\\print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\common.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\lib\\src\\backends\\local\\local_file_system_entity.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\exception.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\line_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\span_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\string_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\source_span.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\lib\\src\\chunked_coding\\charcodes.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_queue.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\typed_buffers.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\platform_chrome_safari_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\find_interaction\\platform_find_interaction_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\platform_in_app_browser.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\platform_headless_in_app_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\platform_inappwebview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\platform_inappwebview_widget.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\print_job\\platform_print_job_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\pull_to_refresh\\platform_pull_to_refresh_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_authentication_session\\platform_web_authenticate_session.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\platform_web_message_channel.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\platform_web_message_listener.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\platform_web_message_port.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_storage\\platform_web_storage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_storage\\platform_web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\webview_environment\\platform_webview_environment.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\action_mode_menu_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_event.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_event_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_headers.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_ready_state.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\attributed_string.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\attributed_string_text_effect_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cache_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\call_async_javascript_result.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_challenge.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\compress_format.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\console_message.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\console_message_level.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_action_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_load_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_resource_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_world.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cookie.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\create_window_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cross_origin.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\css_link_html_tag_attributes.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_scheme_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_share_state.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\data_detector_types.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\dismiss_button_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\download_start_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\favicon.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_credential.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_credential_default.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_federated_credential.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_password_credential.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\force_dark.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\force_dark_strategy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\form_resubmission_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\frame_info.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\geolocation_permission_show_prompt_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_auth_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_auth_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_authentication_challenge.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_cookie_same_site_policy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_hit_test_result.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_hit_test_result_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_initial_data.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_rect.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\javascript_handler_callback.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\layout_algorithm.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\layout_in_display_cutout_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\loaded_resource.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\login_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\media_capture_state.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\media_playback_state.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\meta_tag.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\meta_tag_attribute.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\mixed_content_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\modal_presentation_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\modal_transition_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_action_policy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\on_post_message_callback.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\over_scroll_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\pdf_configuration.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_resource_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_attributes.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_color_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_duplex_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_info.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_media_size.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_orientation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_output_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_rendering_quality.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_resolution.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_state.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\proxy_rule.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\proxy_scheme_filter.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\pull_to_refresh_size.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\referrer_policy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\render_process_gone_detail.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\renderer_priority.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\renderer_priority_policy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\request_focus_node_href_result.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\request_image_ref_result.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_threat.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\sandbox.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\screenshot_configuration.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\script_html_tag_attributes.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollbar_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollview_content_inset_adjustment_behavior.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollview_deceleration_rate.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\security_origin.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\selection_granularity.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_auth_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_auth_response_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_challenge.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\should_allow_deprecated_tls_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_certificate.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_certificate_dname.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_error.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_error_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_default_display_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_display_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_immersive_display_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_screen_orientation.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\underline_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_authentication_challenge.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_credential.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_credential_persistence.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_authentication_method.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_http_auth_credentials.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_proxy_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_attribution.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_cache_policy.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_network_service_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_preferred_content_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_script.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_script_injection_time.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\vertical_scrollbar_position.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_archive_format.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_authentication_session_error.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_history.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_history_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_message_callback.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_error.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_error_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_request.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_response.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_storage_origin.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_storage_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\website_data_record.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\website_data_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\webview_package_info.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\webview_render_process_action.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_features.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\find_session.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\search_result_display_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_load_context.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_page_order.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_pagination_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_disposition.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\printer.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_style_mask.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_titlebar_separator_style.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_navigation_event_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_relation_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\prewarming_token.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\android_resource.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ui_image.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\activity_button.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ui_event_attribution.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\tracing_mode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\tracing_category.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_post_message_result_type.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_scheme_registration.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\disposable.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\webview_environment\\webview_environment_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\platform_webview.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\in_app_webview_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\in_app_webview_keep_alive.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\android\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\apple\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\in_app_browser_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\android\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\apple\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\in_app_browser_menu_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\android\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\apple\\main.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_action_button.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_menu_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_secondary_toolbar.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\asn1_decoder.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\asn1_distinguished_names.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\asn1_identifier.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\asn1_object.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\oid.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\key_usage.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\x509_certificate.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\x509_extension.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\x509_public_key.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\x509_certificate\\asn1_der_encoder.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_storage\\web_storage_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu_item.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\pull_to_refresh\\pull_to_refresh_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\web_message.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_authentication_session\\web_authenticate_session_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\print_job\\print_job_settings.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\flutter_inappwebview_internal_annotations.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_webview_feature.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_proxy_controller.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_webview_asset_loader.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_tracing_controller.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\platform_process_global_config.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\mime_type_resolver.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\android\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\in_app_webview\\apple\\in_app_webview_controller.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\android\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\src\\web_storage\\ios\\web_storage_manager.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\charcode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\eager_span_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\lib\\src\\relative_span_scanner.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\file.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\location_mixin.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_exception.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_mixin.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\span_with_context.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\lib\\src\\typed_buffer.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\action_mode_menu_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_event.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_event_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_headers.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ajax_request_ready_state.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\attributed_string.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\attributed_string_text_effect_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cache_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\call_async_javascript_result.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_challenge.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\client_cert_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\compress_format.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\console_message.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\console_message_level.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_action_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_load_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_resource_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cookie.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\create_window_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\cross_origin.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\css_link_html_tag_attributes.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_scheme_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_share_state.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\data_detector_types.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\dismiss_button_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\download_start_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\favicon.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_credential.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_credential_default.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_federated_credential.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\fetch_request_password_credential.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\force_dark.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\force_dark_strategy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\form_resubmission_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\frame_info.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\geolocation_permission_show_prompt_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_auth_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_auth_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_authentication_challenge.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\http_cookie_same_site_policy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_hit_test_result.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_hit_test_result_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_initial_data.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\in_app_webview_rect.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_alert_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_before_unload_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_confirm_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\js_prompt_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\layout_algorithm.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\layout_in_display_cutout_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\loaded_resource.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\login_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\media_capture_state.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\media_playback_state.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\meta_tag.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\meta_tag_attribute.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\mixed_content_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\modal_presentation_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\modal_transition_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_action_policy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\navigation_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\over_scroll_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\pdf_configuration.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_resource_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\permission_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_attributes.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_color_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_duplex_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_info.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_media_size.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_orientation.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_output_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_rendering_quality.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_resolution.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_state.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\proxy_rule.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\proxy_scheme_filter.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\pull_to_refresh_size.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\referrer_policy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\render_process_gone_detail.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\renderer_priority.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\renderer_priority_policy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\request_focus_node_href_result.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\request_image_ref_result.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\safe_browsing_threat.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\sandbox.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\screenshot_configuration.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\script_html_tag_attributes.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollbar_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollview_content_inset_adjustment_behavior.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\scrollview_deceleration_rate.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\security_origin.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\selection_granularity.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_auth_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_auth_response_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\server_trust_challenge.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\should_allow_deprecated_tls_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_certificate.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_certificate_dname.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_error.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ssl_error_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_default_display_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_display_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_immersive_display_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\trusted_web_activity_screen_orientation.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\underline_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_authentication_challenge.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_credential.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_credential_persistence.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_authentication_method.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_http_auth_credentials.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_protection_space_proxy_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_attribution.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_cache_policy.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_request_network_service_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\url_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_preferred_content_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_script.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\user_script_injection_time.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\vertical_scrollbar_position.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_archive_format.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_authentication_session_error.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_history.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_history_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_error.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_error_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_request.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_resource_response.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_storage_origin.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\web_storage_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\website_data_record.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\website_data_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\webview_package_info.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\webview_render_process_action.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_features.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\find_session.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\search_result_display_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\content_blocker_trigger_load_context.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_page_order.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_pagination_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\print_job_disposition.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\printer.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_style_mask.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\window_titlebar_separator_style.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_navigation_event_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_relation_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\prewarming_token.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\android_resource.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ui_image.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\activity_button.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\ui_event_attribution.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\tracing_mode.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\tracing_category.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_tabs_post_message_result_type.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\types\\custom_scheme_registration.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\webview_environment\\webview_environment_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\android\\in_app_webview_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\apple\\in_app_webview_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_webview\\in_app_webview_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\android\\in_app_browser_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\apple\\in_app_browser_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\in_app_browser_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\in_app_browser\\in_app_browser_menu_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\android\\chrome_custom_tabs_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\apple\\safari_options.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_action_button.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_menu_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\chrome_safari_browser\\chrome_safari_browser_secondary_toolbar.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_storage\\web_storage_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu_item.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\context_menu\\context_menu_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\pull_to_refresh\\pull_to_refresh_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_message\\web_message.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\web_authentication_session\\web_authenticate_session_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\lib\\src\\print_job\\print_job_settings.g.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_object.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_object_constructor.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_object_property.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_object_method.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_enum.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\exchangeable_enum_custom_value.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\supported_platforms.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\lib\\src\\enum_supported_platforms.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\term_glyph.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\highlighter.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\utils.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\ascii_glyph_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\glyph_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\unicode_glyph_set.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\lib\\src\\generated\\top_level.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\charcode.dart D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\lib\\src\\colors.dart
