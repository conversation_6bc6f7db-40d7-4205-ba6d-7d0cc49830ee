# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 44ms
  [gap of 29ms]
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 36ms
  [gap of 24ms]
generate_cxx_metadata completed in 77ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 53ms
  [gap of 28ms]
generate_cxx_metadata completed in 104ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 33ms]
  create-invalidation-state 61ms
  [gap of 22ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 127ms

