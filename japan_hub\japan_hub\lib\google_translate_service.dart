import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class GoogleTranslateService {
  static final GoogleTranslateService _instance = GoogleTranslateService._internal();
  factory GoogleTranslateService() => _instance;
  GoogleTranslateService._internal();

  // 谷歌翻译API配置
  static const String _baseUrl = 'https://translate.googleapis.com/translate_a/single';
  static const String _userAgent = 'Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36';
  
  // 缓存翻译结果
  final Map<String, String> _translationCache = {};
  
  // 支持的语言
  static const Map<String, String> supportedLanguages = {
    'ja': '日语',
    'zh': '中文',
    'en': '英语',
    'ko': '韩语',
    'auto': '自动检测',
  };

  // 翻译文本
  Future<Map<String, dynamic>> translateText(
    String text, {
    String from = 'auto',
    String to = 'zh',
  }) async {
    if (text.trim().isEmpty) {
      return {
        'success': false,
        'error': '文本为空',
      };
    }

    // 检查缓存
    final cacheKey = '${from}_${to}_${text.hashCode}';
    if (_translationCache.containsKey(cacheKey)) {
      return {
        'success': true,
        'translatedText': _translationCache[cacheKey],
        'sourceLanguage': from,
        'targetLanguage': to,
        'fromCache': true,
      };
    }

    try {
      // 构建请求URL
      final url = Uri.parse(_baseUrl).replace(queryParameters: {
        'client': 'gtx',
        'sl': from,
        'tl': to,
        'dt': 't',
        'q': text,
      });

      // 发送请求
      final response = await http.get(
        url,
        headers: {
          'User-Agent': _userAgent,
          'Accept': 'application/json',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        },
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        // 解析响应
        final result = await _parseTranslationResponse(response.body);
        
        if (result['success']) {
          // 缓存翻译结果
          _translationCache[cacheKey] = result['translatedText'];
          await _saveCacheToStorage();
          
          return {
            'success': true,
            'translatedText': result['translatedText'],
            'sourceLanguage': result['sourceLanguage'] ?? from,
            'targetLanguage': to,
            'fromCache': false,
          };
        } else {
          return result;
        }
      } else {
        return {
          'success': false,
          'error': 'HTTP错误: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': '翻译请求失败: $e',
      };
    }
  }

  // 解析翻译响应
  Future<Map<String, dynamic>> _parseTranslationResponse(String responseBody) async {
    try {
      // 谷歌翻译返回的是一个特殊格式的JSON数组
      final decoded = json.decode(responseBody);
      
      if (decoded is List && decoded.isNotEmpty) {
        final translations = decoded[0];
        if (translations is List && translations.isNotEmpty) {
          final translatedText = translations
              .map((item) => item is List && item.isNotEmpty ? item[0] : '')
              .where((text) => text.isNotEmpty)
              .join('');
          
          // 检测源语言
          String? sourceLanguage;
          if (decoded.length > 2 && decoded[2] is String) {
            sourceLanguage = decoded[2];
          }
          
          return {
            'success': true,
            'translatedText': translatedText,
            'sourceLanguage': sourceLanguage,
          };
        }
      }
      
      return {
        'success': false,
        'error': '无法解析翻译结果',
      };
    } catch (e) {
      return {
        'success': false,
        'error': '解析响应失败: $e',
      };
    }
  }

  // 批量翻译
  Future<Map<String, dynamic>> translateBatch(
    List<String> texts, {
    String from = 'auto',
    String to = 'zh',
  }) async {
    final results = <String>[];
    final errors = <String>[];
    
    for (final text in texts) {
      final result = await translateText(text, from: from, to: to);
      
      if (result['success']) {
        results.add(result['translatedText']);
      } else {
        results.add(text); // 翻译失败时保持原文
        errors.add(result['error']);
      }
      
      // 避免请求过于频繁
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return {
      'success': errors.isEmpty,
      'translatedTexts': results,
      'errors': errors,
      'sourceLanguage': from,
      'targetLanguage': to,
    };
  }

  // 检测语言
  Future<Map<String, dynamic>> detectLanguage(String text) async {
    if (text.trim().isEmpty) {
      return {
        'success': false,
        'error': '文本为空',
      };
    }

    try {
      final url = Uri.parse(_baseUrl).replace(queryParameters: {
        'client': 'gtx',
        'sl': 'auto',
        'tl': 'en',
        'dt': 't',
        'q': text.substring(0, text.length > 100 ? 100 : text.length),
      });

      final response = await http.get(
        url,
        headers: {
          'User-Agent': _userAgent,
          'Accept': 'application/json',
        },
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        final decoded = json.decode(response.body);
        
        if (decoded is List && decoded.length > 2 && decoded[2] is String) {
          final detectedLanguage = decoded[2];
          final languageName = supportedLanguages[detectedLanguage] ?? '未知语言';
          
          return {
            'success': true,
            'language': detectedLanguage,
            'languageName': languageName,
            'confidence': 0.9, // 简化处理
          };
        }
      }
      
      return {
        'success': false,
        'error': '无法检测语言',
      };
    } catch (e) {
      return {
        'success': false,
        'error': '语言检测失败: $e',
      };
    }
  }

  // 翻译网页内容
  Future<Map<String, dynamic>> translateWebContent(
    String htmlContent, {
    String from = 'auto',
    String to = 'zh',
  }) async {
    try {
      // 提取文本内容（简化处理）
      final textContent = _extractTextFromHtml(htmlContent);
      
      if (textContent.isEmpty) {
        return {
          'success': false,
          'error': '没有找到可翻译的文本',
        };
      }
      
      // 分段翻译
      final segments = _splitTextIntoSegments(textContent);
      final translatedSegments = <String>[];
      
      for (final segment in segments) {
        final result = await translateText(segment, from: from, to: to);
        
        if (result['success']) {
          translatedSegments.add(result['translatedText']);
        } else {
          translatedSegments.add(segment); // 翻译失败时保持原文
        }
        
        // 避免请求过于频繁
        await Future.delayed(const Duration(milliseconds: 200));
      }
      
      return {
        'success': true,
        'originalText': textContent,
        'translatedText': translatedSegments.join('\n'),
        'sourceLanguage': from,
        'targetLanguage': to,
      };
    } catch (e) {
      return {
        'success': false,
        'error': '网页翻译失败: $e',
      };
    }
  }

  // 从HTML中提取文本（简化实现）
  String _extractTextFromHtml(String html) {
    // 移除HTML标签
    final textOnly = html
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false, dotAll: true), '')
        .replaceAll(RegExp(r'<style[^>]*>.*?</style>', caseSensitive: false, dotAll: true), '')
        .replaceAll(RegExp(r'<[^>]*>'), ' ')
        .replaceAll(RegExp(r'\s+'), ' ')
        .trim();
    
    return textOnly;
  }

  // 将文本分段
  List<String> _splitTextIntoSegments(String text, {int maxLength = 500}) {
    final segments = <String>[];
    final sentences = text.split(RegExp(r'[.!?。！？]'));
    
    String currentSegment = '';
    
    for (final sentence in sentences) {
      if (currentSegment.length + sentence.length > maxLength) {
        if (currentSegment.isNotEmpty) {
          segments.add(currentSegment.trim());
          currentSegment = sentence;
        } else {
          segments.add(sentence.trim());
        }
      } else {
        currentSegment += sentence;
      }
    }
    
    if (currentSegment.isNotEmpty) {
      segments.add(currentSegment.trim());
    }
    
    return segments.where((s) => s.isNotEmpty).toList();
  }

  // 保存缓存到本地存储
  Future<void> _saveCacheToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheJson = json.encode(_translationCache);
      await prefs.setString('translation_cache', cacheJson);
    } catch (e) {
      print('保存翻译缓存失败: $e');
    }
  }

  // 从本地存储加载缓存
  Future<void> loadCacheFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheJson = prefs.getString('translation_cache');
      
      if (cacheJson != null) {
        final cacheMap = json.decode(cacheJson) as Map<String, dynamic>;
        _translationCache.clear();
        _translationCache.addAll(cacheMap.cast<String, String>());
      }
    } catch (e) {
      print('加载翻译缓存失败: $e');
    }
  }

  // 清除缓存
  Future<void> clearCache() async {
    _translationCache.clear();
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('translation_cache');
  }

  // 获取缓存统计
  Map<String, dynamic> getCacheStats() {
    return {
      'cacheSize': _translationCache.length,
      'supportedLanguages': supportedLanguages,
    };
  }
}
