#!/usr/bin/env python3
"""
创建NACG翻译君的N字logo图标
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """创建N字logo图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制蓝色圆角背景
    corner_radius = size // 5  # 20% 圆角
    
    # 绘制圆角矩形背景
    draw.rounded_rectangle(
        [(0, 0), (size, size)],
        radius=corner_radius,
        fill=(33, 150, 243, 255)  # #2196F3 蓝色
    )
    
    # 绘制白色N字
    try:
        # 尝试使用系统字体
        font_size = int(size * 0.6)  # 60% 大小
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("Arial.ttf", font_size)
        except:
            # 使用默认字体
            font = ImageFont.load_default()
    
    # 获取文字尺寸
    text = "N"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # 计算居中位置（向上调整更多）
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - int(size * 0.15)  # 向上调整15%
    
    # 绘制白色N字
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存图像
    img.save(output_path, 'PNG')
    print(f"创建图标: {output_path} ({size}x{size})")

def create_foreground_icon(size, output_path):
    """创建前景图标（用于adaptive icon）"""
    # 创建透明背景图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制白色N字（稍小一些）
    try:
        font_size = int(size * 0.5)  # 50% 大小
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("Arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
    
    # 获取文字尺寸
    text = "N"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    # 计算居中位置（向上调整更多）
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - int(size * 0.15)  # 向上调整15%
    
    # 绘制白色N字
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 确保目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 保存图像
    img.save(output_path, 'PNG')
    print(f"创建前景图标: {output_path} ({size}x{size})")

if __name__ == "__main__":
    # 创建主图标
    create_icon(512, "assets/icon/app_icon.png")
    
    # 创建前景图标
    create_foreground_icon(512, "assets/icon/app_icon_foreground.png")
    
    print("所有图标创建完成！")
