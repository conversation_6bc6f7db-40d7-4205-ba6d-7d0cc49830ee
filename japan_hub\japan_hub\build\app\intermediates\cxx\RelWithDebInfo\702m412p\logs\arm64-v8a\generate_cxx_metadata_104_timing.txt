# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 15ms
  execute-generate-process
    [gap of 11ms]
    exec-configure 12733ms
    [gap of 68ms]
  execute-generate-process completed in 12812ms
  [gap of 55ms]
generate_cxx_metadata completed in 12938ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 22ms]
  create-invalidation-state 59ms
  [gap of 19ms]
generate_cxx_metadata completed in 100ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 37ms
  [gap of 24ms]
generate_cxx_metadata completed in 77ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 40ms
  [gap of 28ms]
generate_cxx_metadata completed in 87ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 23ms]
  create-invalidation-state 62ms
  [gap of 23ms]
  write-metadata-json-to-file 12ms
generate_cxx_metadata completed in 121ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 29ms]
  create-invalidation-state 80ms
  [gap of 29ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 154ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 40ms
  [gap of 24ms]
generate_cxx_metadata completed in 88ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 56ms
  [gap of 33ms]
generate_cxx_metadata completed in 117ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 60ms
  [gap of 33ms]
generate_cxx_metadata completed in 121ms

