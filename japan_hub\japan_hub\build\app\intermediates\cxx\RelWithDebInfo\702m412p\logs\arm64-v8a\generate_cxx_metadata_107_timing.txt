# C/C++ build system timings
generate_cxx_metadata
  [gap of 18ms]
  create-invalidation-state 49ms
  [gap of 22ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 101ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 31ms]
  create-invalidation-state 65ms
  [gap of 33ms]
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 21ms]
  create-invalidation-state 47ms
  [gap of 31ms]
generate_cxx_metadata completed in 99ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 60ms
  [gap of 36ms]
generate_cxx_metadata completed in 120ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 38ms
  [gap of 37ms]
generate_cxx_metadata completed in 94ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 45ms
  [gap of 29ms]
generate_cxx_metadata completed in 94ms

