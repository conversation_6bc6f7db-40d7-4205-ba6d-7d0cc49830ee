                        -HD:\Scoop\persist\puro\data\envs\stable\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=D:\Scoop\apps\android-clt\current\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=D:\Scoop\apps\android-clt\current\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=D:\Scoop\apps\android-clt\current\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\Scoop\apps\android-clt\current\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\lk\japan_hub\japan_hub\build\app\intermediates\cxx\RelWithDebInfo\702m412p\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\lk\japan_hub\japan_hub\build\app\intermediates\cxx\RelWithDebInfo\702m412p\obj\x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\lk\japan_hub\japan_hub\build\.cxx\RelWithDebInfo\702m412p\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2