{"inputs": ["D:\\lk\\japan_hub\\japan_hub\\.dart_tool\\flutter_build\\5f1f876ca41e2e6f91a67c6cd632a34d\\app.dill", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\bin\\cache\\engine.stamp", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\bin\\cache\\engine.stamp", "D:\\lk\\japan_hub\\japan_hub\\pubspec.yaml", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\assets\\t_rex_runner\\t-rex.html", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\lib\\assets\\t_rex_runner\\t-rex.css", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_web-1.1.2\\lib\\assets\\web\\web_support.js", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "D:\\lk\\japan_hub\\japan_hub\\.dart_tool\\flutter_build\\5f1f876ca41e2e6f91a67c6cd632a34d\\native_assets.json", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\bin\\cache\\pkg\\sky_engine\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\envs\\stable\\flutter\\packages\\flutter\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\async-2.13.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\boolean_selector-2.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\characters-1.4.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\clock-1.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\collection-1.19.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\fake_async-1.3.3\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\ffi-2.1.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview-6.1.5\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_android-1.1.3\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_internal_annotations-1.2.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_ios-1.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_macos-1.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_platform_interface-1.3.0+1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_web-1.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_inappwebview_windows-0.6.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http-1.4.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\leak_tracker-10.0.9\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.9\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\matcher-0.12.17\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\meta-1.16.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path-1.9.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences-2.5.3\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_web-2.4.3\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\source_span-1.10.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\stack_trace-1.12.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\stream_channel-2.1.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\string_scanner-1.4.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\term_glyph-1.2.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\test_api-0.7.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher-6.3.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\vm_service-15.0.0\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\web-1.1.1\\LICENSE", "D:\\Scoop\\persist\\puro\\data\\shared\\pub_cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE"], "outputs": ["D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\cupertino_icons\\assets\\CupertinoIcons.ttf", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\flutter_inappwebview\\assets\\t_rex_runner\\t-rex.html", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\flutter_inappwebview\\assets\\t_rex_runner\\t-rex.css", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\packages\\flutter_inappwebview_web\\assets\\web\\web_support.js", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts\\MaterialIcons-Regular.otf", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders\\ink_sparkle.frag", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "D:\\lk\\japan_hub\\japan_hub\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NativeAssetsManifest.json"]}