<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2010 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at
  
          http://www.apache.org/licenses/LICENSE-2.0
  
     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:orientation="vertical"
              style="@style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem">
    <TextView android:id="@+id/action_bar_title"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:singleLine="true"
              android:ellipsize="end" />
    <TextView android:id="@+id/action_bar_subtitle"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_marginTop="@dimen/abc_action_bar_subtitle_top_margin_material"
              android:singleLine="true"
              android:ellipsize="end"
              android:visibility="gone" />
</LinearLayout>
