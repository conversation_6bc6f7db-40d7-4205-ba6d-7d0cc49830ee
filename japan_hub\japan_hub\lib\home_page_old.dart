import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'webview_page.dart';
import 'cache_manager.dart';
import 'proxy_webview.dart';
import 'debug_webview.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  bool _isSidebarExpanded = false; // 桌面端侧边栏展开状态
  late AnimationController _sidebarController;
  late Animation<double> _sidebarAnimation;

  // 底部导航状态
  int _currentPageIndex = 0; // 0:导航, 1:工具, 2:我的

  // 工具状态
  bool _isNetworkAccelerated = true; // 网络加速状态 - 默认开启
  bool _isAITranslateEnabled = true; // AI翻译状态 - 默认开启

  // 用户信息
  String _userName = 'JapanHub用户';
  String _userId = 'JH001';
  bool _isVIP = true; // VIP状态

  final TextEditingController _searchController = TextEditingController();

  // 网站分类数据
  final List<Map<String, dynamic>> _categories = [
    {'name': '小说', 'icon': Icons.book, 'color': Colors.blue},
    {'name': '漫画', 'icon': Icons.auto_stories, 'color': Colors.orange},
    {'name': '插画', 'icon': Icons.palette, 'color': Colors.purple},
    {'name': '视频', 'icon': Icons.play_circle, 'color': Colors.red},
  ];

  // 推荐网站数据
  final List<Map<String, dynamic>> _recommendedSites = [
    {
      'name': '小説家になろう',
      'description': '日本最大的网络小说投稿网站',
      'url': 'https://syosetu.com',
      'category': '小说',
      'status': 'online',
      'icon': 'https://syosetu.com/favicon.ico',
      'color': Colors.blue,
    },
    {
      'name': 'pixiv',
      'description': '插画、漫画、小说创作社区',
      'url': 'https://www.pixiv.net',
      'category': '创作',
      'status': 'online',
      'icon': 'https://www.pixiv.net/favicon.ico',
      'color': Colors.blue,
    },
    {
      'name': 'カクヨム',
      'description': 'KADOKAWA旗下小说投稿平台',
      'url': 'https://kakuyomu.jp',
      'category': '小说',
      'status': 'online',
      'icon': 'https://kakuyomu.jp/favicon.ico',
      'color': Colors.orange,
    },
    {
      'name': 'ニコニコ動画',
      'description': '日本知名弹幕视频网站',
      'url': 'https://www.nicovideo.jp',
      'category': '视频',
      'status': 'online',
      'icon': 'https://www.nicovideo.jp/favicon.ico',
      'color': Colors.black,
    },
    {
      'name': 'Yahoo! JAPAN',
      'description': '日本雅虎门户网站',
      'url': 'https://www.yahoo.co.jp',
      'category': '门户',
      'status': 'online',
      'icon': 'https://www.yahoo.co.jp/favicon.ico',
      'color': Colors.purple,
    },
    {
      'name': 'アニメ新番組',
      'description': '新番动画信息网站',
      'url': 'https://anime-newtype.net',
      'category': '动画',
      'status': 'online',
      'icon': 'https://anime-newtype.net/favicon.ico',
      'color': Colors.red,
    },
  ];

  @override
  void initState() {
    super.initState();
    _sidebarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    // 根据屏幕宽度动态设置侧边栏宽度
    final screenWidth = MediaQuery.of(context).size.width;
    final sidebarWidth = screenWidth < 600
        ? screenWidth *
              0.25 // 移动端：屏幕宽度的25%
        : 200.0; // 桌面端：固定200px
    _sidebarAnimation = Tween<double>(begin: 60.0, end: sidebarWidth).animate(
      CurvedAnimation(parent: _sidebarController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _sidebarController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _toggleSidebar() {
    setState(() {
      _isSidebarExpanded = !_isSidebarExpanded;
      if (_isSidebarExpanded) {
        _sidebarController.forward();
      } else {
        _sidebarController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Column(
        children: [
          // 页眉 - 只在首页显示
          if (_currentPageIndex == 0) _buildHeader(),
          // 主体内容
          Expanded(
            child: SafeArea(
              top: _currentPageIndex != 0, // 非首页需要顶部安全区域
              child: _buildBody(),
            ),
          ),
        ],
      ),
    );
  }

  // 页眉
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 40, 16, 8), // 顶部增加状态栏高度
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 应用标题
          const Expanded(
            child: Text(
              'Japan Hub',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          // 用户头像和ID区域
          GestureDetector(
            onTap: _showUserMenu,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.blue[200]!, width: 1),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircleAvatar(
                    radius: 14,
                    backgroundColor: Colors.blue[100],
                    child: Icon(
                      Icons.person,
                      size: 16,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            _userId,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.blue[700],
                            ),
                          ),
                          if (_isVIP) ...[
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                                vertical: 1,
                              ),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFD700),
                                    Color(0xFFFFA500),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Text(
                                'VIP',
                                style: TextStyle(
                                  fontSize: 8,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(width: 4),
                  Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                    color: Colors.blue[700],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 主体内容
  Widget _buildBody() {
    return SafeArea(
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 在小屏幕上使用Stack布局，大屏幕使用Row布局
          if (constraints.maxWidth < 600) {
            // 移动端：使用Column布局，固定底部导航栏
            return Column(
              children: [
                // 主内容区域 - 根据当前页面显示不同内容
                Expanded(child: _buildCurrentPage()),
                // 固定底部导航栏
                Container(
                  height: 70,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      top: BorderSide(color: Colors.grey[200]!, width: 1),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: _buildMobileBottomNavigation(),
                ),
              ],
            );
          } else {
            // 桌面端：使用Row布局
            return Row(
              children: [
                // 主内容区域
                Expanded(child: _buildMainContent()),
                // 右侧侧边栏
                _buildSidebar(),
              ],
            );
          }
        },
      ),
    );
  }

  // 显示用户菜单
  void _showUserMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 用户信息
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Colors.blue[100],
                    child: Icon(
                      Icons.person,
                      size: 24,
                      color: Colors.blue[700],
                    ),
                  ),
                  const SizedBox(width: 16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _userName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          if (_isVIP) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFD700),
                                    Color(0xFFFFA500),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Text(
                                'VIP',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Text(
                        'ID: $_userId',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Divider(),
              // 菜单选项
              ListTile(
                leading: const Icon(Icons.settings),
                title: const Text('用户设置'),
                onTap: () {
                  Navigator.pop(context);
                  // 打开用户设置
                },
              ),
              ListTile(
                leading: const Icon(Icons.swap_horiz),
                title: const Text('更换账号'),
                onTap: () {
                  Navigator.pop(context);
                  // 更换账号功能
                },
              ),
              ListTile(
                leading: const Icon(Icons.logout, color: Colors.red),
                title: const Text('退出账号', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.pop(context);
                  // 退出账号功能
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 根据当前页面索引显示不同内容
  Widget _buildCurrentPage() {
    switch (_currentPageIndex) {
      case 0:
        return _buildNavigationPage(); // 导航页面
      case 1:
        return _buildToolsPage(); // 工具页面
      case 2:
        return _buildProfilePage(); // 我的页面
      default:
        return _buildNavigationPage();
    }
  }

  // 导航页面（原主页内容）
  Widget _buildNavigationPage() {
    return _buildMainContent();
  }

  // 工具页面
  Widget _buildToolsPage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '工具箱',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildToolCard(
                        icon: Icons.speed,
                        title: '网络加速',
                        description: _isNetworkAccelerated ? '已启用' : '点击启用',
                        color: Colors.blue,
                        isActive: _isNetworkAccelerated,
                        onTap: () {
                          setState(() {
                            _isNetworkAccelerated = !_isNetworkAccelerated;
                          });
                        },
                ),
                _buildToolCard(
                  icon: Icons.translate,
                  title: 'AI翻译',
                  description: _isAITranslateEnabled ? '已启用' : '点击启用',
                  color: Colors.green,
                  isActive: _isAITranslateEnabled,
                  onTap: () {
                    setState(() {
                      _isAITranslateEnabled = !_isAITranslateEnabled;
                    });
                  },
                ),
                _buildToolCard(
                  icon: Icons.bookmark,
                  title: '书签管理',
                  description: '管理收藏网站',
                  color: Colors.orange,
                  onTap: () {
                    // 书签管理
                  },
                ),
                _buildToolCard(
                  icon: Icons.history,
                  title: '浏览历史',
                  description: '查看访问记录',
                  color: Colors.purple,
                  onTap: () {
                    // 浏览历史
                  },
                ),
              ],
            ),
          ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 我的页面
  Widget _buildProfilePage() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 用户信息卡片
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue[200]!, width: 1),
            ),
            child: Column(
              children: [
                CircleAvatar(
                        radius: 40,
                        backgroundColor: Colors.blue[100],
                        child: Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.blue[700],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _userName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          if (_isVIP) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 3,
                              ),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFFD700),
                                    Color(0xFFFFA500),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'VIP',
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ID: $_userId',
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                // 功能列表
                Expanded(
                  child: ListView(
                    children: [
                      _buildProfileItem(
                        icon: Icons.settings,
                        title: '设置',
                        onTap: () {},
                      ),
                      _buildProfileItem(
                        icon: Icons.help,
                        title: '帮助与反馈',
                        onTap: () {},
                      ),
                      _buildProfileItem(
                        icon: Icons.info,
                        title: '关于',
                        onTap: () {},
                      ),
                      _buildProfileItem(
                        icon: Icons.logout,
                        title: '退出账号',
                        onTap: () {},
                        isDestructive: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 主内容区域
  Widget _buildMainContent() {
    return GestureDetector(
      onTap: () {
        // 移动端使用固定底部导航栏，无需处理侧边栏
      },
      child: _buildNavigationGrid(),
    );
  }

  // 网站导航网格
  Widget _buildNavigationGrid() {
    return Padding(
      padding: EdgeInsets.all(
        MediaQuery.of(context).size.width > 600 ? 20.0 : 12.0,
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 根据屏幕宽度动态调整列数
          int crossAxisCount;
          double childAspectRatio;

          if (constraints.maxWidth > 1200) {
            // 大屏幕 (桌面)
            crossAxisCount = 6;
            childAspectRatio = 1.0;
          } else if (constraints.maxWidth > 800) {
            // 中等屏幕 (平板)
            crossAxisCount = 5;
            childAspectRatio = 1.0;
          } else if (constraints.maxWidth > 600) {
            // 小平板/大手机
            crossAxisCount = 4;
            childAspectRatio = 1.0;
          } else if (constraints.maxWidth > 400) {
            // 普通手机屏幕
            crossAxisCount = 3;
            childAspectRatio = 1.0; // 缩小每列，显示更多内容
          } else {
            // 小屏手机
            crossAxisCount = 3;
            childAspectRatio = 0.9; // 更紧凑的布局
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: childAspectRatio,
              crossAxisSpacing: constraints.maxWidth > 600 ? 20 : 12,
              mainAxisSpacing: constraints.maxWidth > 600 ? 20 : 12,
            ),
            itemCount: _recommendedSites.length + 1, // +1 for add button
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildAddSiteCard();
              } else {
                final site = _recommendedSites[index - 1];
                return _buildSiteCard(site, index - 1);
              }
            },
          );
        },
      ),
    );
  }

  // 添加网站卡片
  Widget _buildAddSiteCard() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showAddSiteDialog();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.blue[200]!,
            width: 2,
            strokeAlign: BorderSide.strokeAlignInside,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.blue.withValues(alpha: 0.1),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.blue[300]!, width: 2),
                ),
                child: Icon(
                  Icons.add_rounded,
                  color: Colors.blue[700],
                  size: 24,
                ),
              ),
              const SizedBox(height: 8),
              Flexible(
                child: Text(
                  '添加网站',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                    letterSpacing: 0.1,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 2),
              Flexible(
                child: Text(
                  '点击添加',
                  style: TextStyle(fontSize: 10, color: Colors.blue[500]),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示添加网站对话框
  void _showAddSiteDialog() {
    final TextEditingController urlController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('添加新网站'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: urlController,
                decoration: const InputDecoration(
                  labelText: '网站地址或名称',
                  hintText: '输入网址或搜索网站名称',
                  helperText: '例如：syosetu.com 或 小説家になろう',
                ),
                onChanged: (value) {
                  // 实时搜索和自动关联网址
                  _searchAndSuggestWebsite(value);
                },
              ),
              const SizedBox(height: 16),
              // 搜索建议列表
              SizedBox(height: 100, child: _buildSuggestionsList()),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                _addWebsite(urlController.text);
                Navigator.of(context).pop();
              },
              child: const Text('添加'),
            ),
          ],
        );
      },
    );
  }

  // 搜索和建议网站
  void _searchAndSuggestWebsite(String query) {
    // 这里可以实现网站搜索和自动关联逻辑
    // 例如：根据输入的关键词匹配预设的网站数据库
    setState(() {
      // 更新建议列表
    });
  }

  // 建议列表
  Widget _buildSuggestionsList() {
    // 预设的热门网站建议
    final suggestions = [
      {'name': '小説家になろう', 'url': 'https://syosetu.com'},
      {'name': 'pixiv', 'url': 'https://www.pixiv.net'},
      {'name': 'カクヨム', 'url': 'https://kakuyomu.jp'},
    ];

    return ListView.builder(
      itemCount: suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return ListTile(
          dense: true,
          leading: const Icon(Icons.language, size: 16),
          title: Text(
            suggestion['name']!,
            style: const TextStyle(fontSize: 14),
          ),
          subtitle: Text(
            suggestion['url']!,
            style: const TextStyle(fontSize: 12),
          ),
          onTap: () {
            _addWebsite(suggestion['url']!, suggestion['name']!);
            Navigator.of(context).pop();
          },
        );
      },
    );
  }

  // 添加网站
  void _addWebsite(String url, [String? name]) {
    // 处理URL，确保格式正确
    String finalUrl = _convertToMobileUrl(url);
    String siteName = name ?? _extractSiteName(url);

    setState(() {
      _recommendedSites.add({
        'name': siteName,
        'url': finalUrl,
        'status': 'online',
      });
    });
  }

  // 转换为手机版URL
  String _convertToMobileUrl(String url) {
    // 确保URL有协议前缀
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    // 移除协议前缀进行处理
    String cleanUrl = url.replaceAll(RegExp(r'^https?://'), '');
    cleanUrl = cleanUrl.replaceAll(RegExp(r'^www\.'), '');

    // 根据不同网站转换为手机版URL
    if (cleanUrl.contains('syosetu.com')) {
      return 'https://ncode.syosetu.com'; // 小説家になろう手机版
    } else if (cleanUrl.contains('pixiv.net')) {
      return 'https://www.pixiv.net'; // pixiv已经是响应式，但使用移动端User-Agent
    } else if (cleanUrl.contains('kakuyomu.jp')) {
      return 'https://kakuyomu.jp'; // カクヨム已经是响应式
    } else if (cleanUrl.contains('nicovideo.jp')) {
      return 'https://sp.nicovideo.jp'; // ニコニコ動画手机版
    } else if (cleanUrl.contains('yahoo.co.jp')) {
      return 'https://m.yahoo.co.jp'; // Yahoo! JAPAN手机版
    } else {
      // 默认返回原URL，依靠移动端User-Agent显示手机版
      return url;
    }
  }

  // 从URL提取网站名称
  String _extractSiteName(String url) {
    url = url.replaceAll(RegExp(r'^https?://'), '');
    url = url.replaceAll(RegExp(r'^www\.'), '');

    if (url.contains('syosetu.com')) {
      return '小説家になろう';
    } else if (url.contains('pixiv.net')) {
      return 'pixiv';
    } else if (url.contains('kakuyomu.jp')) {
      return 'カクヨム';
    } else {
      // 提取域名作为名称
      return url.split('/')[0].split('.')[0];
    }
  }

  // 网站卡片
  Widget _buildSiteCard(Map<String, dynamic> site, int index) {
    return GestureDetector(
      onTap: () {
        // 打开对应手机版网页
        HapticFeedback.lightImpact();
        _openWebsite(site['url']);
      },
      onLongPress: () {
        // 长按显示选项菜单
        HapticFeedback.mediumImpact();
        _showSiteOptionsDialog(site, index);
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.08),
              spreadRadius: 0,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: Colors.grey.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 网站图标
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: (site['color'] as Color).withValues(alpha: 0.2),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                  border: Border.all(
                    color: (site['color'] as Color).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(9),
                  child: site['icon'] != null
                      ? Image.network(
                          site['icon'],
                          width: 24,
                          height: 24,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.language,
                              color: site['color'] as Color,
                              size: 24,
                            );
                          },
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Icon(
                              Icons.language,
                              color: site['color'] as Color,
                              size: 24,
                            );
                          },
                        )
                      : Icon(
                          Icons.language,
                          color: site['color'] as Color,
                          size: 24,
                        ),
                ),
              ),
              const SizedBox(height: 8),
              // 网站名称 - 使用Flexible防止溢出
              Flexible(
                child: Text(
                  site['name'],
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                    letterSpacing: 0.1,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 4),
              // 网站状态指示器
              Container(
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: site['status'] == 'online'
                      ? Colors.green
                      : Colors.grey,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 显示网站选项对话框
  void _showSiteOptionsDialog(Map<String, dynamic> site, int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(site['name']),
          content: Text('选择操作：'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openWithDebug(site['url'], site['name']);
              },
              child: const Text('调试模式'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showDeleteConfirmDialog(index);
              },
              child: const Text('删除'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  // 显示删除确认对话框
  void _showDeleteConfirmDialog(int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除"${_recommendedSites[index]['name']}"吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _recommendedSites.removeAt(index);
                });
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  // 移动端固定底部导航栏
  Widget _buildMobileBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 导航按钮
          _buildBottomNavButton(
            icon: Icons.home,
            label: '导航',
            isActive: _currentPageIndex == 0,
            onTap: () {
              setState(() {
                _currentPageIndex = 0;
              });
            },
          ),
          // 工具按钮
          _buildBottomNavButton(
            icon: Icons.build,
            label: '工具',
            isActive: _currentPageIndex == 1,
            onTap: () {
              setState(() {
                _currentPageIndex = 1;
              });
            },
          ),
          // 我的按钮
          _buildBottomNavButton(
            icon: Icons.person,
            label: '我的',
            isActive: _currentPageIndex == 2,
            onTap: () {
              setState(() {
                _currentPageIndex = 2;
              });
            },
          ),
        ],
      ),
    );
  }

  // 工具卡片
  Widget _buildToolCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isActive
              ? color.withValues(alpha: 0.2)
              : color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isActive ? color : color.withValues(alpha: 0.3),
            width: isActive ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              children: [
                Icon(icon, size: 32, color: color),
                if (isActive)
                  Positioned(
                    right: -4,
                    top: -4,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: const Icon(
                        Icons.check,
                        size: 10,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 个人页面列表项
  Widget _buildProfileItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(icon, color: isDestructive ? Colors.red : Colors.grey[600]),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : Colors.black87,
          fontWeight: FontWeight.w500,
        ),
      ),
      trailing: Icon(Icons.chevron_right, color: Colors.grey[400]),
      onTap: onTap,
    );
  }

  // 底部导航按钮
  Widget _buildBottomNavButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
    bool badge = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              children: [
                Icon(
                  icon,
                  size: 24,
                  color: isActive ? Colors.blue : Colors.grey[600],
                ),
                if (badge)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: const BoxDecoration(
                        color: Colors.green,
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w500,
                color: isActive ? Colors.blue : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 右侧侧边栏
  Widget _buildSidebar() {
    return AnimatedBuilder(
      animation: _sidebarAnimation,
      builder: (context, child) {
        return Container(
          width: _sidebarAnimation.value,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                spreadRadius: 1,
                blurRadius: 5,
                offset: const Offset(-2, 0),
              ),
            ],
          ),
          child: Column(
            children: [
              // 侧边栏头部 - 始终可见的功能
              _buildSidebarHeader(),
              // 侧边栏内容 - 展开后可见
              if (_isSidebarExpanded) ...[
                const Divider(height: 1),
                _buildSidebarContent(),
              ],
            ],
          ),
        );
      },
    );
  }

  // 侧边栏头部
  Widget _buildSidebarHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          // 导航按钮
          _buildSidebarButton(
            icon: Icons.home,
            label: '导航',
            isActive: true,
            onTap: _toggleSidebar,
          ),
          const SizedBox(height: 12),
          // 翻译按钮
          _buildSidebarButton(
            icon: Icons.translate,
            label: '翻译',
            isActive: false,
            onTap: _toggleSidebar,
          ),
          const SizedBox(height: 12),
          // 优化按钮
          _buildSidebarButton(
            icon: Icons.speed,
            label: '优化',
            isActive: false,
            onTap: _toggleSidebar,
            badge: '已连接',
          ),
        ],
      ),
    );
  }

  // 侧边栏按钮
  Widget _buildSidebarButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
    String? badge,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: isActive ? Colors.blue[100] : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            Center(
              child: Icon(
                icon,
                color: isActive ? Colors.blue : Colors.grey[600],
                size: 20,
              ),
            ),
            if (badge != null && _isSidebarExpanded)
              Positioned(
                left: 50,
                top: -8,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    badge,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            if (_isSidebarExpanded)
              Positioned(
                left: 50,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Text(
                    label,
                    style: TextStyle(
                      color: isActive ? Colors.blue : Colors.grey[700],
                      fontSize: 14,
                      fontWeight: isActive
                          ? FontWeight.w600
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 侧边栏展开内容
  Widget _buildSidebarContent() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 用户信息区域
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  // 用户头像
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.blue[100],
                    child: const Icon(
                      Icons.person,
                      size: 30,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // 用户ID
                  const Text(
                    'user_12345',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 4),
                  // VIP状态
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.orange[100],
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'VIP会员',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  // 设置按钮
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {},
                      icon: const Icon(Icons.settings, size: 16),
                      label: const Text('设置'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: Colors.grey[700],
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
    );
  }

  // 打开网站
  void _openWebsite(String url) {
    // 确保URL格式正确
    String finalUrl = url;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = 'https://$url';
    }

    // 转换为手机版URL
    String mobileUrl = _convertToMobileUrl(finalUrl);

    // 启动智能预加载
    _startSmartPreload(finalUrl);

    // 获取网站名称作为标题
    String title = _extractSiteName(url);

    // 使用手机版URL打开WebView
    _openWithWebView(mobileUrl, title);
  }

  // 启动智能预加载
  void _startSmartPreload(String url) async {
    try {
      final cacheManager = CacheManager();

      // 根据网站类型确定分类
      String category = 'general';
      if (url.contains('syosetu.com') || url.contains('kakuyomu.jp')) {
        category = '小说';
      } else if (url.contains('pixiv.net')) {
        category = '创作';
      } else if (url.contains('nicovideo.jp')) {
        category = '视频';
      } else if (url.contains('yahoo.co.jp')) {
        category = '门户';
      }

      // 异步执行逐级预加载，不阻塞页面打开
      cacheManager.progressivePreload(url, category);
    } catch (e) {
      // 预加载失败不影响正常使用
    }
  }

  // 显示打开选项对话框
  void _showOpenOptionsDialog(String url, String title) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('打开 $title'),
          content: const Text('选择打开方式：'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openWithWebView(url, title);
              },
              child: const Text('内嵌浏览'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openWithProxy(url, title);
              },
              child: const Text('代理模式'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _openWithAdvancedProxy(url, title);
              },
              child: const Text('高级代理'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  // 普通WebView打开
  void _openWithWebView(String url, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(initialUrl: url, title: title),
      ),
    );
  }

  // 使用代理打开
  void _openWithProxy(String url, String title) {
    // 使用公共CORS代理
    final proxyUrl = 'https://cors-anywhere.herokuapp.com/$url';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            WebViewPage(initialUrl: proxyUrl, title: '$title (代理模式)'),
      ),
    );
  }

  // 使用高级代理打开
  void _openWithAdvancedProxy(String url, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProxyWebViewPage(originalUrl: url, title: title),
      ),
    );
  }

  // 使用调试模式打开
  void _openWithDebug(String url, String title) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DebugWebViewPage(url: url, title: title),
      ),
    );
  }
}
