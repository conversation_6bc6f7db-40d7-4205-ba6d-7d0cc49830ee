# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 44ms
  [gap of 21ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 42ms
  [gap of 27ms]
generate_cxx_metadata completed in 89ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 44ms
  [gap of 25ms]
  write-metadata-json-to-file 15ms
generate_cxx_metadata completed in 103ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 28ms]
  create-invalidation-state 49ms
  [gap of 32ms]
generate_cxx_metadata completed in 109ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 27ms]
  create-invalidation-state 51ms
  [gap of 25ms]
  write-metadata-json-to-file 14ms
generate_cxx_metadata completed in 118ms

