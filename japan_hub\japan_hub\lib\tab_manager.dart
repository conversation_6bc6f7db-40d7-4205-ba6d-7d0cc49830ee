import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

// 标签页数据类
class TabData {
  final String id;
  String title;
  String? url;
  bool isHomePage;
  InAppWebViewController? webViewController;
  bool isLoaded; // 页面是否已加载
  DateTime lastUpdate; // 最后更新时间
  bool hasError; // 是否有错误

  TabData({
    required this.id,
    required this.title,
    this.url,
    this.isHomePage = false,
    this.webViewController,
    this.isLoaded = false,
    DateTime? lastUpdate,
    this.hasError = false,
  }) : lastUpdate = lastUpdate ?? DateTime.now();
}

// 标签页管理器
class TabManager extends ChangeNotifier {
  static final TabManager _instance = TabManager._internal();
  factory TabManager() => _instance;
  TabManager._internal();

  List<TabData> _tabs = [];
  int _currentIndex = 0;

  List<TabData> get tabs => _tabs;
  int get currentIndex => _currentIndex;
  TabData? get currentTab => _tabs.isNotEmpty ? _tabs[_currentIndex] : null;
  int get tabCount => _tabs.length;

  // 初始化，添加首屏标签
  void initialize() {
    if (_tabs.isEmpty) {
      _tabs.add(TabData(id: 'home', title: 'Nacg翻译君', isHomePage: true));
      notifyListeners();
    }
  }

  // 添加新标签页
  void addTab({required String url, required String title}) {
    final newTab = TabData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      url: url,
      isHomePage: false,
    );

    _tabs.add(newTab);
    _currentIndex = _tabs.length - 1; // 切换到新标签页

    notifyListeners();
  }

  // 切换标签页
  void switchToTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      _currentIndex = index;
      notifyListeners();
    }
  }

  // 关闭标签页
  void closeTab(int index) {
    if (index >= 0 && index < _tabs.length && _tabs.length > 1) {
      _tabs.removeAt(index);

      // 调整当前索引
      if (_currentIndex >= _tabs.length) {
        _currentIndex = _tabs.length - 1;
      } else if (_currentIndex > index) {
        _currentIndex--;
      }

      notifyListeners();
    }
  }

  // 更新标签页标题 - 使用dynamic避免类型推断问题
  void updateTabTitle(String tabId, dynamic title) {
    try {
      final String? titleStr = title?.toString();
      if (titleStr == null || titleStr.isEmpty) {
        debugPrint('🔧 TabManager: 标题为空，跳过更新');
        return;
      }

      final tab = _tabs.firstWhere(
        (t) => t.id == tabId,
        orElse: () => TabData(id: '', title: ''),
      );
      if (tab.id.isNotEmpty) {
        tab.title = titleStr;
        notifyListeners();
        debugPrint('🔧 TabManager: 更新标题成功 "$titleStr"');
      } else {
        debugPrint('🔧 TabManager: 找不到标签页 $tabId');
      }
    } catch (e) {
      debugPrint('🔧 TabManager: 更新标题失败 $e');
    }
  }

  // 更新标签页URL - 使用dynamic避免类型推断问题
  void updateTabUrl(String tabId, dynamic url) {
    try {
      final String? urlStr = url?.toString();
      if (urlStr == null || urlStr.isEmpty || urlStr == 'about:blank') {
        debugPrint('🔧 TabManager: URL无效，跳过更新: $urlStr');
        return;
      }

      final tab = _tabs.firstWhere(
        (t) => t.id == tabId,
        orElse: () => TabData(id: '', title: ''),
      );
      if (tab.id.isNotEmpty) {
        tab.url = urlStr;
        notifyListeners();
        debugPrint('🔧 TabManager: 更新URL成功 "$urlStr"');
      } else {
        debugPrint('🔧 TabManager: 找不到标签页 $tabId');
      }
    } catch (e) {
      debugPrint('🔧 TabManager: 更新URL失败 $e');
    }
  }

  // 获取指定标签页
  TabData? getTab(String tabId) {
    try {
      return _tabs.firstWhere((t) => t.id == tabId);
    } catch (e) {
      debugPrint('🔧 TabManager: 找不到标签页 $tabId');
      return null;
    }
  }

  // 更新标签页状态
  void updateTabState(
    String tabId, {
    bool? isLoaded,
    bool? hasError,
    String? url,
    String? title,
  }) {
    try {
      final tab = getTab(tabId);
      if (tab != null) {
        if (isLoaded != null) tab.isLoaded = isLoaded;
        if (hasError != null) tab.hasError = hasError;
        if (url != null && url.isNotEmpty && url != 'about:blank')
          tab.url = url;
        if (title != null && title.isNotEmpty) tab.title = title;
        tab.lastUpdate = DateTime.now();
        notifyListeners();
        debugPrint('🔧 TabManager: 更新状态成功 $tabId');
      }
    } catch (e) {
      debugPrint('🔧 TabManager: 更新状态失败 $e');
    }
  }

  // 批量更新标签页信息
  void batchUpdateTab(
    String tabId, {
    dynamic title,
    dynamic url,
    bool? isLoaded,
    bool? hasError,
  }) {
    try {
      final tab = getTab(tabId);
      if (tab == null) {
        debugPrint('🔧 TabManager: 批量更新失败，找不到标签页 $tabId');
        return;
      }

      bool updated = false;

      // 更新标题
      if (title != null) {
        final String? titleStr = title.toString();
        if (titleStr != null && titleStr.isNotEmpty) {
          tab.title = titleStr;
          updated = true;
          debugPrint('🔧 TabManager: 批量更新标题 "$titleStr"');
        }
      }

      // 更新URL
      if (url != null) {
        final String? urlStr = url.toString();
        if (urlStr != null && urlStr.isNotEmpty && urlStr != 'about:blank') {
          tab.url = urlStr;
          updated = true;
          debugPrint('🔧 TabManager: 批量更新URL "$urlStr"');
        }
      }

      // 更新状态
      if (isLoaded != null) {
        tab.isLoaded = isLoaded;
        updated = true;
      }
      if (hasError != null) {
        tab.hasError = hasError;
        updated = true;
      }

      if (updated) {
        tab.lastUpdate = DateTime.now();
        notifyListeners();
        debugPrint('🔧 TabManager: 批量更新完成 $tabId');
      }
    } catch (e) {
      debugPrint('🔧 TabManager: 批量更新失败 $e');
    }
  }

  // 更新标签页的WebView控制器
  void updateTabWebViewController(
    String tabId,
    InAppWebViewController controller,
  ) {
    final tab = _tabs.firstWhere(
      (t) => t.id == tabId,
      orElse: () => TabData(id: '', title: ''),
    );
    if (tab.id.isNotEmpty) {
      tab.webViewController = controller;
      notifyListeners();
    }
  }
}
