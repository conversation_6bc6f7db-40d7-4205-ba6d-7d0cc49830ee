import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

// 标签页数据类
class TabData {
  final String id;
  String title;
  String? url;
  bool isHomePage;
  InAppWebViewController? webViewController;

  TabData({
    required this.id,
    required this.title,
    this.url,
    this.isHomePage = false,
    this.webViewController,
  });
}

// 标签页管理器
class TabManager extends ChangeNotifier {
  static final TabManager _instance = TabManager._internal();
  factory TabManager() => _instance;
  TabManager._internal();

  List<TabData> _tabs = [];
  int _currentIndex = 0;

  List<TabData> get tabs => _tabs;
  int get currentIndex => _currentIndex;
  TabData? get currentTab => _tabs.isNotEmpty ? _tabs[_currentIndex] : null;
  int get tabCount => _tabs.length;

  // 初始化，添加首屏标签
  void initialize() {
    if (_tabs.isEmpty) {
      _tabs.add(TabData(id: 'home', title: 'Nacg翻译君', isHomePage: true));
      notifyListeners();
    }
  }

  // 添加新标签页
  void addTab({required String url, required String title}) {
    final newTab = TabData(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      url: url,
      isHomePage: false,
    );

    _tabs.add(newTab);
    _currentIndex = _tabs.length - 1; // 切换到新标签页

    notifyListeners();
  }

  // 切换标签页
  void switchToTab(int index) {
    if (index >= 0 && index < _tabs.length) {
      _currentIndex = index;
      notifyListeners();
    }
  }

  // 关闭标签页
  void closeTab(int index) {
    if (index >= 0 && index < _tabs.length && _tabs.length > 1) {
      _tabs.removeAt(index);

      // 调整当前索引
      if (_currentIndex >= _tabs.length) {
        _currentIndex = _tabs.length - 1;
      } else if (_currentIndex > index) {
        _currentIndex--;
      }

      notifyListeners();
    }
  }

  // 更新标签页标题
  void updateTabTitle(String tabId, String? title) {
    if (title == null || title.isEmpty) return;

    final tab = _tabs.firstWhere(
      (t) => t.id == tabId,
      orElse: () => TabData(id: '', title: ''),
    );
    if (tab.id.isNotEmpty) {
      tab.title = title;
      notifyListeners();
    }
  }

  // 更新标签页URL
  void updateTabUrl(String tabId, String? url) {
    if (url == null || url.isEmpty || url == 'about:blank') return;

    final tab = _tabs.firstWhere(
      (t) => t.id == tabId,
      orElse: () => TabData(id: '', title: ''),
    );
    if (tab.id.isNotEmpty) {
      tab.url = url;
      notifyListeners();
    }
  }

  // 更新标签页的WebView控制器
  void updateTabWebViewController(
    String tabId,
    InAppWebViewController controller,
  ) {
    final tab = _tabs.firstWhere(
      (t) => t.id == tabId,
      orElse: () => TabData(id: '', title: ''),
    );
    if (tab.id.isNotEmpty) {
      tab.webViewController = controller;
      notifyListeners();
    }
  }
}
