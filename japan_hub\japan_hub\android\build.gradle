allprojects {
    repositories {
        // 使用阿里云镜像
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://maven.aliyun.com/repository/public' }
        
        // 备用镜像
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        
        // 原始仓库作为最后备选
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
